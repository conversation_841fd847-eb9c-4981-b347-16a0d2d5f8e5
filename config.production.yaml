# Production Environment Configuration
# This file contains production-specific settings

# Database settings (use PostgreSQL in production)
database_url: "${DATABASE_URL}"  # Set via environment variable
vector_db_path: "/var/lib/semantic_fs/vector_db"

# Logging settings
log_level: "INFO"
enable_file_logging: true
enable_json_logging: true  # Better for log aggregation
enable_log_rotation: true
max_log_file_size: 10485760  # 10MB
log_backup_count: 10

# Security settings (strict for production)
auth_enabled: true
require_auth_for_read: true
require_auth_for_write: true
rate_limit_enabled: true
rate_limit_requests: 100
rate_limit_window: 60
security_headers_enabled: true

# API settings
api_host: "0.0.0.0"
api_port: 8000
cors_origins: 
  - "https://yourdomain.com"
  - "https://app.yourdomain.com"

# LLM settings (prefer OpenAI for production reliability)
use_ollama: false
openai_api_key: "${OPENAI_API_KEY}"  # Set via environment variable
llm_model: "gpt-3.5-turbo"

# File processing
max_file_size: 104857600  # 100MB
auto_tag_enabled: true
content_summary_enabled: true
relationship_tracking_enabled: true

# Performance settings
supported_extensions:
  - ".txt"
  - ".md"
  - ".pdf"
  - ".docx"
  - ".doc"
  - ".xlsx"
  - ".xls"
  - ".csv"
  - ".json"
  - ".xml"
  - ".py"
  - ".js"
  - ".html"
  - ".css"
  - ".sql"
  - ".yaml"
  - ".yml"
