"""
LIVE EXTENSION SCANNER - Real-time file type analysis with support detection
Shows live counts of each file extension as we scan through directories
"""

import os
import sys
import time
import threading
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine


class LiveExtensionScanner:
    """Live scanner that shows real-time extension counts"""
    
    def __init__(self):
        self.extension_counts = Counter()
        self.total_files = 0
        self.total_size = 0
        self.scanning = False
        self.start_time = None
        self.lock = threading.Lock()
        
        # Define supported extensions (comprehensive list matching desktop engine)
        self.supported_extensions = {
            # Documents & Text
            '.txt', '.md', '.markdown', '.mdown', '.mkd', '.mdx', '.rst', '.asciidoc', '.adoc', '.tex', '.latex',

            # Office Documents
            '.pdf', '.docx', '.doc', '.rtf', '.odt', '.pages', '.xlsx', '.xls', '.csv', '.ods', '.numbers', '.pptx', '.ppt', '.odp', '.key',

            # Python
            '.py', '.pyw', '.pyi', '.pyx',

            # JavaScript/TypeScript
            '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs', '.map',

            # Web
            '.html', '.htm', '.xhtml', '.shtml', '.css', '.scss', '.sass', '.less', '.styl',

            # Systems Programming
            '.c', '.h', '.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.rs', '.rlib', '.go', '.mod', '.sum',

            # JVM Languages
            '.java', '.class', '.jar', '.scala', '.sc', '.kt', '.kts', '.clj', '.cljs', '.cljc',

            # Other Languages
            '.cs', '.vb', '.fs', '.php', '.rb', '.swift', '.hs', '.ml', '.r', '.lua', '.pl', '.pm',

            # Shell & Scripts
            '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh', '.ps1', '.psm1', '.psd1', '.bat', '.cmd',

            # Data & Config
            '.json', '.jsonl', '.json5', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.config', '.properties', '.env',

            # Database
            '.sql', '.mysql', '.pgsql', '.sqlite', '.db', '.cql', '.cypher', '.sparql', '.graphql', '.gql',

            # Logs & Debug
            '.log', '.logs', '.out', '.err', '.trace', '.debug', '.dump', '.dmp',

            # Special Files
            '.readme', '.license', '.changelog', '.authors', '.makefile', '.dockerfile', '.gitignore',

            # Images (with OCR support)
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg',
            '.ico', '.psd', '.ai', '.eps', '.raw', '.cr2', '.nef', '.arw', '.dng',

            # Specialized & Data formats
            '.pkl', '.pickle', '.joblib', '.npy', '.npz', '.mat', '.h5', '.hdf5',
            '.parquet', '.avro', '.orc', '.feather', '.arrow', '.msgpack',
            '.protobuf', '.pb', '.proto', '.thrift', '.capnp',

            # Web & Font formats
            '.woff', '.woff2', '.ttf', '.otf', '.eot', '.svg', '.webmanifest',
            '.htaccess', '.htpasswd', '.robots', '.sitemap',

            # Archive formats (metadata extraction)
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.lz4', '.zst',

            # Binary data with metadata
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat', '.dump', '.core',

            # Specialized text formats
            '.pod', '.man', '.info', '.help', '.1', '.2', '.3', '.4', '.5', '.6', '.7', '.8', '.9',
            '.patch', '.diff', '.rej', '.orig', '.bak', '.swp', '.tmp'
        }
        
        # Excluded directories
        self.excluded_dirs = {
            'node_modules', '.git', '.svn', '__pycache__', '.pytest_cache',
            'venv', 'env', '.venv', 'build', 'dist', '.tox',
            'Windows', 'Program Files', 'Program Files (x86)',
            '$Recycle.Bin', 'System Volume Information',
            '.Trash', '.Trashes'
        }

        # Common extensionless files that should be treated as text
        self.extensionless_text_files = {
            'readme', 'license', 'changelog', 'authors', 'contributors',
            'copying', 'install', 'news', 'todo', 'fixme', 'version',
            'release', 'history', 'makefile', 'dockerfile', 'gemfile',
            'rakefile', 'procfile', 'vagrantfile', 'gruntfile', 'gulpfile'
        }
    
    def _should_skip_directory(self, dir_path: Path) -> bool:
        """Check if directory should be skipped"""
        dir_name = dir_path.name.lower()
        return dir_name in self.excluded_dirs

    def _is_extension_supported(self, extension: str) -> bool:
        """Check if extension is supported, including extensionless files"""
        if extension:
            # Handle special extensionless files marked with parentheses
            if extension.startswith('(') and extension.endswith(')'):
                filename = extension[1:-1]  # Remove parentheses
                return filename in self.extensionless_text_files
            else:
                return extension.lower() in self.supported_extensions
        else:
            return False
    
    def _update_display(self):
        """Update the live display"""
        while self.scanning:
            with self.lock:
                self._print_live_stats()
            time.sleep(1)  # Update every second
    
    def _print_live_stats(self):
        """Print current statistics"""
        # Clear screen (works on Windows)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        elapsed = time.time() - self.start_time if self.start_time else 0
        
        print("🔍 LIVE EXTENSION SCANNER")
        print("=" * 60)
        print(f"⏱️  Scanning time: {elapsed:.1f}s")
        print(f"📁 Total files found: {self.total_files:,}")
        print(f"💾 Total size: {self.total_size / (1024*1024*1024):.2f} GB")
        print(f"⚡ Speed: {self.total_files / max(elapsed, 1):.1f} files/sec")
        print()
        
        if self.extension_counts:
            print("📊 LIVE EXTENSION COUNTS:")
            print("-" * 60)
            
            # Sort by count (descending)
            sorted_extensions = sorted(self.extension_counts.items(), 
                                     key=lambda x: x[1], reverse=True)
            
            supported_count = 0
            unsupported_count = 0
            
            # Show top 30 extensions
            for i, (ext, count) in enumerate(sorted_extensions[:30]):
                if not ext:
                    ext = "(no extension)"
                
                # Check if supported
                is_supported = self._is_extension_supported(ext)
                status = "✅" if is_supported else "❌"
                
                if is_supported:
                    supported_count += count
                else:
                    unsupported_count += count
                
                print(f"{status} {ext:<15} {count:>8,} files")
            
            if len(sorted_extensions) > 30:
                remaining = len(sorted_extensions) - 30
                print(f"... and {remaining} more extensions")
            
            print()
            print("📈 SUPPORT SUMMARY:")
            print(f"✅ Supported files:   {supported_count:,} ({supported_count/max(self.total_files,1)*100:.1f}%)")
            print(f"❌ Unsupported files: {unsupported_count:,} ({unsupported_count/max(self.total_files,1)*100:.1f}%)")
    
    def scan_directory(self, root_path: Path):
        """Scan directory and count extensions"""
        print(f"🚀 Starting scan of: {root_path}")
        
        self.scanning = True
        self.start_time = time.time()
        
        # Start display update thread
        display_thread = threading.Thread(target=self._update_display, daemon=True)
        display_thread.start()
        
        try:
            for dirpath, dirnames, filenames in os.walk(root_path):
                current_dir = Path(dirpath)
                
                # Skip excluded directories
                dirnames[:] = [d for d in dirnames 
                             if not self._should_skip_directory(current_dir / d)]
                
                # Skip if current directory should be excluded
                if self._should_skip_directory(current_dir):
                    continue
                
                for filename in filenames:
                    file_path = current_dir / filename
                    
                    try:
                        if file_path.is_file():
                            # Get file extension
                            extension = file_path.suffix.lower()

                            # For extensionless files, check if it's a known text file
                            if not extension:
                                filename_lower = file_path.name.lower()
                                if filename_lower in self.extensionless_text_files:
                                    extension = f"({filename_lower})"  # Mark as special

                            # Get file size
                            try:
                                file_size = file_path.stat().st_size
                            except (OSError, PermissionError):
                                file_size = 0

                            # Update counts
                            with self.lock:
                                self.extension_counts[extension] += 1
                                self.total_files += 1
                                self.total_size += file_size
                    
                    except (OSError, PermissionError):
                        # Skip files we can't access
                        continue
        
        except KeyboardInterrupt:
            print("\n🛑 Scan interrupted by user")
        except Exception as e:
            print(f"\n💥 Error during scan: {e}")
        finally:
            self.scanning = False
            
            # Final display
            with self.lock:
                self._print_live_stats()
            
            print("\n🏁 SCAN COMPLETE!")
            self._print_final_summary()
    
    def _print_final_summary(self):
        """Print final summary with recommendations"""
        print("\n" + "=" * 60)
        print("📋 FINAL ANALYSIS & RECOMMENDATIONS")
        print("=" * 60)
        
        total_supported = sum(count for ext, count in self.extension_counts.items()
                            if self._is_extension_supported(ext))
        total_unsupported = self.total_files - total_supported
        
        print(f"✅ CURRENTLY SUPPORTED: {total_supported:,} files ({total_supported/max(self.total_files,1)*100:.1f}%)")
        print(f"❌ NOT YET SUPPORTED:   {total_unsupported:,} files ({total_unsupported/max(self.total_files,1)*100:.1f}%)")
        
        # Find top unsupported extensions
        unsupported_extensions = [(ext, count) for ext, count in self.extension_counts.items()
                                if not self._is_extension_supported(ext) and count > 10]
        unsupported_extensions.sort(key=lambda x: x[1], reverse=True)
        
        if unsupported_extensions:
            print(f"\n🎯 TOP UNSUPPORTED EXTENSIONS TO ADD:")
            for ext, count in unsupported_extensions[:10]:
                if not ext:
                    ext = "(no extension)"
                print(f"   {ext:<15} {count:>8,} files")
        
        print(f"\n💡 RECOMMENDATIONS:")
        if total_supported / max(self.total_files, 1) > 0.8:
            print("   🎉 EXCELLENT! 80%+ files already supported")
        elif total_supported / max(self.total_files, 1) > 0.5:
            print("   👍 GOOD! 50%+ files supported, room for improvement")
        else:
            print("   ⚠️  NEEDS WORK! Less than 50% files supported")
        
        print(f"   📈 Potential improvement: +{total_unsupported:,} files with better support")


def find_target_directory():
    """Find directory to scan"""
    print("🔍 SELECT DIRECTORY TO SCAN:")
    print("1. Samsung USB Drive (E:\\)")
    print("2. Current directory")
    print("3. Custom path")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        usb_path = Path("E:\\")
        if usb_path.exists():
            return usb_path
        else:
            print("❌ USB drive not found at E:\\")
            return None
    elif choice == "2":
        return Path.cwd()
    elif choice == "3":
        custom_path = input("Enter path: ").strip()
        path = Path(custom_path)
        if path.exists():
            return path
        else:
            print(f"❌ Path not found: {custom_path}")
            return None
    else:
        print("❌ Invalid choice")
        return None


def main():
    """Main function"""
    print("🚀 LIVE EXTENSION SCANNER")
    print("Real-time file type analysis with support detection")
    print("=" * 60)
    
    # Find target directory
    target_dir = find_target_directory()
    if not target_dir:
        print("❌ No valid directory selected")
        return
    
    # Create scanner and start
    scanner = LiveExtensionScanner()
    
    try:
        scanner.scan_directory(target_dir)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
