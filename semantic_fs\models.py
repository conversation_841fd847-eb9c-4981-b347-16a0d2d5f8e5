"""
Core data models for the semantic filesystem
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class FileRecord(Base):
    """Database model for file metadata"""
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True)
    path = Column(String, unique=True, nullable=False)
    filename = Column(String, nullable=False)
    file_type = Column(String, nullable=False)
    size = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    modified_at = Column(DateTime, default=datetime.utcnow)
    accessed_at = Column(DateTime, default=datetime.utcnow)
    
    # Semantic metadata
    content_summary = Column(Text)
    auto_tags = Column(JSON)  # List of automatically generated tags
    user_tags = Column(JSON)  # User-defined tags
    embedding_id = Column(String)  # Reference to vector embedding
    
    # Relationships
    relationships = relationship("FileRelationship", back_populates="file")

class FileRelationship(Base):
    """Tracks relationships between files and contexts"""
    __tablename__ = "file_relationships"
    
    id = Column(Integer, primary_key=True)
    file_id = Column(Integer, ForeignKey("files.id"))
    relationship_type = Column(String, nullable=False)  # "used_in", "references", "part_of"
    context = Column(String, nullable=False)  # "youtube_video", "paper", "project"
    context_id = Column(String)  # Specific identifier
    context_metadata = Column(JSON)  # Additional context info
    created_at = Column(DateTime, default=datetime.utcnow)
    
    file = relationship("FileRecord", back_populates="relationships")

class SemanticQuery(BaseModel):
    """Model for natural language queries"""
    query: str
    filters: Optional[Dict[str, Any]] = None
    limit: int = Field(default=10, ge=1, le=100)
    include_content: bool = False

class FileMetadata(BaseModel):
    """Pydantic model for file metadata"""
    id: int
    path: str
    filename: str
    file_type: str
    size: int
    created_at: datetime
    modified_at: datetime
    accessed_at: datetime
    content_summary: Optional[str] = None
    auto_tags: List[str] = []
    user_tags: List[str] = []
    relationships: List[Dict[str, Any]] = []

class SearchResult(BaseModel):
    """Model for search results"""
    file: FileMetadata
    relevance_score: float
    match_reason: str
    content_snippet: Optional[str] = None

class QueryResult(BaseModel):
    """Model for query results"""
    query: str
    results: List[SearchResult]
    total_found: int
    execution_time: float
    interpretation: str  # How the query was interpreted
