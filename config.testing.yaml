# Testing Environment Configuration
# This file contains test-specific settings

# Database settings (use in-memory for tests)
database_url: "sqlite:///:memory:"
vector_db_path: "./test_vector_db"

# Logging settings
log_level: "WARNING"  # Reduce noise during tests
enable_file_logging: false
enable_json_logging: false
enable_log_rotation: false

# Security settings (disabled for testing)
auth_enabled: false
require_auth_for_read: false
require_auth_for_write: false
rate_limit_enabled: false
security_headers_enabled: false

# API settings
api_host: "localhost"
api_port: 8001  # Different port to avoid conflicts
cors_origins: ["*"]

# LLM settings (use mocks in tests)
use_ollama: false
openai_api_key: "test_key"
llm_model: "gpt-3.5-turbo"

# File processing (smaller limits for faster tests)
max_file_size: 1048576  # 1MB
auto_tag_enabled: false  # Disable to speed up tests
content_summary_enabled: false
relationship_tracking_enabled: false

# Performance settings (minimal for testing)
supported_extensions:
  - ".txt"
  - ".md"
  - ".json"
  - ".py"
