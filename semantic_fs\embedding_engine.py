"""
Embedding engine for converting text to semantic vectors
"""

import hashlib
import logging
from typing import List, Optional, Dict, Any
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.config import Settings as ChromaSettings

from .config import settings
from .cache import cache_embedding

logger = logging.getLogger(__name__)

class EmbeddingEngine:
    """Handles text embedding and vector storage"""
    
    def __init__(self):
        try:
            # Initialize sentence transformer model
            logger.info(f"Loading embedding model: {settings.embedding_model}")
            self.model = SentenceTransformer(settings.embedding_model)

            # Initialize ChromaDB
            logger.info(f"Initializing ChromaDB at: {settings.vector_db_path}")
            self.chroma_client = chromadb.PersistentClient(
                path=settings.vector_db_path,
                settings=ChromaSettings(anonymized_telemetry=False)
            )

            # Get or create collection
            self.collection = self.chroma_client.get_or_create_collection(
                name="file_embeddings",
                metadata={"description": "File content embeddings for semantic search"}
            )
            logger.info("Embedding engine initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize embedding engine: {e}")
            raise
    
    @cache_embedding(ttl=3600)  # Cache for 1 hour
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding vector for text"""
        embedding = self.model.encode(text, convert_to_tensor=False)
        return embedding.tolist()
    
    def generate_embedding_id(self, file_path: str, content_hash: str) -> str:
        """Generate unique ID for embedding"""
        combined = f"{file_path}:{content_hash}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def store_embedding(
        self,
        embedding_id: str,
        text: str,
        metadata: Dict[str, Any]
    ) -> None:
        """Store text embedding in vector database"""
        try:
            embedding = self.generate_embedding(text)

            # Validate metadata - ensure all values are scalar types
            validated_metadata = {}
            for key, value in metadata.items():
                if isinstance(value, (str, int, float, bool)) or value is None:
                    validated_metadata[key] = value
                else:
                    # Convert non-scalar values to strings
                    validated_metadata[key] = str(value)

            self.collection.upsert(
                ids=[embedding_id],
                embeddings=[embedding],
                documents=[text],
                metadatas=[validated_metadata]
            )
            logger.debug(f"Stored embedding for: {embedding_id}")

        except Exception as e:
            logger.error(f"Failed to store embedding {embedding_id}: {e}")
            raise
    
    def search_similar(
        self, 
        query_text: str, 
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar content using semantic similarity"""
        query_embedding = self.generate_embedding(query_text)
        
        # Build where clause for filtering
        where_clause = {}
        if filters:
            where_clause.update(filters)
        
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=limit,
            where=where_clause if where_clause else None,
            include=["documents", "metadatas", "distances"]
        )
        
        # Format results
        formatted_results = []
        if results["ids"] and results["ids"][0]:
            for i, embedding_id in enumerate(results["ids"][0]):
                # Convert distance to similarity score (0-1 range)
                distance = results["distances"][0][i]
                # For cosine distance, similarity = (2 - distance) / 2 to ensure 0-1 range
                similarity_score = max(0.0, min(1.0, (2 - distance) / 2))

                formatted_results.append({
                    "embedding_id": embedding_id,
                    "document": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "similarity_score": similarity_score
                })
        
        return formatted_results
    
    def delete_embedding(self, embedding_id: str) -> None:
        """Delete embedding from vector database"""
        try:
            self.collection.delete(ids=[embedding_id])
        except Exception:
            # Embedding might not exist, which is fine
            pass
    
    def update_embedding(
        self, 
        embedding_id: str, 
        text: str, 
        metadata: Dict[str, Any]
    ) -> None:
        """Update existing embedding"""
        # Delete old embedding and create new one
        self.delete_embedding(embedding_id)
        self.store_embedding(embedding_id, text, metadata)
