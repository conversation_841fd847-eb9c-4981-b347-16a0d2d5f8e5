2025-08-06 18:12:41 ERROR    [semantic_fs.config_manager] reload_config:164 - Error reloading configuration: 72 validation errors for Settings
USE_OLLAMA
  Extra inputs are not permitted [type=extra_forbidden, input_value='false', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_BASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://localhost:11434', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-oss:20b', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OPENAI_API_KEY
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LLM_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-3.5-turbo', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DATABASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='sqlite:///./semantic_fs.db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VECTOR_DB_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='./vector_db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
EMBEDDING_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='all-MiniLM-L6-v2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
AUTO_TAG_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CONTENT_SUMMARY_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
RELATIONSHIP_TRACKING_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_HOST
  Extra inputs are not permitted [type=extra_forbidden, input_value='localhost', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_PORT
  Extra inputs are not permitted [type=extra_forbidden, input_value='8000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ALLUSERSPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
APPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Roaming', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYINSTALL
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData\\chocolatey', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYLASTPATHUPDATE
  Extra inputs are not permitted [type=extra_forbidden, input_value='133645995987675342', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHROME_CRASHPAD_PIPE_NAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\crashpad_5328_MKXLPCIYFOKKVWLC', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMPUTERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMSPEC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\WINDOWS\\system32\\cmd.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DRIVERDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Windows\\System32\\Drivers\\DriverData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\Users\\Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOCALAPPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOGONSERVER
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
NUMBER_OF_PROCESSORS
  Extra inputs are not permitted [type=extra_forbidden, input_value='4', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVECONSUMER
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ORIGINAL_XDG_CURRENT_DESKTOP
  Extra inputs are not permitted [type=extra_forbidden, input_value='undefined', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OS
  Extra inputs are not permitted [type=extra_forbidden, input_value='Windows_NT', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\...ilot-chat\\debugCommand', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATHEXT
  Extra inputs are not permitted [type=extra_forbidden, input_value='.COM;.EXE;.BAT;.CMD;.VBS....WSH;.MSC;.RB;.RBW;.CPL', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_ARCHITECTURE
  Extra inputs are not permitted [type=extra_forbidden, input_value='AMD64', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_IDENTIFIER
  Extra inputs are not permitted [type=extra_forbidden, input_value='Intel64 Family 6 Model 1...tepping 9, GenuineIntel', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_LEVEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='6', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_REVISION
  Extra inputs are not permitted [type=extra_forbidden, input_value='9e09', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PSMODULEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\Doc...\Program Files\\Intel\\', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PUBLIC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TEMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN_ROAMINGPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ZES_ENABLE_SYSMAN
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_PAGER
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM
  Extra inputs are not permitted [type=extra_forbidden, input_value='vscode', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM_VERSION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1.102.3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LANG
  Extra inputs are not permitted [type=extra_forbidden, input_value='en_US.UTF-8', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COLORTERM
  Extra inputs are not permitted [type=extra_forbidden, input_value='truecolor', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_ASKPASS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\git\\dist\\askpass.sh', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_NODE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\App...osoft VS Code\\Code.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_EXTRA_ARGS
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_MAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\dist\\askpass-main.js', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_IPC_HANDLE
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\vscode-git-08700968f9-sock', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PYDEVD_DISABLE_FILE_VALIDATION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...nt-2c3054fe09d561c9.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
BUNDLED_DEBUGPY_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...\bundled\\libs\\debugpy', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_INJECTION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_DUPLICATE_LIB_OK
  Extra inputs are not permitted [type=extra_forbidden, input_value='True', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_INIT_AT_FORK
  Extra inputs are not permitted [type=extra_forbidden, input_value='FALSE', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-08-06 18:12:41 ERROR    [semantic_fs.config_manager] reload_config:164 - Error reloading configuration: 72 validation errors for Settings
USE_OLLAMA
  Extra inputs are not permitted [type=extra_forbidden, input_value='false', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_BASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://localhost:11434', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-oss:20b', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OPENAI_API_KEY
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LLM_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-3.5-turbo', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DATABASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='sqlite:///./semantic_fs.db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VECTOR_DB_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='./vector_db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
EMBEDDING_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='all-MiniLM-L6-v2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
AUTO_TAG_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CONTENT_SUMMARY_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
RELATIONSHIP_TRACKING_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_HOST
  Extra inputs are not permitted [type=extra_forbidden, input_value='localhost', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_PORT
  Extra inputs are not permitted [type=extra_forbidden, input_value='8000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ALLUSERSPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
APPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Roaming', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYINSTALL
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData\\chocolatey', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYLASTPATHUPDATE
  Extra inputs are not permitted [type=extra_forbidden, input_value='133645995987675342', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHROME_CRASHPAD_PIPE_NAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\crashpad_5328_MKXLPCIYFOKKVWLC', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMPUTERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMSPEC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\WINDOWS\\system32\\cmd.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DRIVERDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Windows\\System32\\Drivers\\DriverData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\Users\\Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOCALAPPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOGONSERVER
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
NUMBER_OF_PROCESSORS
  Extra inputs are not permitted [type=extra_forbidden, input_value='4', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVECONSUMER
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ORIGINAL_XDG_CURRENT_DESKTOP
  Extra inputs are not permitted [type=extra_forbidden, input_value='undefined', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OS
  Extra inputs are not permitted [type=extra_forbidden, input_value='Windows_NT', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\...ilot-chat\\debugCommand', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATHEXT
  Extra inputs are not permitted [type=extra_forbidden, input_value='.COM;.EXE;.BAT;.CMD;.VBS....WSH;.MSC;.RB;.RBW;.CPL', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_ARCHITECTURE
  Extra inputs are not permitted [type=extra_forbidden, input_value='AMD64', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_IDENTIFIER
  Extra inputs are not permitted [type=extra_forbidden, input_value='Intel64 Family 6 Model 1...tepping 9, GenuineIntel', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_LEVEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='6', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_REVISION
  Extra inputs are not permitted [type=extra_forbidden, input_value='9e09', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PSMODULEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\Doc...\Program Files\\Intel\\', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PUBLIC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TEMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN_ROAMINGPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ZES_ENABLE_SYSMAN
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_PAGER
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM
  Extra inputs are not permitted [type=extra_forbidden, input_value='vscode', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM_VERSION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1.102.3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LANG
  Extra inputs are not permitted [type=extra_forbidden, input_value='en_US.UTF-8', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COLORTERM
  Extra inputs are not permitted [type=extra_forbidden, input_value='truecolor', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_ASKPASS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\git\\dist\\askpass.sh', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_NODE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\App...osoft VS Code\\Code.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_EXTRA_ARGS
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_MAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\dist\\askpass-main.js', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_IPC_HANDLE
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\vscode-git-08700968f9-sock', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PYDEVD_DISABLE_FILE_VALIDATION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...nt-2c3054fe09d561c9.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
BUNDLED_DEBUGPY_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...\bundled\\libs\\debugpy', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_INJECTION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_DUPLICATE_LIB_OK
  Extra inputs are not permitted [type=extra_forbidden, input_value='True', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_INIT_AT_FORK
  Extra inputs are not permitted [type=extra_forbidden, input_value='FALSE', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
