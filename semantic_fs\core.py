"""
Core semantic filesystem engine
"""

import os
import hashlib
import mimetypes
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tu<PERSON>
from pathlib import Path
import logging

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from .database import get_db, initialize_database
from .models import FileRecord, FileRelationship, SemanticQuery, QueryResult, SearchResult, FileMetadata
from .embedding_engine import EmbeddingEngine
from .content_extractor import ContentExtractor
from .llm_processor import LLMProcessor
from .cache import cache_query_result, get_query_cache
from .config import settings
from .exceptions import (
    FileProcessingError, ContentExtractionError, EmbeddingError,
    SearchError, DatabaseError, FileSystemError, handle_errors,
    ErrorSeverity, ErrorRecovery
)

logger = logging.getLogger(__name__)

class SemanticFilesystem:
    """Main semantic filesystem engine"""

    _instance = None
    _initialized = False

    def __new__(cls):
        """Singleton pattern to ensure only one instance"""
        if cls._instance is None:
            cls._instance = super(SemanticFilesystem, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the semantic filesystem"""
        if self._initialized:
            return

        logger.info("Initializing semantic filesystem...")

        # Initialize resource management
        try:
            from .resource_manager import start_resource_management
            start_resource_management()
        except ImportError:
            logger.warning("Resource management not available")

        # Initialize components
        self.embedding_engine = EmbeddingEngine()
        self.content_extractor = ContentExtractor()
        self.llm_processor = LLMProcessor()

        # Initialize database with migration support
        initialize_database()

        # Register cleanup
        self._register_cleanup_handlers()

        self._initialized = True
        logger.info("Semantic filesystem initialized")

    def _register_cleanup_handlers(self):
        """Register cleanup handlers for resource management"""
        try:
            from .resource_manager import register_cleanup_handler

            # Register component cleanup
            if hasattr(self.embedding_engine, 'cleanup'):
                register_cleanup_handler(self.embedding_engine.cleanup)

            if hasattr(self.content_extractor, 'cleanup'):
                register_cleanup_handler(self.content_extractor.cleanup)

            if hasattr(self.llm_processor, 'cleanup'):
                register_cleanup_handler(self.llm_processor.cleanup)

            # Register cache cleanup
            from .cache import get_memory_cache
            cache = get_memory_cache()
            register_cleanup_handler(cache.clear)

        except ImportError:
            logger.warning("Resource management not available for cleanup registration")
    
    @handle_errors(
        error_types=(FileProcessingError, ContentExtractionError, EmbeddingError, DatabaseError),
        severity=ErrorSeverity.MEDIUM,
        recovery=ErrorRecovery.FALLBACK,
        fallback_value=None
    )
    def index_file(self, file_path: str, context: Optional[Dict[str, Any]] = None) -> Optional[FileRecord]:
        """Index a single file into the semantic filesystem"""
        try:
            file_path = Path(file_path).resolve()

            # Validate file exists
            if not file_path.exists():
                raise FileSystemError(
                    path=str(file_path),
                    operation="index",
                    message="File does not exist"
                )

            # Check if file is readable
            if not os.access(file_path, os.R_OK):
                raise FileSystemError(
                    path=str(file_path),
                    operation="index",
                    message="File is not readable (permission denied)"
                )

            # Check file size
            file_size = file_path.stat().st_size
            if file_size > settings.max_file_size:
                raise FileProcessingError(
                    file_path=str(file_path),
                    message=f"File too large ({file_size} bytes, max {settings.max_file_size} bytes)"
                )

            if file_size == 0:
                raise FileProcessingError(
                    file_path=str(file_path),
                    message="File is empty"
                )
            
            # Extract file metadata
            file_stats = file_path.stat()
            file_type = mimetypes.guess_type(str(file_path))[0] or "unknown"
            
            # Extract content
            try:
                content = self.content_extractor.extract_content(str(file_path))
                if not content:
                    raise ContentExtractionError(
                        file_path=str(file_path),
                        file_type=file_type,
                        original_error=None
                    )
            except Exception as e:
                raise ContentExtractionError(
                    file_path=str(file_path),
                    file_type=file_type,
                    original_error=e
                )
            
            # Generate content hash for embedding ID
            content_hash = hashlib.md5(content.encode()).hexdigest()
            embedding_id = self.embedding_engine.generate_embedding_id(str(file_path), content_hash)
            
            # Generate content summary and tags using LLM
            summary = self.llm_processor.generate_summary(content, str(file_path))
            auto_tags = self.llm_processor.generate_tags(content, str(file_path))
            
            with get_db() as db:
                # Check if file already exists
                existing_file = db.query(FileRecord).filter(FileRecord.path == str(file_path)).first()
                
                if existing_file:
                    # Update existing file
                    existing_file.modified_at = datetime.fromtimestamp(file_stats.st_mtime)
                    existing_file.accessed_at = datetime.fromtimestamp(file_stats.st_atime)
                    existing_file.size = file_stats.st_size
                    existing_file.content_summary = summary
                    existing_file.auto_tags = auto_tags
                    existing_file.embedding_id = embedding_id
                    
                    # Update embedding
                    self.embedding_engine.update_embedding(
                        embedding_id,
                        content,
                        {
                            "file_path": str(file_path),
                            "file_type": file_type,
                            "filename": file_path.name,
                            "summary": summary,
                            "tags": ", ".join(auto_tags) if auto_tags else ""
                        }
                    )
                    
                    file_record = existing_file
                else:
                    # Create new file record
                    file_record = FileRecord(
                        path=str(file_path),
                        filename=file_path.name,
                        file_type=file_type,
                        size=file_stats.st_size,
                        created_at=datetime.fromtimestamp(file_stats.st_ctime),
                        modified_at=datetime.fromtimestamp(file_stats.st_mtime),
                        accessed_at=datetime.fromtimestamp(file_stats.st_atime),
                        content_summary=summary,
                        auto_tags=auto_tags,
                        embedding_id=embedding_id
                    )
                    
                    db.add(file_record)
                    db.flush()  # Get the ID
                    
                    # Store embedding
                    self.embedding_engine.store_embedding(
                        embedding_id,
                        content,
                        {
                            "file_id": file_record.id,
                            "file_path": str(file_path),
                            "file_type": file_type,
                            "filename": file_path.name,
                            "summary": summary,
                            "tags": ", ".join(auto_tags) if auto_tags else ""
                        }
                    )
                
                # Add context relationships if provided
                if context:
                    self._add_context_relationships(db, file_record, context)
                
                db.commit()
                logger.info(f"Successfully indexed: {file_path}")
                return file_record
                
        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {e}")
            return None
    
    @cache_query_result(ttl=300)  # Cache for 5 minutes
    def query(self, query: SemanticQuery) -> QueryResult:
        """Process a natural language query and return results"""
        start_time = datetime.now()
        
        try:
            # Use LLM to interpret the query
            interpretation = self.llm_processor.interpret_query(query.query)
            
            # Extract search terms and filters from interpretation
            search_terms = interpretation.get("search_terms", [])
            temporal_filters = interpretation.get("temporal_filters", {})
            context_filters = interpretation.get("context_filters", {})
            file_type_filters = interpretation.get("file_type_filters", [])
            
            # Perform semantic search
            semantic_results = []
            if search_terms:
                combined_query = " ".join(search_terms)
                semantic_results = self.embedding_engine.search_similar(
                    combined_query,
                    limit=query.limit * 2  # Get more results for filtering
                )
            
            # Query database with filters
            with get_db() as db:
                db_query = db.query(FileRecord)
                
                # Apply temporal filters
                if temporal_filters:
                    if "start_date" in temporal_filters:
                        start_date = temporal_filters["start_date"]
                        if isinstance(start_date, str):
                            start_date = datetime.strptime(start_date, '%Y-%m-%d')
                        db_query = db_query.filter(FileRecord.created_at >= start_date)
                        logger.debug(f"Applied start_date filter: {start_date}")
                    if "end_date" in temporal_filters:
                        end_date = temporal_filters["end_date"]
                        if isinstance(end_date, str):
                            end_date = datetime.strptime(end_date, '%Y-%m-%d')
                            # Set to end of day to include files created on the end date
                            end_date = end_date.replace(hour=23, minute=59, second=59)
                        db_query = db_query.filter(FileRecord.created_at <= end_date)
                        logger.debug(f"Applied end_date filter: {end_date}")
                
                # Apply file type filters
                if file_type_filters:
                    db_query = db_query.filter(FileRecord.file_type.in_(file_type_filters))
                
                # Get all matching files
                db_results = db_query.all()
                
                # Combine and rank results
                results = self._combine_and_rank_results(
                    semantic_results, 
                    db_results, 
                    context_filters,
                    query.query
                )
                
                # Limit results
                results = results[:query.limit]
                
                # Convert to SearchResult objects
                search_results = []
                for result in results:
                    file_metadata = FileMetadata(
                        id=result["file"].id,
                        path=result["file"].path,
                        filename=result["file"].filename,
                        file_type=result["file"].file_type,
                        size=result["file"].size,
                        created_at=result["file"].created_at,
                        modified_at=result["file"].modified_at,
                        accessed_at=result["file"].accessed_at,
                        content_summary=result["file"].content_summary,
                        auto_tags=result["file"].auto_tags or [],
                        user_tags=result["file"].user_tags or [],
                        relationships=[
                            {
                                "type": rel.relationship_type,
                                "context": rel.context,
                                "context_id": rel.context_id,
                                "metadata": rel.context_metadata
                            }
                            for rel in result["file"].relationships
                        ]
                    )
                    
                    search_result = SearchResult(
                        file=file_metadata,
                        relevance_score=result["score"],
                        match_reason=result["reason"],
                        content_snippet=result.get("snippet")
                    )
                    search_results.append(search_result)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return QueryResult(
                    query=query.query,
                    results=search_results,
                    total_found=len(search_results),
                    execution_time=execution_time,
                    interpretation=interpretation.get("interpretation", "")
                )
                
        except Exception as e:
            logger.error(f"Error processing query '{query.query}': {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            return QueryResult(
                query=query.query,
                results=[],
                total_found=0,
                execution_time=execution_time,
                interpretation=f"Error processing query: {e}"
            )
    
    def _add_context_relationships(self, db: Session, file_record: FileRecord, context: Dict[str, Any]):
        """Add context relationships for a file"""
        for context_type, context_data in context.items():
            if isinstance(context_data, dict):
                relationship = FileRelationship(
                    file_id=file_record.id,
                    relationship_type="used_in",
                    context=context_type,
                    context_id=context_data.get("id", ""),
                    context_metadata=context_data
                )
                db.add(relationship)
    
    def _combine_and_rank_results(
        self, 
        semantic_results: List[Dict], 
        db_results: List[FileRecord],
        context_filters: Dict[str, Any],
        original_query: str
    ) -> List[Dict[str, Any]]:
        """Combine semantic and database results with intelligent ranking"""
        combined_results = {}
        
        # Add semantic results
        for result in semantic_results:
            file_path = result["metadata"].get("file_path")
            if file_path:
                # Use file_path as key for now, will convert to file_id when matching with db_results
                combined_results[file_path] = {
                    "file": None,  # Will be filled from db_results
                    "semantic_score": result["similarity_score"],
                    "snippet": result["document"][:200] + "..." if len(result["document"]) > 200 else result["document"],
                    "reasons": ["semantic similarity"]
                }
        
        # Add database results and match with semantic results
        for file_record in db_results:
            # Check if this file has semantic results (by file path)
            if file_record.path in combined_results:
                combined_results[file_record.path]["file"] = file_record
                # Convert key from file_path to file_id for consistency
                combined_results[file_record.id] = combined_results.pop(file_record.path)
            else:
                combined_results[file_record.id] = {
                    "file": file_record,
                    "semantic_score": 0.0,
                    "snippet": file_record.content_summary,
                    "reasons": ["metadata match"]
                }
        
        # Filter out results without file records
        valid_results = [r for r in combined_results.values() if r["file"] is not None]
        
        # Apply context filters and boost scores
        for result in valid_results:
            context_score = self._calculate_context_score(result["file"], context_filters)
            result["context_score"] = context_score
            
            # Calculate final score
            result["score"] = (
                result["semantic_score"] * 0.6 + 
                context_score * 0.4
            )
            
            # Create match reason
            result["reason"] = ", ".join(result["reasons"])
            if context_score > 0:
                result["reason"] += ", context match"
        
        # Sort by final score
        valid_results.sort(key=lambda x: x["score"], reverse=True)
        
        return valid_results
    
    def _calculate_context_score(self, file_record: FileRecord, context_filters: Dict[str, Any]) -> float:
        """Calculate context relevance score for a file"""
        if not context_filters:
            return 0.0
        
        score = 0.0
        max_score = len(context_filters)
        
        for context_type, context_value in context_filters.items():
            for relationship in file_record.relationships:
                if relationship.context == context_type:
                    if isinstance(context_value, str):
                        if context_value.lower() in str(relationship.context_metadata).lower():
                            score += 1.0
                    else:
                        score += 0.5  # Partial match for complex filters
        
        return score / max_score if max_score > 0 else 0.0
