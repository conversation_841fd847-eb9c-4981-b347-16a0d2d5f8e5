"""
Configuration settings for the semantic filesystem
"""

import os
import logging
from pathlib import Path
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings with validation"""

    # Database settings
    database_url: str = Field(default="sqlite:///./semantic_fs.db", description="Database connection URL")

    # Vector database settings
    vector_db_path: str = Field(default="./vector_db", description="Path to vector database storage")
    embedding_model: str = Field(default="all-MiniLM-L6-v2", description="Sentence transformer model name")

    # LLM settings
    openai_api_key: str = Field(default="", description="OpenAI API key (optional)")
    llm_model: str = Field(default="gpt-3.5-turbo", description="OpenAI model to use")

    # Ollama settings (local LLM)
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama API base URL")
    ollama_model: str = Field(default="phi", description="Ollama model to use (phi, mistral, etc.)")
    use_ollama: bool = Field(default=True, description="Use Ollama instead of OpenAI when available")

    # File system settings
    watch_directories: List[str] = Field(default_factory=list, description="Directories to monitor for changes")
    max_file_size: int = Field(default=100 * 1024 * 1024, ge=1024, description="Maximum file size in bytes")
    supported_extensions: List[str] = Field(
        default=[
            ".txt", ".md", ".pdf", ".docx", ".doc",
            ".xlsx", ".xls", ".csv", ".json", ".xml",
            ".py", ".js", ".html", ".css", ".sql"
        ],
        description="Supported file extensions"
    )

    # Semantic processing settings
    auto_tag_enabled: bool = Field(default=True, description="Enable automatic tag generation")
    content_summary_enabled: bool = Field(default=True, description="Enable content summarization")
    relationship_tracking_enabled: bool = Field(default=True, description="Enable relationship tracking")

    # API settings
    api_host: str = Field(default="localhost", description="API server host")
    api_port: int = Field(default=8000, ge=1, le=65535, description="API server port")

    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
    enable_file_logging: bool = Field(default=True, description="Enable logging to files")
    enable_json_logging: bool = Field(default=False, description="Enable structured JSON logging")
    enable_log_rotation: bool = Field(default=True, description="Enable log file rotation")
    max_log_file_size: int = Field(default=10 * 1024 * 1024, description="Maximum log file size in bytes")
    log_backup_count: int = Field(default=5, ge=1, le=50, description="Number of log backup files to keep")

    # Security settings
    auth_enabled: bool = Field(default=False, description="Enable authentication")
    require_auth_for_read: bool = Field(default=False, description="Require authentication for read operations")
    require_auth_for_write: bool = Field(default=True, description="Require authentication for write operations")
    jwt_secret_key: str = Field(default="", description="JWT secret key (auto-generated if empty)")
    api_keys: List[str] = Field(default_factory=list, description="Valid API keys")
    rate_limit_enabled: bool = Field(default=True, description="Enable rate limiting")
    rate_limit_requests: int = Field(default=100, ge=1, description="Max requests per window")
    rate_limit_window: int = Field(default=60, ge=1, description="Rate limit window in seconds")
    security_headers_enabled: bool = Field(default=True, description="Enable security headers")
    cors_origins: List[str] = Field(default=["*"], description="Allowed CORS origins")

    @validator('vector_db_path')
    def validate_vector_db_path(cls, v):
        """Ensure vector database path is valid"""
        path = Path(v)
        try:
            path.mkdir(parents=True, exist_ok=True)
            if not path.is_dir():
                raise ValueError(f"Vector database path is not a directory: {v}")
        except Exception as e:
            raise ValueError(f"Cannot create vector database directory {v}: {e}")
        return str(path.resolve())

    @validator('supported_extensions')
    def validate_extensions(cls, v):
        """Ensure all extensions start with a dot"""
        validated = []
        for ext in v:
            if not ext.startswith('.'):
                ext = '.' + ext
            validated.append(ext.lower())
        return validated

    @validator('log_level')
    def validate_log_level(cls, v):
        """Ensure log level is valid"""
        valid_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}. Must be one of {valid_levels}")
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra environment variables

def validate_dependencies():
    """Validate that required dependencies are available"""
    missing_deps = []
    warnings = []

    # Check for required packages
    try:
        import sentence_transformers
    except ImportError:
        missing_deps.append("sentence-transformers")

    try:
        import chromadb
    except ImportError:
        missing_deps.append("chromadb")

    try:
        import sqlalchemy
    except ImportError:
        missing_deps.append("sqlalchemy")

    try:
        import fastapi
    except ImportError:
        missing_deps.append("fastapi")

    try:
        import uvicorn
    except ImportError:
        missing_deps.append("uvicorn")

    # Check for optional packages
    try:
        import openai
    except ImportError:
        warnings.append("OpenAI package not available - LLM features will use fallback methods")

    try:
        import magic
    except ImportError:
        warnings.append("python-magic not available - file type detection may be limited")

    if missing_deps:
        raise ImportError(
            f"Missing required dependencies: {', '.join(missing_deps)}. "
            f"Please install them with: pip install {' '.join(missing_deps)}"
        )

    for warning in warnings:
        logger.warning(warning)

    return True

# Validate dependencies first
validate_dependencies()

# Global settings instance
try:
    settings = Settings()
    logger.info("Configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load configuration: {e}")
    raise
