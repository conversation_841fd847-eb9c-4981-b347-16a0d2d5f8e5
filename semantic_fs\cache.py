"""
Caching system for semantic filesystem
"""

import hashlib
import json
import time
import logging
from typing import Any, Optional, Dict, List
from pathlib import Path
from functools import wraps
import pickle

logger = logging.getLogger(__name__)

class MemoryCache:
    """In-memory cache with TTL support and automatic cleanup"""

    def __init__(self, default_ttl: int = 300, max_size: int = 1000):  # 5 minutes default, 1000 items max
        """Initialize memory cache

        Args:
            default_ttl: Default time-to-live in seconds
            max_size: Maximum number of items in cache
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.last_cleanup = time.time()
        self.cleanup_interval = 60  # Cleanup every minute
    
    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """Check if cache entry is expired"""
        return time.time() > entry['expires_at']
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        if self._is_expired(entry):
            del self.cache[key]
            return None
        
        # Update access time for LRU
        entry['accessed_at'] = time.time()
        return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with automatic cleanup

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (uses default if None)
        """
        # Check if cleanup is needed
        self._maybe_cleanup()

        # Enforce size limit
        if len(self.cache) >= self.max_size:
            self._evict_oldest()

        ttl = ttl or self.default_ttl
        expires_at = time.time() + ttl

        self.cache[key] = {
            'value': value,
            'expires_at': expires_at,
            'created_at': time.time(),
            'accessed_at': time.time()
        }

    def _maybe_cleanup(self):
        """Run cleanup if needed"""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self.cleanup_expired()
            self.last_cleanup = current_time

    def _evict_oldest(self):
        """Evict oldest entries to make room"""
        if not self.cache:
            return

        # Sort by access time and remove oldest 10%
        items = list(self.cache.items())
        items.sort(key=lambda x: x[1]['accessed_at'])

        num_to_remove = max(1, len(items) // 10)
        for i in range(num_to_remove):
            key, _ = items[i]
            del self.cache[key]

        logger.debug(f"Evicted {num_to_remove} cache entries")

    def cleanup_expired(self) -> int:
        """Remove expired entries from cache

        Returns:
            Number of entries removed
        """
        current_time = time.time()
        expired_keys = []

        for key, entry in self.cache.items():
            if current_time > entry['expires_at']:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

        return len(expired_keys)

    def clear(self) -> int:
        """Clear all cache entries

        Returns:
            Number of entries cleared
        """
        count = len(self.cache)
        self.cache.clear()
        logger.debug(f"Cleared {count} cache entries")
        return count

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        current_time = time.time()
        expired_count = sum(1 for entry in self.cache.values()
                          if current_time > entry['expires_at'])

        return {
            "total_entries": len(self.cache),
            "expired_entries": expired_count,
            "max_size": self.max_size,
            "utilization": len(self.cache) / self.max_size if self.max_size > 0 else 0,
            "last_cleanup": self.last_cleanup
        }
    
    def delete(self, key: str) -> bool:
        """Delete key from cache
        
        Args:
            key: Cache key
            
        Returns:
            True if key was deleted, False if not found
        """
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        self.cache.clear()
    
    def cleanup_expired(self) -> int:
        """Remove expired entries
        
        Returns:
            Number of entries removed
        """
        expired_keys = []
        current_time = time.time()
        
        for key, entry in self.cache.items():
            if current_time > entry['expires_at']:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics
        
        Returns:
            Dictionary with cache stats
        """
        current_time = time.time()
        expired_count = sum(1 for entry in self.cache.values() 
                          if current_time > entry['expires_at'])
        
        return {
            'total_entries': len(self.cache),
            'expired_entries': expired_count,
            'active_entries': len(self.cache) - expired_count,
            'memory_usage_mb': len(str(self.cache)) / (1024 * 1024)  # Rough estimate
        }

class QueryCache:
    """Specialized cache for search queries"""
    
    def __init__(self, cache: MemoryCache):
        """Initialize query cache
        
        Args:
            cache: Memory cache instance
        """
        self.cache = cache
        self.query_prefix = "query:"
    
    def _make_query_key(self, query: str, limit: int = 10, filters: Optional[Dict] = None) -> str:
        """Create cache key for query
        
        Args:
            query: Search query
            limit: Result limit
            filters: Search filters
            
        Returns:
            Cache key string
        """
        # Create deterministic key from query parameters
        key_data = {
            'query': query.lower().strip(),
            'limit': limit,
            'filters': filters or {}
        }
        key_json = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.md5(key_json.encode()).hexdigest()
        return f"{self.query_prefix}{key_hash}"
    
    def get_query_result(self, query: str, limit: int = 10, filters: Optional[Dict] = None) -> Optional[Any]:
        """Get cached query result
        
        Args:
            query: Search query
            limit: Result limit
            filters: Search filters
            
        Returns:
            Cached result or None
        """
        key = self._make_query_key(query, limit, filters)
        result = self.cache.get(key)
        
        if result:
            logger.debug(f"Cache hit for query: {query[:50]}...")
        
        return result
    
    def set_query_result(self, query: str, result: Any, limit: int = 10, 
                        filters: Optional[Dict] = None, ttl: int = 300) -> None:
        """Cache query result
        
        Args:
            query: Search query
            result: Query result to cache
            limit: Result limit
            filters: Search filters
            ttl: Time-to-live in seconds
        """
        key = self._make_query_key(query, limit, filters)
        self.cache.set(key, result, ttl)
        logger.debug(f"Cached query result: {query[:50]}...")
    
    def invalidate_all_queries(self) -> int:
        """Invalidate all cached queries
        
        Returns:
            Number of queries invalidated
        """
        query_keys = [key for key in self.cache.cache.keys() 
                     if key.startswith(self.query_prefix)]
        
        for key in query_keys:
            self.cache.delete(key)
        
        logger.info(f"Invalidated {len(query_keys)} cached queries")
        return len(query_keys)

class EmbeddingCache:
    """Cache for embedding vectors"""
    
    def __init__(self, cache: MemoryCache):
        """Initialize embedding cache
        
        Args:
            cache: Memory cache instance
        """
        self.cache = cache
        self.embedding_prefix = "embedding:"
    
    def _make_embedding_key(self, text: str) -> str:
        """Create cache key for embedding
        
        Args:
            text: Text to embed
            
        Returns:
            Cache key string
        """
        # Use hash of text as key
        text_hash = hashlib.sha256(text.encode()).hexdigest()
        return f"{self.embedding_prefix}{text_hash}"
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """Get cached embedding
        
        Args:
            text: Text to get embedding for
            
        Returns:
            Cached embedding vector or None
        """
        key = self._make_embedding_key(text)
        return self.cache.get(key)
    
    def set_embedding(self, text: str, embedding: List[float], ttl: int = 3600) -> None:
        """Cache embedding
        
        Args:
            text: Text that was embedded
            embedding: Embedding vector
            ttl: Time-to-live in seconds (1 hour default)
        """
        key = self._make_embedding_key(text)
        self.cache.set(key, embedding, ttl)

# Global cache instances
_memory_cache = MemoryCache(default_ttl=300)  # 5 minutes
_query_cache = QueryCache(_memory_cache)
_embedding_cache = EmbeddingCache(_memory_cache)

def get_memory_cache() -> MemoryCache:
    """Get global memory cache instance"""
    return _memory_cache

def get_query_cache() -> QueryCache:
    """Get global query cache instance"""
    return _query_cache

def get_embedding_cache() -> EmbeddingCache:
    """Get global embedding cache instance"""
    return _embedding_cache

def cache_query_result(ttl: int = 300):
    """Decorator to cache query results
    
    Args:
        ttl: Time-to-live in seconds
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract query parameters for cache key
            if len(args) > 1 and hasattr(args[1], 'query'):
                query_obj = args[1]
                query_text = query_obj.query
                limit = getattr(query_obj, 'limit', 10)
                
                # Check cache first
                cached_result = _query_cache.get_query_result(query_text, limit)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                _query_cache.set_query_result(query_text, result, limit, ttl=ttl)
                return result
            
            # Fallback to normal execution if can't cache
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def cache_embedding(ttl: int = 3600):
    """Decorator to cache embeddings
    
    Args:
        ttl: Time-to-live in seconds
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract text parameter for cache key
            if len(args) > 1 and isinstance(args[1], str):
                text = args[1]
                
                # Check cache first
                cached_embedding = _embedding_cache.get_embedding(text)
                if cached_embedding is not None:
                    return cached_embedding
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                if result is not None:
                    _embedding_cache.set_embedding(text, result, ttl)
                return result
            
            # Fallback to normal execution if can't cache
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
