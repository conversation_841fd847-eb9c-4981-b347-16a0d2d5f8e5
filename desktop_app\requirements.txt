# Desktop Application Requirements
# ================================

# GUI Framework
PySide6==6.6.1
PyQt6==6.6.1

# Core Dependencies (from main app)
sqlalchemy==2.0.23
alembic==1.13.1
pydantic==2.5.2
pydantic-settings==2.1.0

# Vector Database & Embeddings
chromadb==0.4.18
sentence-transformers==2.2.2

# File Processing
python-magic==0.4.27
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2
python-pptx==0.6.23

# System Integration
psutil==5.9.6
watchdog==3.0.0
pywin32==311; sys_platform == "win32"

# Packaging & Distribution
PyInstaller==6.3.0
auto-py-to-exe==2.41.0

# Configuration
PyYAML==6.0.1
python-dotenv==1.0.0

# Utilities
requests==2.31.0
rich==13.7.0
click==8.1.7

# Subscription Management
cryptography==41.0.8
jwt==1.3.1
PyJWT==2.8.0

# Performance & Monitoring
memory-profiler==0.61.0
line-profiler==4.1.1

# Development & Testing
pytest==7.4.3
pytest-qt==4.2.0
pytest-mock==3.12.0
