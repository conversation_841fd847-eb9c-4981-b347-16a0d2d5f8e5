"""
Security and authentication system for the semantic filesystem API
"""

import os
import jwt
import time
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from functools import wraps
from collections import defaultdict, deque

from fastapi import HTT<PERSON>Exception, Request, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from .config import settings

logger = logging.getLogger(__name__)

# Security configuration
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7


class SecuritySettings:
    """Security configuration settings"""
    
    def __init__(self):
        # JWT settings
        self.jwt_secret_key = os.getenv("JWT_SECRET_KEY", self._generate_secret_key())
        self.jwt_algorithm = ALGORITHM
        self.access_token_expire_minutes = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", ACCESS_TOKEN_EXPIRE_MINUTES))
        self.refresh_token_expire_days = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", REFRESH_TOKEN_EXPIRE_DAYS))
        
        # API Key settings
        self.api_keys = self._load_api_keys()
        
        # Rate limiting settings
        self.rate_limit_enabled = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
        self.rate_limit_requests = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
        self.rate_limit_window = int(os.getenv("RATE_LIMIT_WINDOW", "60"))  # seconds
        
        # Security headers
        self.security_headers_enabled = os.getenv("SECURITY_HEADERS_ENABLED", "true").lower() == "true"
        
        # Authentication settings
        self.auth_enabled = os.getenv("AUTH_ENABLED", "false").lower() == "true"
        self.require_auth_for_read = os.getenv("REQUIRE_AUTH_FOR_READ", "false").lower() == "true"
        self.require_auth_for_write = os.getenv("REQUIRE_AUTH_FOR_WRITE", "true").lower() == "true"
    
    def _generate_secret_key(self) -> str:
        """Generate a secure secret key"""
        return secrets.token_urlsafe(32)
    
    def _load_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """Load API keys from environment or configuration"""
        api_keys = {}
        
        # Load from environment variable (comma-separated key:name pairs)
        keys_env = os.getenv("API_KEYS", "")
        if keys_env:
            for key_pair in keys_env.split(","):
                if ":" in key_pair:
                    key, name = key_pair.strip().split(":", 1)
                    api_keys[key] = {
                        "name": name,
                        "created_at": datetime.utcnow(),
                        "permissions": ["read", "write"]
                    }
        
        # Generate a default API key if none exist
        if not api_keys:
            default_key = secrets.token_urlsafe(32)
            api_keys[default_key] = {
                "name": "default",
                "created_at": datetime.utcnow(),
                "permissions": ["read", "write"]
            }
            logger.warning(f"Generated default API key: {default_key}")
        
        return api_keys


# Global security settings
security_settings = SecuritySettings()


class TokenData(BaseModel):
    """Token data model"""
    username: Optional[str] = None
    permissions: List[str] = []


class User(BaseModel):
    """User model"""
    username: str
    permissions: List[str] = []
    created_at: datetime
    last_login: Optional[datetime] = None


class RateLimiter:
    """Simple in-memory rate limiter"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(deque)
    
    def is_allowed(self, client_id: str) -> bool:
        """Check if request is allowed for client"""
        now = time.time()
        client_requests = self.requests[client_id]
        
        # Remove old requests outside the window
        while client_requests and client_requests[0] <= now - self.window_seconds:
            client_requests.popleft()
        
        # Check if under limit
        if len(client_requests) >= self.max_requests:
            return False
        
        # Add current request
        client_requests.append(now)
        return True
    
    def get_stats(self, client_id: str) -> Dict[str, Any]:
        """Get rate limiting stats for client"""
        now = time.time()
        client_requests = self.requests[client_id]
        
        # Clean old requests
        while client_requests and client_requests[0] <= now - self.window_seconds:
            client_requests.popleft()
        
        return {
            "requests_made": len(client_requests),
            "requests_remaining": max(0, self.max_requests - len(client_requests)),
            "window_seconds": self.window_seconds,
            "reset_time": now + self.window_seconds if client_requests else now
        }


# Global rate limiter
rate_limiter = RateLimiter(
    max_requests=security_settings.rate_limit_requests,
    window_seconds=security_settings.rate_limit_window
)


class SecurityManager:
    """Manages authentication and security"""
    
    def __init__(self):
        self.security_scheme = HTTPBearer(auto_error=False)
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=security_settings.access_token_expire_minutes)
        to_encode.update({"exp": expire, "type": "access"})
        
        return jwt.encode(
            to_encode,
            security_settings.jwt_secret_key,
            algorithm=security_settings.jwt_algorithm
        )
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=security_settings.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        
        return jwt.encode(
            to_encode,
            security_settings.jwt_secret_key,
            algorithm=security_settings.jwt_algorithm
        )
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(
                token,
                security_settings.jwt_secret_key,
                algorithms=[security_settings.jwt_algorithm]
            )
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.JWTError as e:
            logger.warning(f"Token verification failed: {e}")
            return None
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify API key"""
        if api_key in security_settings.api_keys:
            return security_settings.api_keys[api_key]
        return None
    
    def get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting"""
        # Use API key if present
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{hashlib.sha256(api_key.encode()).hexdigest()[:16]}"
        
        # Use IP address as fallback
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}"


# Global security manager
security_manager = SecurityManager()


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_manager.security_scheme)
) -> Optional[User]:
    """Get current authenticated user"""
    
    if not security_settings.auth_enabled:
        # Return a default user when auth is disabled
        return User(
            username="anonymous",
            permissions=["read", "write"],
            created_at=datetime.utcnow()
        )
    
    # Check API key first
    api_key = request.headers.get("X-API-Key")
    if api_key:
        key_data = security_manager.verify_api_key(api_key)
        if key_data:
            return User(
                username=key_data["name"],
                permissions=key_data["permissions"],
                created_at=key_data["created_at"]
            )
    
    # Check JWT token
    if credentials:
        token_data = security_manager.verify_token(credentials.credentials)
        if token_data:
            return User(
                username=token_data.get("sub", "unknown"),
                permissions=token_data.get("permissions", []),
                created_at=datetime.utcnow()
            )
    
    return None


def require_auth(permissions: List[str] = None):
    """Decorator to require authentication with specific permissions"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request and user from kwargs
            request = kwargs.get("request")
            user = kwargs.get("current_user")
            
            if not user and security_settings.auth_enabled:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Check permissions
            if permissions and user:
                if not any(perm in user.permissions for perm in permissions):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Insufficient permissions"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware"""
    if not security_settings.rate_limit_enabled:
        return await call_next(request)
    
    client_id = security_manager.get_client_id(request)
    
    if not rate_limiter.is_allowed(client_id):
        stats = rate_limiter.get_stats(client_id)
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded",
            headers={
                "X-RateLimit-Limit": str(security_settings.rate_limit_requests),
                "X-RateLimit-Remaining": str(stats["requests_remaining"]),
                "X-RateLimit-Reset": str(int(stats["reset_time"]))
            }
        )
    
    response = await call_next(request)
    
    # Add rate limit headers to response
    stats = rate_limiter.get_stats(client_id)
    response.headers["X-RateLimit-Limit"] = str(security_settings.rate_limit_requests)
    response.headers["X-RateLimit-Remaining"] = str(stats["requests_remaining"])
    response.headers["X-RateLimit-Reset"] = str(int(stats["reset_time"]))
    
    return response


def add_security_headers(response):
    """Add security headers to response"""
    if not security_settings.security_headers_enabled:
        return response
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com"
    
    return response
