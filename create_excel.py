#!/usr/bin/env python3
"""
Create a sample Excel file for testing
"""

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill
from datetime import datetime, date

# Create a new workbook
wb = openpyxl.Workbook()

# Get the active worksheet
ws = wb.active
ws.title = "Employee Performance"

# Add headers
headers = ['Employee ID', 'Name', 'Department', 'Performance Score', 'Salary', 'Hire Date', 'Status']
for col, header in enumerate(headers, 1):
    cell = ws.cell(row=1, column=col, value=header)
    cell.font = Font(bold=True)
    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

# Add employee data
employees = [
    ['EMP001', '<PERSON>', 'Engineering', 92, 85000, date(2022, 3, 15), 'Active'],
    ['EMP002', '<PERSON>', 'Marketing', 88, 72000, date(2021, 7, 22), 'Active'],
    ['<PERSON>MP003', '<PERSON>', 'Engineering', 95, 90000, date(2020, 1, 10), 'Active'],
    ['EMP004', '<PERSON>', 'Sales', 87, 68000, date(2023, 4, 5), 'Active'],
    ['EMP005', '<PERSON>', 'HR', 91, 75000, date(2022, 9, 18), 'Active'],
    ['EMP006', 'Emily Brown', 'Engineering', 89, 82000, date(2021, 11, 30), 'Active'],
    ['EMP007', '<PERSON> <PERSON>', 'Finance', 93, 78000, date(2020, 6, 12), 'Active'],
    ['EMP008', 'Jennifer Lee', 'Marketing', 86, 70000, date(2023, 2, 28), 'Active'],
    ['EMP009', 'Michael Garcia', 'Sales', 90, 73000, date(2022, 8, 14), 'Active'],
    ['EMP010', 'Amanda White', 'Engineering', 94, 87000, date(2021, 5, 3), 'Active']
]

for row, employee in enumerate(employees, 2):
    for col, value in enumerate(employee, 1):
        ws.cell(row=row, column=col, value=value)

# Add a summary sheet
summary_ws = wb.create_sheet("Summary")
summary_ws['A1'] = "EMPLOYEE PERFORMANCE SUMMARY"
summary_ws['A1'].font = Font(bold=True, size=14)

summary_ws['A3'] = "Report Date:"
summary_ws['B3'] = "April 2024"

summary_ws['A4'] = "Total Employees:"
summary_ws['B4'] = len(employees)

summary_ws['A5'] = "Average Performance Score:"
summary_ws['B5'] = sum(emp[3] for emp in employees) / len(employees)

summary_ws['A6'] = "Average Salary:"
summary_ws['B6'] = sum(emp[4] for emp in employees) / len(employees)

summary_ws['A8'] = "Department Breakdown:"
summary_ws['A9'] = "Engineering: 4 employees"
summary_ws['A10'] = "Marketing: 2 employees"
summary_ws['A11'] = "Sales: 2 employees"
summary_ws['A12'] = "HR: 1 employee"
summary_ws['A13'] = "Finance: 1 employee"

summary_ws['A15'] = "Notes:"
summary_ws['A16'] = "This report was generated in April 2024 for quarterly review."
summary_ws['A17'] = "All performance scores are based on Q1 2024 evaluations."
summary_ws['A18'] = "Salary data is confidential and for internal use only."

# Save the workbook
wb.save('test_documents/employee_performance.xlsx')
print("Excel file created successfully!")
