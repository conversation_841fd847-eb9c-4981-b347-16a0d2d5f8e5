"""
Test script for desktop semantic engine
Tests massive scale indexing, performance, and subscription management
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine, IndexingProgress
from core.subscription_manager import SubscriptionManager


def create_test_files(test_dir: Path, num_files: int = 100) -> None:
    """Create test files for indexing"""
    print(f"Creating {num_files} test files in {test_dir}")
    
    # Create different file types
    file_types = [
        ('.txt', 'This is a text document about {}. It contains important information.'),
        ('.py', '# Python script for {}\ndef main():\n    print("Hello world")\n\nif __name__ == "__main__":\n    main()'),
        ('.md', '# {} Documentation\n\nThis is a markdown file with information about the project.'),
        ('.json', '{{"name": "{}", "type": "test", "data": ["item1", "item2", "item3"]}}'),
        ('.csv', 'name,value,category\n{},100,test\nitem2,200,sample\nitem3,300,demo'),
    ]
    
    for i in range(num_files):
        file_type, content_template = file_types[i % len(file_types)]
        filename = f"test_file_{i:04d}{file_type}"
        file_path = test_dir / filename
        
        content = content_template.format(f"test_item_{i}")
        file_path.write_text(content, encoding='utf-8')
        
        # Create some subdirectories
        if i % 20 == 0:
            subdir = test_dir / f"subdir_{i // 20}"
            subdir.mkdir(exist_ok=True)
            
            # Add files in subdirectory
            for j in range(5):
                sub_file = subdir / f"sub_file_{j}.txt"
                sub_file.write_text(f"Subdirectory file {j} in folder {i // 20}")
    
    print(f"✅ Created {num_files} test files")


def test_subscription_manager():
    """Test subscription management system"""
    print("\n🔐 Testing Subscription Manager...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        app_data_dir = Path(temp_dir) / "app_data"
        app_data_dir.mkdir()
        
        # Initialize subscription manager
        sub_manager = SubscriptionManager(app_data_dir)
        
        # Test 1: Default free tier
        print("Test 1: Default subscription")
        info = sub_manager.get_subscription_info()
        print(f"  Tier: {info['tier']}")
        print(f"  Status: {info['status']}")
        print(f"  Max files: {info['limits']['max_files']}")
        assert info['tier'] == 'free'
        assert info['limits']['max_files'] == 1000
        print("  ✅ Free tier working")
        
        # Test 2: Start trial
        print("\nTest 2: Starting trial")
        trial_started = sub_manager.start_trial()
        assert trial_started == True
        
        info = sub_manager.get_subscription_info()
        print(f"  Tier: {info['tier']}")
        print(f"  Status: {info['status']}")
        print(f"  Days remaining: {info['days_remaining']}")
        assert info['tier'] == 'pro'
        assert info['status'] == 'trial'
        assert info['days_remaining'] >= 6  # Should be 6 or 7 days depending on timing
        print("  ✅ Trial activation working")
        
        # Test 3: Feature access
        print("\nTest 3: Feature access")
        has_unlimited = sub_manager.check_feature_access('unlimited_indexing')
        has_basic = sub_manager.check_feature_access('basic_search')
        print(f"  Unlimited indexing: {has_unlimited}")
        print(f"  Basic search: {has_basic}")
        assert has_unlimited == True  # Pro trial should have unlimited
        print("  ✅ Feature access working")
        
        # Test 4: Indexing limits
        print("\nTest 4: Indexing limits")
        limits = sub_manager.check_indexing_limits(500, 0.5)
        print(f"  Files allowed: {limits['files_allowed']}")
        print(f"  Size allowed: {limits['size_allowed']}")
        print(f"  Files usage: {limits['files_usage_percent']:.1f}%")
        assert limits['files_allowed'] == True
        assert limits['size_allowed'] == True
        print("  ✅ Indexing limits working")
        
        print("🎉 Subscription Manager tests PASSED!")


def test_desktop_engine_basic():
    """Test basic desktop engine functionality"""
    print("\n🚀 Testing Desktop Engine - Basic Functions...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_files"
        test_dir.mkdir()
        
        # Create test files
        create_test_files(test_dir, 50)
        
        # Initialize engine
        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir()
        
        engine = DesktopSemanticEngine(config_dir)
        
        # Test 1: File discovery
        print("Test 1: File discovery")
        files_found = list(engine._walk_files(test_dir))
        print(f"  Found {len(files_found)} files")
        assert len(files_found) > 50  # Should find main files + subdirectory files
        print("  ✅ File discovery working")
        
        # Test 2: File filtering
        print("\nTest 2: File filtering")
        indexable_files = [f for f in files_found if engine._should_index_file(f)]
        print(f"  Indexable files: {len(indexable_files)}")
        assert len(indexable_files) > 0
        print("  ✅ File filtering working")
        
        # Test 3: Scope estimation
        print("\nTest 3: Scope estimation")
        scope = engine.estimate_indexing_scope([test_dir])
        print(f"  Total files: {scope['total_files']}")
        print(f"  Total size: {scope['total_size_gb']:.3f} GB")
        print(f"  Estimated time: {scope['estimated_time_hours']:.2f} hours")
        assert scope['total_files'] > 0
        print("  ✅ Scope estimation working")
        
        print("🎉 Desktop Engine basic tests PASSED!")


def test_desktop_engine_indexing():
    """Test actual indexing functionality"""
    print("\n📚 Testing Desktop Engine - Indexing...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_files"
        test_dir.mkdir()
        
        # Create test files
        create_test_files(test_dir, 20)  # Smaller set for actual indexing
        
        # Initialize engine
        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir()
        
        engine = DesktopSemanticEngine(config_dir)
        
        # Progress tracking
        progress_updates = []
        
        def progress_callback(progress: IndexingProgress):
            progress_updates.append({
                'processed': progress.processed_files,
                'total': progress.total_files,
                'percentage': progress.progress_percentage,
                'current_file': progress.current_file
            })
            print(f"  Progress: {progress.processed_files}/{progress.total_files} ({progress.progress_percentage:.1f}%)")
        
        # Test 1: Start indexing
        print("Test 1: Starting indexing")
        indexing_started = engine.start_full_indexing([test_dir], progress_callback)
        assert indexing_started == True
        print("  ✅ Indexing started")
        
        # Test 2: Wait for completion
        print("\nTest 2: Waiting for indexing completion")
        start_time = time.time()
        timeout = 60  # 1 minute timeout
        
        while engine.is_indexing and (time.time() - start_time) < timeout:
            time.sleep(1)
            stats = engine.get_indexing_stats()
            if stats['current_progress'].processed_files > 0:
                print(f"  Processed: {stats['current_progress'].processed_files} files")
        
        if engine.is_indexing:
            print("  ⚠️  Indexing timeout - stopping")
            engine.stop_indexing()
            time.sleep(2)
        
        # Test 3: Check results
        print("\nTest 3: Checking indexing results")
        stats = engine.get_indexing_stats()
        print(f"  Total indexed files: {stats['total_indexed_files']}")
        print(f"  Memory usage: {stats['memory_usage_gb']:.2f} GB")
        
        if stats['total_indexed_files'] > 0:
            print("  ✅ Files were indexed successfully")
        else:
            print("  ⚠️  No files were indexed - may need more time")
        
        # Test 4: Search functionality
        if stats['total_indexed_files'] > 0:
            print("\nTest 4: Search functionality")
            try:
                results = engine.search_files("test document", limit=5)
                print(f"  Search results: {len(results)}")
                if results:
                    print(f"  First result: {results[0].get('filename', 'N/A')}")
                    print("  ✅ Search working")
                else:
                    print("  ⚠️  No search results found")
            except Exception as e:
                print(f"  ⚠️  Search error: {e}")
        
        print(f"🎉 Desktop Engine indexing tests completed!")
        print(f"   Progress updates received: {len(progress_updates)}")


def test_performance_stress():
    """Test performance with larger dataset"""
    print("\n⚡ Testing Performance - Stress Test...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "stress_test"
        test_dir.mkdir()
        
        # Create larger test dataset
        print("Creating stress test dataset...")
        create_test_files(test_dir, 500)  # 500 files for stress test
        
        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir()
        
        engine = DesktopSemanticEngine(config_dir)
        
        # Test 1: Memory usage before indexing
        print("Test 1: Initial memory usage")
        import psutil
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024**2)  # MB
        print(f"  Initial memory: {initial_memory:.1f} MB")
        
        # Test 2: Scope estimation performance
        print("\nTest 2: Scope estimation performance")
        start_time = time.time()
        scope = engine.estimate_indexing_scope([test_dir])
        estimation_time = time.time() - start_time
        print(f"  Estimation time: {estimation_time:.2f} seconds")
        print(f"  Files to index: {scope['total_files']}")
        print(f"  Estimated indexing time: {scope['estimated_time_hours']:.2f} hours")
        
        # Test 3: Memory usage after estimation
        current_memory = process.memory_info().rss / (1024**2)  # MB
        print(f"  Memory after estimation: {current_memory:.1f} MB")
        print(f"  Memory increase: {current_memory - initial_memory:.1f} MB")
        
        if estimation_time < 10:  # Should be fast
            print("  ✅ Scope estimation performance good")
        else:
            print("  ⚠️  Scope estimation slow")
        
        # Test 4: File discovery performance
        print("\nTest 3: File discovery performance")
        start_time = time.time()
        files = list(engine._walk_files(test_dir))
        discovery_time = time.time() - start_time
        print(f"  Discovery time: {discovery_time:.2f} seconds")
        print(f"  Files discovered: {len(files)}")
        print(f"  Files per second: {len(files) / discovery_time:.1f}")
        
        if discovery_time < 5:  # Should be very fast
            print("  ✅ File discovery performance good")
        else:
            print("  ⚠️  File discovery slow")
        
        print("🎉 Performance stress tests completed!")


def main():
    """Run all tests"""
    print("🧪 DESKTOP ENGINE COMPREHENSIVE TESTING")
    print("=" * 50)
    
    try:
        # Test 1: Subscription Management
        test_subscription_manager()
        
        # Test 2: Basic Engine Functions
        test_desktop_engine_basic()
        
        # Test 3: Actual Indexing
        test_desktop_engine_indexing()
        
        # Test 4: Performance Stress Test
        test_performance_stress()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS COMPLETED!")
        print("✅ Desktop engine is ready for GUI development")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
