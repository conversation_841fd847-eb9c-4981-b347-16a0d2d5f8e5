"""
EXTENSION SUPPORT UPDATER - Adds comprehensive file type support
Updates both desktop_engine.py and content_extractor.py with missing extensions
"""

import sys
from pathlib import Path

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))


def get_comprehensive_extensions():
    """Get comprehensive list of text-based file extensions"""
    return {
        # Documents & Text
        '.txt', '.md', '.markdown', '.mdown', '.mkd', '.mdx',
        '.pdf', '.docx', '.doc', '.rtf', '.odt', '.pages',
        '.xlsx', '.xls', '.csv', '.ods', '.numbers',
        '.pptx', '.ppt', '.odp', '.key',
        
        # Programming Languages
        '.py', '.pyw', '.pyi', '.pyx',  # Python
        '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs',  # JavaScript/TypeScript
        '.html', '.htm', '.xhtml', '.shtml',  # HTML
        '.css', '.scss', '.sass', '.less', '.styl',  # CSS
        '.java', '.class', '.jar',  # Java
        '.cpp', '.cxx', '.cc', '.c', '.h', '.hpp', '.hxx',  # C/C++
        '.cs', '.vb', '.fs',  # .NET languages
        '.php', '.php3', '.php4', '.php5', '.phtml',  # PHP
        '.rb', '.rbw', '.rake', '.gemspec',  # Ruby
        '.go', '.mod', '.sum',  # Go
        '.rs', '.rlib',  # Rust
        '.swift',  # Swift
        '.kt', '.kts',  # Kotlin
        '.scala', '.sc',  # Scala
        '.clj', '.cljs', '.cljc', '.edn',  # Clojure
        '.hs', '.lhs',  # Haskell
        '.ml', '.mli', '.mll', '.mly',  # OCaml
        '.r', '.R', '.rmd', '.rnw',  # R
        '.m', '.mm',  # Objective-C
        '.pl', '.pm', '.t', '.pod',  # Perl
        '.lua',  # Lua
        '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh',  # Shell scripts
        '.ps1', '.psm1', '.psd1',  # PowerShell
        '.bat', '.cmd',  # Windows batch
        '.asm', '.s',  # Assembly
        '.f', '.f90', '.f95', '.f03', '.f08',  # Fortran
        '.pas', '.pp', '.inc',  # Pascal
        '.d',  # D
        '.nim', '.nims',  # Nim
        '.cr',  # Crystal
        '.ex', '.exs',  # Elixir
        '.erl', '.hrl',  # Erlang
        '.dart',  # Dart
        '.v', '.vh', '.sv', '.svh',  # Verilog/SystemVerilog
        '.vhd', '.vhdl',  # VHDL
        
        # Data & Configuration
        '.json', '.jsonl', '.ndjson', '.json5',
        '.xml', '.xsd', '.xsl', '.xslt', '.dtd',
        '.yaml', '.yml',
        '.toml',
        '.ini', '.cfg', '.conf', '.config',
        '.properties',
        '.env', '.envrc',
        '.gitignore', '.gitattributes', '.gitmodules',
        '.dockerignore', '.dockerfile',
        '.makefile', '.mk',
        '.cmake', '.cmakelist',
        '.gradle', '.gradlew',
        '.maven', '.pom',
        '.sbt',
        '.cabal',
        '.cargo',
        '.package', '.lock',
        '.requirements',
        '.pipfile', '.poetry',
        '.gemfile',
        '.composer',
        '.npm', '.yarn',
        
        # Markup & Documentation
        '.rst', '.rest', '.restx',
        '.asciidoc', '.adoc', '.asc',
        '.tex', '.latex', '.ltx', '.sty', '.cls',
        '.wiki', '.mediawiki',
        '.org',
        '.pod',
        '.man', '.1', '.2', '.3', '.4', '.5', '.6', '.7', '.8', '.9',
        
        # Log & Data files
        '.log', '.logs',
        '.out', '.err',
        '.trace', '.debug',
        '.dump', '.dmp',
        '.backup', '.bak', '.old', '.orig',
        '.tmp', '.temp',
        '.cache',
        '.pid',
        '.lock',
        
        # Database & Query
        '.sql', '.mysql', '.pgsql', '.sqlite', '.db',
        '.cql', '.cypher',
        '.sparql',
        '.graphql', '.gql',
        
        # Web & API
        '.api', '.rest',
        '.wsdl', '.wadl',
        '.raml', '.swagger',
        '.openapi',
        '.postman',
        
        # Scientific & Data
        '.csv', '.tsv', '.psv',
        '.parquet', '.avro', '.orc',
        '.hdf5', '.h5',
        '.nc', '.cdf',
        '.fits', '.fit',
        '.mat',
        '.npz', '.npy',
        
        # Specialized formats
        '.ics', '.vcf', '.vcard',
        '.gpx', '.kml', '.kmz',
        '.geojson', '.topojson',
        '.svg', '.svgz',
        '.eps', '.ps',
        '.dot', '.gv',
        '.puml', '.plantuml',
        '.mermaid',
        
        # No extension files (common names)
        '.readme', '.license', '.changelog', '.authors', '.contributors',
        '.copying', '.install', '.news', '.todo', '.fixme',
        '.version', '.release', '.history'
    }


def get_comprehensive_mime_mappings():
    """Get comprehensive MIME type mappings"""
    return {
        # Text files
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.markdown': 'text/markdown',
        '.mdown': 'text/markdown',
        '.mkd': 'text/markdown',
        '.mdx': 'text/markdown',
        '.rst': 'text/x-rst',
        '.rest': 'text/x-rst',
        '.asciidoc': 'text/asciidoc',
        '.adoc': 'text/asciidoc',
        '.tex': 'text/x-tex',
        '.latex': 'text/x-tex',
        
        # Programming languages
        '.py': 'text/x-python',
        '.pyw': 'text/x-python',
        '.pyi': 'text/x-python',
        '.js': 'application/javascript',
        '.jsx': 'application/javascript',
        '.ts': 'application/typescript',
        '.tsx': 'application/typescript',
        '.mjs': 'application/javascript',
        '.cjs': 'application/javascript',
        '.html': 'text/html',
        '.htm': 'text/html',
        '.xhtml': 'application/xhtml+xml',
        '.css': 'text/css',
        '.scss': 'text/x-scss',
        '.sass': 'text/x-sass',
        '.less': 'text/x-less',
        '.java': 'text/x-java-source',
        '.cpp': 'text/x-c++src',
        '.cxx': 'text/x-c++src',
        '.cc': 'text/x-c++src',
        '.c': 'text/x-csrc',
        '.h': 'text/x-chdr',
        '.hpp': 'text/x-c++hdr',
        '.cs': 'text/x-csharp',
        '.php': 'application/x-php',
        '.rb': 'application/x-ruby',
        '.go': 'text/x-go',
        '.rs': 'text/x-rust',
        '.swift': 'text/x-swift',
        '.kt': 'text/x-kotlin',
        '.scala': 'text/x-scala',
        '.clj': 'text/x-clojure',
        '.hs': 'text/x-haskell',
        '.ml': 'text/x-ocaml',
        '.r': 'text/x-r',
        '.lua': 'text/x-lua',
        '.sh': 'application/x-sh',
        '.bash': 'application/x-sh',
        '.ps1': 'application/x-powershell',
        '.bat': 'application/x-msdos-program',
        '.sql': 'application/sql',
        
        # Data formats
        '.json': 'application/json',
        '.jsonl': 'application/jsonlines',
        '.json5': 'application/json5',
        '.xml': 'application/xml',
        '.yaml': 'application/x-yaml',
        '.yml': 'application/x-yaml',
        '.toml': 'application/toml',
        '.ini': 'text/plain',
        '.cfg': 'text/plain',
        '.conf': 'text/plain',
        '.config': 'text/plain',
        '.properties': 'text/plain',
        '.env': 'text/plain',
        '.csv': 'text/csv',
        '.tsv': 'text/tab-separated-values',
        '.log': 'text/plain',
        
        # Documents
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        
        # Default for unknown extensions
        '.readme': 'text/plain',
        '.license': 'text/plain',
        '.changelog': 'text/plain',
        '.makefile': 'text/plain',
        '.dockerfile': 'text/plain',
    }


def update_desktop_engine():
    """Update desktop_engine.py with comprehensive extensions"""
    engine_path = Path(__file__).parent / "core" / "desktop_engine.py"
    
    if not engine_path.exists():
        print(f"❌ Desktop engine not found at: {engine_path}")
        return False
    
    # Read current file
    with open(engine_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the supported_extensions section
    start_marker = "self.supported_extensions = {"
    end_marker = "}"
    
    start_idx = content.find(start_marker)
    if start_idx == -1:
        print("❌ Could not find supported_extensions in desktop_engine.py")
        return False
    
    # Find the end of the set
    brace_count = 0
    end_idx = start_idx + len(start_marker)
    for i, char in enumerate(content[end_idx:], end_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            if brace_count == 0:
                end_idx = i + 1
                break
            brace_count -= 1
    
    # Generate new extensions set
    extensions = get_comprehensive_extensions()
    extensions_str = "self.supported_extensions = {\n"
    
    # Group extensions by category
    categories = {
        "Documents & Text": ['.txt', '.md', '.markdown', '.mdown', '.mkd', '.mdx', '.rst', '.asciidoc', '.adoc', '.tex', '.latex'],
        "Office Documents": ['.pdf', '.docx', '.doc', '.rtf', '.odt', '.pages', '.xlsx', '.xls', '.csv', '.ods', '.numbers', '.pptx', '.ppt', '.odp', '.key'],
        "Python": ['.py', '.pyw', '.pyi', '.pyx'],
        "JavaScript/TypeScript": ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs'],
        "Web": ['.html', '.htm', '.xhtml', '.shtml', '.css', '.scss', '.sass', '.less', '.styl'],
        "Systems Programming": ['.c', '.h', '.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.rs', '.rlib', '.go', '.mod', '.sum'],
        "JVM Languages": ['.java', '.class', '.jar', '.scala', '.sc', '.kt', '.kts', '.clj', '.cljs', '.cljc'],
        "Other Languages": ['.cs', '.vb', '.fs', '.php', '.rb', '.swift', '.hs', '.ml', '.r', '.lua', '.pl', '.pm'],
        "Shell & Scripts": ['.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh', '.ps1', '.psm1', '.psd1', '.bat', '.cmd'],
        "Data & Config": ['.json', '.jsonl', '.json5', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.config', '.properties', '.env'],
        "Database": ['.sql', '.mysql', '.pgsql', '.sqlite', '.db', '.cql', '.cypher', '.sparql', '.graphql', '.gql'],
        "Logs & Debug": ['.log', '.logs', '.out', '.err', '.trace', '.debug', '.dump', '.dmp'],
        "Special Files": ['.readme', '.license', '.changelog', '.authors', '.makefile', '.dockerfile', '.gitignore']
    }
    
    for category, exts in categories.items():
        extensions_str += f"            # {category}\n"
        line_exts = []
        for ext in exts:
            if ext in extensions:
                line_exts.append(f"'{ext}'")
        
        # Group extensions in lines of reasonable length
        while line_exts:
            line = "            "
            current_line = []
            while line_exts and len(line + line_exts[0] + ", ") < 80:
                current_line.append(line_exts.pop(0))
            
            if current_line:
                extensions_str += line + ", ".join(current_line) + ",\n"
        
        extensions_str += "\n"
    
    extensions_str += "        }"
    
    # Replace the old extensions with new ones
    new_content = content[:start_idx] + extensions_str + content[end_idx:]
    
    # Write back to file
    with open(engine_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ Updated desktop_engine.py with {len(extensions)} supported extensions")
    return True


def update_content_extractor():
    """Update content_extractor.py with comprehensive MIME mappings"""
    extractor_path = Path(__file__).parent.parent / "semantic_fs" / "content_extractor.py"
    
    if not extractor_path.exists():
        print(f"❌ Content extractor not found at: {extractor_path}")
        return False
    
    # Read current file
    with open(extractor_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the _get_mime_from_extension method
    method_start = content.find("def _get_mime_from_extension(self, extension: str)")
    if method_start == -1:
        print("❌ Could not find _get_mime_from_extension method")
        return False
    
    # Find the extension_map
    map_start = content.find("extension_map = {", method_start)
    if map_start == -1:
        print("❌ Could not find extension_map")
        return False
    
    # Find the end of the map
    brace_count = 0
    map_end = map_start + len("extension_map = {")
    for i, char in enumerate(content[map_end:], map_end):
        if char == '{':
            brace_count += 1
        elif char == '}':
            if brace_count == 0:
                map_end = i + 1
                break
            brace_count -= 1
    
    # Generate new MIME mappings
    mime_mappings = get_comprehensive_mime_mappings()
    mappings_str = "extension_map = {\n"
    
    for ext, mime_type in sorted(mime_mappings.items()):
        mappings_str += f"            '{ext}': '{mime_type}',\n"
    
    mappings_str += "        }"
    
    # Replace the old mappings with new ones
    new_content = content[:map_start] + mappings_str + content[map_end:]
    
    # Write back to file
    with open(extractor_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ Updated content_extractor.py with {len(mime_mappings)} MIME mappings")
    return True


def main():
    """Main function"""
    print("🚀 EXTENSION SUPPORT UPDATER")
    print("Adding comprehensive file type support...")
    print("=" * 50)
    
    success = True
    
    # Update desktop engine
    print("📝 Updating desktop_engine.py...")
    if not update_desktop_engine():
        success = False
    
    # Update content extractor
    print("📝 Updating content_extractor.py...")
    if not update_content_extractor():
        success = False
    
    if success:
        print("\n🎉 SUCCESS! File type support has been greatly expanded!")
        print("💡 Now run the live scanner again to see the improvement!")
    else:
        print("\n❌ Some updates failed. Check the error messages above.")


if __name__ == "__main__":
    main()
