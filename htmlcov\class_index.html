<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">22%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d___init___py.html">semantic_fs\__init__.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>184</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="43 184">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t16">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t16"><data value='MemoryCache'>MemoryCache</data></a></td>
                <td>30</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="2 30">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t124">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t124"><data value='QueryCache'>QueryCache</data></a></td>
                <td>19</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="2 19">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t206">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t206"><data value='EmbeddingCache'>EmbeddingCache</data></a></td>
                <td>8</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 8">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="47 71">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>566</td>
                <td>566</td>
                <td>0</td>
                <td class="right" data-ratio="0 566">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t14">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t14"><data value='Settings'>Settings</data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t104">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t104"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>92</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="76 92">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t46">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t46"><data value='ContentExtractor'>ContentExtractor</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="36 44">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t31">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t31"><data value='SemanticFilesystem'>SemanticFilesystem</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html">semantic_fs\database.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="15 34">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t17">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t17"><data value='EmbeddingEngine'>EmbeddingEngine</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t5">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t5"><data value='SemanticFilesystemError'>SemanticFilesystemError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t13">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t13"><data value='FileProcessingError'>FileProcessingError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t27">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t27"><data value='ContentExtractionError'>ContentExtractionError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t38">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t38"><data value='EmbeddingError'>EmbeddingError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t52">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t52"><data value='SearchError'>SearchError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t66">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t66"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t80">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t80"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t94">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t94"><data value='ValidationError'>ValidationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t108">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t108"><data value='DependencyError'>DependencyError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t122">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t122"><data value='FileSystemError'>FileSystemError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t138">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t138"><data value='LLMError'>LLMError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t154">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t154"><data value='CacheError'>CacheError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t171">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t171"><data value='ErrorSeverity'>ErrorSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t179">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t179"><data value='ErrorRecovery'>ErrorRecovery</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>87</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="49 87">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t38">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t38"><data value='SemanticFileHandler'>SemanticFileHandler</data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t210">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t210"><data value='FileMonitor'>FileMonitor</data></a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="43 47">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t22">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t22"><data value='LLMProcessor'>LLMProcessor</data></a></td>
                <td>180</td>
                <td>180</td>
                <td>0</td>
                <td class="right" data-ratio="0 180">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="24 25">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t20">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t20"><data value='StructuredFormatter'>StructuredFormatter</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t78">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t78"><data value='ColoredConsoleFormatter'>ColoredConsoleFormatter</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t118">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t118"><data value='LoggingManager'>LoggingManager</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t26">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t26"><data value='MigrationManager'>MigrationManager</data></a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t14">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t14"><data value='FileRecord'>FileRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t36">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t36"><data value='FileRelationship'>FileRelationship</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t50">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t50"><data value='SemanticQuery'>SemanticQuery</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t57">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t57"><data value='FileMetadata'>FileMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t72">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t72"><data value='SearchResult'>SearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t79">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html#t79"><data value='QueryResult'>QueryResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="61 61">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t30">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t30"><data value='SecuritySettings'>SecuritySettings</data></a></td>
                <td>25</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="21 25">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t93">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t93"><data value='TokenData'>TokenData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t99">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t99"><data value='User'>User</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t107">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t107"><data value='RateLimiter'>RateLimiter</data></a></td>
                <td>16</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="3 16">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t156">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t156"><data value='SecurityManager'>SecurityManager</data></a></td>
                <td>26</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="1 26">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>94</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="48 94">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t29">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t29"><data value='SignalManager'>SignalManager</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="32 47">68%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2605</td>
                <td>2033</td>
                <td>0</td>
                <td class="right" data-ratio="572 2605">22%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
