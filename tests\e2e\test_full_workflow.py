"""
End-to-end tests for complete workflows
"""

import pytest
import tempfile
import shutil
import time
from pathlib import Path
from unittest.mock import patch, Mock

from semantic_fs.core import SemanticFilesystem
from semantic_fs.models import SemanticQuery
from semantic_fs.file_monitor import FileMonitor


@pytest.mark.e2e
@pytest.mark.slow
class TestCompleteWorkflow:
    """Test complete end-to-end workflows"""
    
    def test_index_and_search_workflow(self, semantic_fs_with_mocks, test_files):
        """Test complete index and search workflow"""
        fs = semantic_fs_with_mocks
        
        # Index multiple files
        indexed_files = []
        for filename, filepath in test_files.items():
            with patch('semantic_fs.core.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                result = fs.index_file(str(filepath))
                if result:
                    indexed_files.append(filename)
        
        # Verify files were indexed
        assert len(indexed_files) > 0
        
        # Perform searches
        test_queries = [
            "artificial intelligence",
            "python script",
            "meeting notes",
            "quarterly report"
        ]
        
        for query_text in test_queries:
            query = SemanticQuery(query=query_text, limit=10)
            
            with patch('semantic_fs.core.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.all.return_value = []
                
                result = fs.query(query)
                
                # Verify query executed successfully
                assert result is not None
                assert result.query == query_text
                assert isinstance(result.execution_time, float)
                assert result.execution_time >= 0
    
    def test_file_monitoring_workflow(self, semantic_fs_with_mocks, temp_dir):
        """Test file monitoring workflow"""
        fs = semantic_fs_with_mocks
        
        # Create file monitor
        monitor = FileMonitor(fs)
        
        # Add watch path
        success = monitor.add_watch_path(str(temp_dir), recursive=True)
        assert success is True
        
        # Start monitoring
        success = monitor.start()
        assert success is True
        
        # Create a new file (this would trigger monitoring in real scenario)
        new_file = temp_dir / "monitored_file.txt"
        new_file.write_text("This is a monitored file for testing")
        
        # In a real scenario, the file monitor would detect this and index it
        # For testing, we'll simulate the indexing
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            result = fs.index_file(str(new_file))
        
        # Stop monitoring
        monitor.stop()
        
        # Verify monitoring worked
        assert monitor.is_running is False
    
    def test_update_file_workflow(self, semantic_fs_with_mocks, test_files):
        """Test updating an existing file workflow"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        # Index file initially
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            initial_result = fs.index_file(str(test_file))
        
        # Modify the file
        test_file.write_text("Updated content for the test document")
        
        # Re-index the file (simulating update)
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # Mock existing file record
            existing_record = Mock()
            existing_record.id = 1
            existing_record.path = str(test_file)
            existing_record.embedding_id = "existing_embedding"
            
            mock_db.query.return_value.filter.return_value.first.return_value = existing_record
            
            updated_result = fs.index_file(str(test_file))
        
        # Verify update process was called
        assert fs.embedding_engine.delete_embedding.called
        assert fs.content_extractor.extract_content.called
    
    def test_batch_indexing_workflow(self, semantic_fs_with_mocks, temp_dir):
        """Test batch indexing of multiple files"""
        fs = semantic_fs_with_mocks
        
        # Create multiple test files
        test_files = []
        for i in range(5):
            file_path = temp_dir / f"batch_file_{i}.txt"
            file_path.write_text(f"This is batch test file number {i}")
            test_files.append(file_path)
        
        # Index all files
        indexed_count = 0
        for file_path in test_files:
            with patch('semantic_fs.core.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                result = fs.index_file(str(file_path))
                if result:
                    indexed_count += 1
        
        # Verify all files were processed
        assert indexed_count == len(test_files)
    
    def test_error_recovery_workflow(self, semantic_fs_with_mocks, test_files):
        """Test error recovery in workflows"""
        fs = semantic_fs_with_mocks
        
        # Test with a file that causes content extraction error
        test_file = test_files["test_document.txt"]
        
        # Mock content extractor to fail first, then succeed
        fs.content_extractor.extract_content.side_effect = [
            Exception("Extraction failed"),
            "Successfully extracted content"
        ]
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            # First attempt should handle error gracefully
            result1 = fs.index_file(str(test_file))
            
            # Reset the side effect for second attempt
            fs.content_extractor.extract_content.side_effect = None
            fs.content_extractor.extract_content.return_value = "Successfully extracted content"
            
            # Second attempt should succeed
            result2 = fs.index_file(str(test_file))
        
        # Verify error handling worked
        assert fs.content_extractor.extract_content.call_count >= 1


@pytest.mark.e2e
@pytest.mark.slow
class TestPerformanceWorkflow:
    """Test performance-related workflows"""
    
    def test_large_file_handling(self, semantic_fs_with_mocks, temp_dir):
        """Test handling of large files"""
        fs = semantic_fs_with_mocks
        
        # Create a moderately large file (1MB)
        large_file = temp_dir / "large_file.txt"
        content = "This is a test line.\n" * 50000  # ~1MB
        large_file.write_text(content)
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            start_time = time.time()
            result = fs.index_file(str(large_file))
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Verify file was processed and timing is reasonable
            assert processing_time < 30  # Should complete within 30 seconds
    
    def test_concurrent_operations(self, semantic_fs_with_mocks, test_files):
        """Test concurrent indexing and searching"""
        fs = semantic_fs_with_mocks
        
        # This test simulates concurrent operations
        # In a real scenario, you'd use threading or asyncio
        
        operations_completed = 0
        
        # Simulate indexing operations
        for filename, filepath in test_files.items():
            with patch('semantic_fs.core.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                result = fs.index_file(str(filepath))
                if result:
                    operations_completed += 1
        
        # Simulate search operations
        queries = ["test", "document", "python", "meeting"]
        for query_text in queries:
            query = SemanticQuery(query=query_text, limit=5)
            
            with patch('semantic_fs.core.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.all.return_value = []
                
                result = fs.query(query)
                if result:
                    operations_completed += 1
        
        # Verify all operations completed
        assert operations_completed > 0
    
    def test_memory_usage_workflow(self, semantic_fs_with_mocks, temp_dir):
        """Test memory usage during operations"""
        fs = semantic_fs_with_mocks
        
        # Create multiple files to test memory usage
        files = []
        for i in range(10):
            file_path = temp_dir / f"memory_test_{i}.txt"
            file_path.write_text(f"Memory test content {i} " * 1000)
            files.append(file_path)
        
        # Index all files and monitor that operations complete
        for file_path in files:
            with patch('semantic_fs.core.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                result = fs.index_file(str(file_path))
                
                # Verify operation completed without memory issues
                assert result is not None or result is None  # Either success or handled failure


@pytest.mark.e2e
class TestIntegrationWorkflow:
    """Test integration between different components"""
    
    def test_database_migration_workflow(self):
        """Test database migration workflow"""
        from semantic_fs.migrations import get_migration_manager
        
        manager = get_migration_manager()
        
        # Test initialization
        success = manager.initialize_database()
        assert success is True
        
        # Test status check
        status = manager.get_status()
        assert isinstance(status, dict)
        assert "current_revision" in status
        assert "head_revision" in status
        assert "is_up_to_date" in status
    
    def test_logging_workflow(self):
        """Test logging system workflow"""
        from semantic_fs.logging_config import setup_logging, get_log_stats
        
        # Setup logging
        success = setup_logging(
            log_level="DEBUG",
            enable_file_logging=True,
            enable_json_logging=False
        )
        assert success is True
        
        # Get logging stats
        stats = get_log_stats()
        assert isinstance(stats, dict)
        assert "log_directory" in stats
        assert "files" in stats
    
    def test_security_workflow(self):
        """Test security system workflow"""
        from semantic_fs.security import SecurityManager, RateLimiter
        
        # Test security manager
        manager = SecurityManager()
        
        # Test token creation and verification
        data = {"sub": "test_user", "permissions": ["read"]}
        token = manager.create_access_token(data)
        assert isinstance(token, str)
        
        decoded = manager.verify_token(token)
        assert decoded is not None
        assert decoded["sub"] == "test_user"
        
        # Test rate limiter
        limiter = RateLimiter(max_requests=5, window_seconds=60)
        
        # Test rate limiting
        for i in range(5):
            assert limiter.is_allowed("test_client") is True
        
        assert limiter.is_allowed("test_client") is False
