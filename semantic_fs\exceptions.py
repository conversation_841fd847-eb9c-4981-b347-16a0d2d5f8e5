"""
Custom exceptions for semantic filesystem
"""

class SemanticFilesystemError(Exception):
    """Base exception for semantic filesystem errors"""
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "SEMANTIC_FS_ERROR"
        self.details = details or {}

class FileProcessingError(SemanticFilesystemError):
    """Error during file processing/indexing"""
    def __init__(self, file_path: str, message: str, original_error: Exception = None):
        super().__init__(
            message=f"Error processing file '{file_path}': {message}",
            error_code="FILE_PROCESSING_ERROR",
            details={
                "file_path": file_path,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.file_path = file_path
        self.original_error = original_error

class ContentExtractionError(FileProcessingError):
    """Error during content extraction"""
    def __init__(self, file_path: str, file_type: str, original_error: Exception = None):
        super().__init__(
            file_path=file_path,
            message=f"Failed to extract content from {file_type} file",
            original_error=original_error
        )
        self.error_code = "CONTENT_EXTRACTION_ERROR"
        self.file_type = file_type

class EmbeddingError(SemanticFilesystemError):
    """Error during embedding generation or storage"""
    def __init__(self, message: str, embedding_id: str = None, original_error: Exception = None):
        super().__init__(
            message=f"Embedding error: {message}",
            error_code="EMBEDDING_ERROR",
            details={
                "embedding_id": embedding_id,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.embedding_id = embedding_id
        self.original_error = original_error

class SearchError(SemanticFilesystemError):
    """Error during search operations"""
    def __init__(self, query: str, message: str, original_error: Exception = None):
        super().__init__(
            message=f"Search error for query '{query}': {message}",
            error_code="SEARCH_ERROR",
            details={
                "query": query,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.query = query
        self.original_error = original_error

class DatabaseError(SemanticFilesystemError):
    """Error during database operations"""
    def __init__(self, operation: str, message: str, original_error: Exception = None):
        super().__init__(
            message=f"Database error during {operation}: {message}",
            error_code="DATABASE_ERROR",
            details={
                "operation": operation,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.operation = operation
        self.original_error = original_error

class ConfigurationError(SemanticFilesystemError):
    """Error in system configuration"""
    def __init__(self, setting: str, message: str, suggestion: str = None):
        super().__init__(
            message=f"Configuration error for '{setting}': {message}",
            error_code="CONFIGURATION_ERROR",
            details={
                "setting": setting,
                "suggestion": suggestion
            }
        )
        self.setting = setting
        self.suggestion = suggestion

class ValidationError(SemanticFilesystemError):
    """Error in input validation"""
    def __init__(self, field: str, value: str, message: str):
        super().__init__(
            message=f"Validation error for '{field}' with value '{value}': {message}",
            error_code="VALIDATION_ERROR",
            details={
                "field": field,
                "value": value
            }
        )
        self.field = field
        self.value = value

class DependencyError(SemanticFilesystemError):
    """Error with missing or incompatible dependencies"""
    def __init__(self, dependency: str, message: str, install_command: str = None):
        super().__init__(
            message=f"Dependency error for '{dependency}': {message}",
            error_code="DEPENDENCY_ERROR",
            details={
                "dependency": dependency,
                "install_command": install_command
            }
        )
        self.dependency = dependency
        self.install_command = install_command

class FileSystemError(SemanticFilesystemError):
    """Error with file system operations"""
    def __init__(self, path: str, operation: str, message: str, original_error: Exception = None):
        super().__init__(
            message=f"File system error during {operation} on '{path}': {message}",
            error_code="FILESYSTEM_ERROR",
            details={
                "path": path,
                "operation": operation,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.path = path
        self.operation = operation
        self.original_error = original_error

class LLMError(SemanticFilesystemError):
    """Error with LLM operations"""
    def __init__(self, operation: str, message: str, provider: str = None, original_error: Exception = None):
        super().__init__(
            message=f"LLM error during {operation}: {message}",
            error_code="LLM_ERROR",
            details={
                "operation": operation,
                "provider": provider,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.operation = operation
        self.provider = provider
        self.original_error = original_error

class CacheError(SemanticFilesystemError):
    """Error with caching operations"""
    def __init__(self, operation: str, key: str, message: str, original_error: Exception = None):
        super().__init__(
            message=f"Cache error during {operation} for key '{key}': {message}",
            error_code="CACHE_ERROR",
            details={
                "operation": operation,
                "key": key,
                "original_error": str(original_error) if original_error else None
            }
        )
        self.operation = operation
        self.key = key
        self.original_error = original_error

# Error severity levels
class ErrorSeverity:
    CRITICAL = "critical"  # System cannot function
    HIGH = "high"         # Major functionality affected
    MEDIUM = "medium"     # Some functionality affected
    LOW = "low"          # Minor issues, system still functional
    INFO = "info"        # Informational, not an error

# Error recovery strategies
class ErrorRecovery:
    RETRY = "retry"           # Retry the operation
    FALLBACK = "fallback"     # Use fallback method
    SKIP = "skip"            # Skip and continue
    ABORT = "abort"          # Stop processing
    USER_INPUT = "user_input" # Request user intervention

# Error handling utilities
import logging
import traceback
from functools import wraps
from typing import Callable, Any, Optional, Dict, Union

logger = logging.getLogger(__name__)

def handle_errors(
    error_types: Union[Exception, tuple] = Exception,
    severity: str = ErrorSeverity.MEDIUM,
    recovery: str = ErrorRecovery.FALLBACK,
    fallback_value: Any = None,
    log_traceback: bool = True,
    custom_message: str = None
):
    """Decorator for comprehensive error handling

    Args:
        error_types: Exception types to catch
        severity: Error severity level
        recovery: Recovery strategy
        fallback_value: Value to return on error
        log_traceback: Whether to log full traceback
        custom_message: Custom error message
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_types as e:
                # Log the error
                error_msg = custom_message or f"Error in {func.__name__}: {str(e)}"

                if log_traceback:
                    logger.error(f"{error_msg}\n{traceback.format_exc()}")
                else:
                    logger.error(error_msg)

                # Apply recovery strategy
                if recovery == ErrorRecovery.RETRY:
                    # Simple retry logic (could be enhanced)
                    try:
                        logger.info(f"Retrying {func.__name__}...")
                        return func(*args, **kwargs)
                    except error_types:
                        logger.error(f"Retry failed for {func.__name__}")
                        return fallback_value

                elif recovery == ErrorRecovery.FALLBACK:
                    return fallback_value

                elif recovery == ErrorRecovery.SKIP:
                    logger.info(f"Skipping {func.__name__} due to error")
                    return fallback_value

                elif recovery == ErrorRecovery.ABORT:
                    logger.critical(f"Aborting due to critical error in {func.__name__}")
                    raise

                else:  # Default fallback
                    return fallback_value

        return wrapper
    return decorator

def log_error(
    error: Exception,
    context: Dict[str, Any] = None,
    severity: str = ErrorSeverity.MEDIUM,
    user_message: str = None
) -> Dict[str, Any]:
    """Log error with context and return error info

    Args:
        error: The exception that occurred
        context: Additional context information
        severity: Error severity level
        user_message: User-friendly error message

    Returns:
        Dictionary with error information
    """
    context = context or {}

    # Create error info
    error_info = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "severity": severity,
        "context": context,
        "user_message": user_message or "An error occurred while processing your request"
    }

    # Log based on severity
    if severity == ErrorSeverity.CRITICAL:
        logger.critical(f"CRITICAL ERROR: {error_info}")
    elif severity == ErrorSeverity.HIGH:
        logger.error(f"HIGH SEVERITY ERROR: {error_info}")
    elif severity == ErrorSeverity.MEDIUM:
        logger.warning(f"MEDIUM SEVERITY ERROR: {error_info}")
    else:
        logger.info(f"LOW SEVERITY ERROR: {error_info}")

    return error_info

def create_user_friendly_error(error: Exception, operation: str = None) -> Dict[str, Any]:
    """Create user-friendly error response

    Args:
        error: The exception that occurred
        operation: The operation that failed

    Returns:
        User-friendly error dictionary
    """
    operation = operation or "operation"

    # Map technical errors to user-friendly messages
    error_messages = {
        FileNotFoundError: f"The file you're trying to {operation} could not be found.",
        PermissionError: f"Permission denied. Please check file permissions.",
        ConnectionError: f"Connection error. Please check your network connection.",
        TimeoutError: f"The {operation} timed out. Please try again.",
        ValueError: f"Invalid input provided for {operation}.",
        KeyError: f"Required information is missing for {operation}.",
        FileProcessingError: f"Unable to process the file. The file may be corrupted or in an unsupported format.",
        EmbeddingError: f"Error generating search embeddings. Search functionality may be limited.",
        SearchError: f"Search failed. Please try a different query.",
        DatabaseError: f"Database error occurred. Please try again later.",
        ConfigurationError: f"System configuration error. Please contact support.",
        DependencyError: f"Missing required component. Please check system installation."
    }

    error_type = type(error)
    user_message = error_messages.get(error_type, f"An unexpected error occurred during {operation}.")

    return {
        "success": False,
        "error": {
            "message": user_message,
            "type": error_type.__name__,
            "operation": operation
        }
    }
