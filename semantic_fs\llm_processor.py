"""
LLM-powered content analysis and query interpretation
"""

import json
import re
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

from .config import settings

logger = logging.getLogger(__name__)

class LLMProcessor:
    """Handles LLM-powered content analysis and query interpretation"""

    def __init__(self):
        """Initialize LLM processor"""
        self.use_ollama = settings.use_ollama
        self.ollama_available = False
        self.openai_available = False

        # Check Ollama availability first (preferred)
        if self.use_ollama:
            self.ollama_available = self._check_ollama_availability()
            if self.ollama_available:
                logger.info(f"Using Ollama with model: {settings.ollama_model}")
                self.enabled = True
                return

        # Fallback to OpenAI if Ollama not available
        if HAS_OPENAI and settings.openai_api_key:
            openai.api_key = settings.openai_api_key
            self.client = openai.OpenAI(api_key=settings.openai_api_key)
            self.openai_available = True
            self.enabled = True
            logger.info("Using OpenAI for LLM processing")
        else:
            logger.warning("No LLM provider available - using fallback methods")
            self.enabled = False

    def _check_ollama_availability(self) -> bool:
        """Check if Ollama is running and has the required model"""
        try:
            # Check if Ollama server is running
            response = requests.get(f"{settings.ollama_base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                return False

            # Check if the required model is available
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]

            # Check for exact match first, then base name match
            model_available = (
                settings.ollama_model in model_names or
                any(model.split(":")[0] == settings.ollama_model.split(":")[0] for model in model_names)
            )

            if not model_available:
                logger.warning(f"Ollama model '{settings.ollama_model}' not found. Available models: {model_names}")
                logger.info(f"To install the model, run: ollama pull {settings.ollama_model}")
                return False

            return True

        except Exception as e:
            logger.debug(f"Ollama not available: {e}")
            return False

    def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """Make a request to Ollama API"""
        try:
            payload = {
                "model": settings.ollama_model,
                "prompt": prompt,
                "stream": False
            }

            if system_prompt:
                payload["system"] = system_prompt

            response = requests.post(
                f"{settings.ollama_base_url}/api/generate",
                json=payload,
                timeout=60
            )

            if response.status_code != 200:
                raise Exception(f"Ollama API error: {response.status_code}")

            result = response.json()
            return result.get("response", "").strip()

        except Exception as e:
            logger.error(f"Ollama API call failed: {e}")
            raise

    def _call_llm(self, messages: List[Dict[str, str]]) -> str:
        """Make a call to the LLM API"""
        if not self.enabled:
            return ""

        try:
            # Use Ollama if available
            if self.ollama_available:
                # Convert messages to Ollama format
                system_prompt = None
                user_prompt = ""

                for message in messages:
                    if message["role"] == "system":
                        system_prompt = message["content"]
                    elif message["role"] == "user":
                        user_prompt += message["content"] + "\n"

                return self._call_ollama(user_prompt.strip(), system_prompt)

            # Fallback to OpenAI
            elif self.openai_available:
                response = self.client.chat.completions.create(
                    model=settings.llm_model,
                    messages=messages,
                    max_tokens=1000,
                    temperature=0.1
                )
                return response.choices[0].message.content.strip()

            return ""

        except Exception as e:
            logger.error(f"LLM API call failed: {e}")
            return ""

    def interpret_query(self, query: str) -> Dict[str, Any]:
        """Interpret a natural language query and extract search parameters"""
        if not self.enabled:
            return self._fallback_query_interpretation(query)
        
        try:
            prompt = f"""
            Analyze this natural language file search query and extract structured search parameters:
            
            Query: "{query}"
            
            Extract the following information and return as JSON:
            {{
                "interpretation": "A clear interpretation of what the user is looking for",
                "search_terms": ["list", "of", "key", "search", "terms"],
                "temporal_filters": {{
                    "relative_time": "6 months ago" or null,
                    "start_date": "YYYY-MM-DD" or null,
                    "end_date": "YYYY-MM-DD" or null
                }},
                "context_filters": {{
                    "youtube_video": "PTX" or null,
                    "paper": "research topic" or null,
                    "project": "project name" or null
                }},
                "file_type_filters": ["pdf", "docx"] or [],
                "relationship_filters": ["used_in", "references"] or []
            }}
            
            For temporal expressions like "6 months ago", calculate the actual date range.
            Today's date is {datetime.now().strftime('%Y-%m-%d')}.
            """
            
            result_text = self._call_llm([
                {"role": "system", "content": "You are an expert at interpreting file search queries. Always return valid JSON."},
                {"role": "user", "content": prompt}
            ])
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # Process temporal filters
                if result.get("temporal_filters", {}).get("relative_time"):
                    result["temporal_filters"] = self._process_temporal_filter(
                        result["temporal_filters"]["relative_time"]
                    )
                
                return result
            else:
                logger.warning("Could not extract JSON from LLM response")
                return self._fallback_query_interpretation(query)
                
        except Exception as e:
            logger.error(f"Error interpreting query with LLM: {e}")
            return self._fallback_query_interpretation(query)
    
    def generate_summary(self, content: str, file_path: str) -> str:
        """Generate a summary of file content"""
        if not self.enabled:
            return self._fallback_summary(content)
        
        try:
            # Truncate content if too long
            max_content_length = 3000
            if len(content) > max_content_length:
                content = content[:max_content_length] + "..."
            
            prompt = f"""
            Summarize the content of this file in 2-3 sentences. Focus on the main topic, purpose, and key information.
            
            File: {file_path}
            Content: {content}
            
            Summary:
            """
            
            return self._call_llm([
                {"role": "system", "content": "You are an expert at summarizing document content concisely and accurately."},
                {"role": "user", "content": prompt}
            ])
            
        except Exception as e:
            logger.error(f"Error generating summary with LLM: {e}")
            return self._fallback_summary(content)
    
    def generate_tags(self, content: str, file_path: str) -> List[str]:
        """Generate relevant tags for file content"""
        if not self.enabled:
            return self._fallback_tags(content, file_path)
        
        try:
            # Truncate content if too long
            max_content_length = 2000
            if len(content) > max_content_length:
                content = content[:max_content_length] + "..."
            
            prompt = f"""
            Generate 5-10 relevant tags for this file content. Tags should be:
            - Single words or short phrases
            - Descriptive of the content, topic, or purpose
            - Useful for searching and categorization
            
            File: {file_path}
            Content: {content}
            
            Return tags as a JSON array: ["tag1", "tag2", "tag3"]
            """
            
            result_text = self._call_llm([
                {"role": "system", "content": "You are an expert at generating descriptive tags for documents. Always return valid JSON array."},
                {"role": "user", "content": prompt}
            ])
            
            # Extract JSON array from response
            json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if json_match:
                tags = json.loads(json_match.group())
                return [tag.lower().strip() for tag in tags if isinstance(tag, str)]
            else:
                logger.warning("Could not extract JSON array from LLM response")
                return self._fallback_tags(content, file_path)
                
        except Exception as e:
            logger.error(f"Error generating tags with LLM: {e}")
            return self._fallback_tags(content, file_path)
    
    def _process_temporal_filter(self, relative_time: str) -> Dict[str, str]:
        """Process relative time expressions into date ranges"""
        now = datetime.now()
        
        # Extract number and unit
        time_match = re.search(r'(\d+)\s*(day|week|month|year)s?\s*ago', relative_time.lower())
        if time_match:
            number = int(time_match.group(1))
            unit = time_match.group(2)
            
            if unit == "day":
                start_date = now - timedelta(days=number)
            elif unit == "week":
                start_date = now - timedelta(weeks=number)
            elif unit == "month":
                start_date = now - timedelta(days=number * 30)  # Approximate
            elif unit == "year":
                start_date = now - timedelta(days=number * 365)  # Approximate
            else:
                start_date = now - timedelta(days=30)  # Default to 30 days
            
            return {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": now.strftime('%Y-%m-%d')
            }
        
        return {}
    
    def _fallback_query_interpretation(self, query: str) -> Dict[str, Any]:
        """Fallback query interpretation without LLM"""
        query_lower = query.lower()
        
        # Extract basic search terms
        search_terms = []
        words = re.findall(r'\b\w+\b', query_lower)
        
        # Remove common stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'that', 'this', 'show', 'me', 'find', 'get'}
        search_terms = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Extract file types
        file_type_filters = []
        if 'pdf' in query_lower:
            file_type_filters.append('application/pdf')
        if 'doc' in query_lower or 'word' in query_lower:
            file_type_filters.append('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        if 'excel' in query_lower or 'spreadsheet' in query_lower:
            file_type_filters.append('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        
        # Extract temporal information
        temporal_filters = {}
        time_patterns = [
            (r'(\d+)\s*months?\s*ago', lambda m: datetime.now() - timedelta(days=int(m.group(1)) * 30)),
            (r'(\d+)\s*weeks?\s*ago', lambda m: datetime.now() - timedelta(weeks=int(m.group(1)))),
            (r'(\d+)\s*days?\s*ago', lambda m: datetime.now() - timedelta(days=int(m.group(1)))),
        ]
        
        for pattern, date_func in time_patterns:
            match = re.search(pattern, query_lower)
            if match:
                start_date = date_func(match)
                temporal_filters = {
                    "start_date": start_date.strftime('%Y-%m-%d'),
                    "end_date": datetime.now().strftime('%Y-%m-%d')
                }
                break
        
        # Extract context information
        context_filters = {}
        if 'youtube' in query_lower or 'video' in query_lower:
            # Try to extract video topic
            video_match = re.search(r'video.*?on\s+(\w+)', query_lower)
            if video_match:
                context_filters['youtube_video'] = video_match.group(1)
            else:
                context_filters['youtube_video'] = 'video'
        
        if 'paper' in query_lower or 'research' in query_lower:
            context_filters['paper'] = 'research'
        
        return {
            "interpretation": f"Looking for files related to: {', '.join(search_terms)}",
            "search_terms": search_terms,
            "temporal_filters": temporal_filters,
            "context_filters": context_filters,
            "file_type_filters": file_type_filters,
            "relationship_filters": []
        }
    
    def _fallback_summary(self, content: str) -> str:
        """Generate a simple summary without LLM"""
        if not content:
            return "Empty file"
        
        # Take first few sentences
        sentences = re.split(r'[.!?]+', content)
        summary_sentences = []
        char_count = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and char_count + len(sentence) < 200:
                summary_sentences.append(sentence)
                char_count += len(sentence)
            else:
                break
        
        if summary_sentences:
            return '. '.join(summary_sentences) + '.'
        else:
            return content[:200] + "..." if len(content) > 200 else content
    
    def _fallback_tags(self, content: str, file_path: str) -> List[str]:
        """Generate simple tags without LLM"""
        tags = []
        
        # Add file extension as tag
        file_ext = file_path.split('.')[-1].lower()
        if file_ext:
            tags.append(file_ext)
        
        # Extract common words as tags
        if content:
            words = re.findall(r'\b[a-zA-Z]{4,}\b', content.lower())
            word_freq = {}
            for word in words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # Get most frequent words as tags
            frequent_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            for word, freq in frequent_words[:5]:
                if freq > 2:  # Only words that appear multiple times
                    tags.append(word)
        
        return tags[:8]  # Limit to 8 tags
