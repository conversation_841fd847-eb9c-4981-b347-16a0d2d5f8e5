"""
Content extraction for various file types
"""

import os
import mimetypes
from typing import Optional, Dict, Any
from pathlib import Path
import logging

# PDF processing
try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

# Document processing
try:
    from docx import Document
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

# Excel processing
try:
    import openpyxl
    HAS_EXCEL = True
except ImportError:
    HAS_EXCEL = False

# Image processing
try:
    from PIL import Image, ExifTags
    from PIL.ExifTags import TAGS
    import pytesseract
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

# Enhanced image processing
try:
    import cv2
    import numpy as np
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False

import json
import csv
from xml.etree import ElementTree as ET

logger = logging.getLogger(__name__)

class ContentExtractor:
    """Extracts text content from various file types"""
    
    def __init__(self):
        """Initialize content extractor"""
        self.extractors = {
            'text/plain': self._extract_text,
            'text/markdown': self._extract_text,
            'application/json': self._extract_json,
            'text/csv': self._extract_csv,
            'application/xml': self._extract_xml,
            'text/xml': self._extract_xml,
            'text/html': self._extract_html,
            'text/css': self._extract_text,
            'application/javascript': self._extract_text,
            'text/javascript': self._extract_text,
            'application/python': self._extract_text,
            'text/x-python': self._extract_text,
        }
        
        if HAS_PDF:
            self.extractors['application/pdf'] = self._extract_pdf
        
        if HAS_DOCX:
            self.extractors['application/vnd.openxmlformats-officedocument.wordprocessingml.document'] = self._extract_docx
        
        if HAS_EXCEL:
            self.extractors['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'] = self._extract_excel
        
        if HAS_OCR:
            # Comprehensive image format support
            self.extractors['image/jpeg'] = self._extract_image_enhanced
            self.extractors['image/jpg'] = self._extract_image_enhanced
            self.extractors['image/png'] = self._extract_image_enhanced
            self.extractors['image/gif'] = self._extract_image_enhanced
            self.extractors['image/bmp'] = self._extract_image_enhanced
            self.extractors['image/tiff'] = self._extract_image_enhanced
            self.extractors['image/tif'] = self._extract_image_enhanced
            self.extractors['image/webp'] = self._extract_image_enhanced
            self.extractors['image/svg+xml'] = self._extract_image_enhanced
            self.extractors['image/x-icon'] = self._extract_image_enhanced
            self.extractors['image/vnd.adobe.photoshop'] = self._extract_image_enhanced
    
    def extract_content(self, file_path: str) -> Optional[str]:
        """Extract text content from a file"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return None
            
            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if not mime_type:
                # Try to determine from extension
                extension = file_path.suffix.lower()
                mime_type = self._get_mime_from_extension(extension)
            
            if not mime_type:
                logger.warning(f"Could not determine MIME type for: {file_path}")
                return None
            
            # Get appropriate extractor
            extractor = self.extractors.get(mime_type)
            if not extractor:
                logger.warning(f"No extractor available for MIME type: {mime_type}")
                return None
            
            # Extract content
            content = extractor(file_path)
            
            if content:
                # Clean and normalize content
                content = self._clean_content(content)
                logger.debug(f"Extracted {len(content)} characters from {file_path}")
                return content
            else:
                logger.warning(f"No content extracted from: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting content from {file_path}: {e}")
            return None
    
    def _get_mime_from_extension(self, extension: str) -> Optional[str]:
        """Get MIME type from file extension"""
        extension_map = {
            '.adoc': 'text/asciidoc',
            '.asciidoc': 'text/asciidoc',
            '.bash': 'application/x-sh',
            '.bat': 'application/x-msdos-program',
            '.c': 'text/x-csrc',
            '.cc': 'text/x-c++src',
            '.cfg': 'text/plain',
            '.changelog': 'text/plain',
            '.cjs': 'application/javascript',
            '.clj': 'text/x-clojure',
            '.conf': 'text/plain',
            '.config': 'text/plain',
            '.cpp': 'text/x-c++src',
            '.cs': 'text/x-csharp',
            '.css': 'text/css',
            '.csv': 'text/csv',
            '.cxx': 'text/x-c++src',
            '.dockerfile': 'text/plain',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.env': 'text/plain',
            '.gif': 'image/gif',
            '.go': 'text/x-go',
            '.h': 'text/x-chdr',
            '.hpp': 'text/x-c++hdr',
            '.hs': 'text/x-haskell',
            '.htm': 'text/html',
            '.html': 'text/html',
            '.ico': 'image/x-icon',
            '.ini': 'text/plain',
            '.java': 'text/x-java-source',
            '.jpeg': 'image/jpeg',
            '.jpg': 'image/jpeg',
            '.js': 'application/javascript',
            '.json': 'application/json',
            '.json5': 'application/json5',
            '.jsonl': 'application/jsonlines',
            '.jsx': 'application/javascript',
            '.kt': 'text/x-kotlin',
            '.latex': 'text/x-tex',
            '.less': 'text/x-less',
            '.license': 'text/plain',
            '.log': 'text/plain',
            '.lua': 'text/x-lua',
            '.makefile': 'text/plain',
            '.markdown': 'text/markdown',
            '.md': 'text/markdown',
            '.mdown': 'text/markdown',
            '.mdx': 'text/markdown',
            '.mjs': 'application/javascript',
            '.mkd': 'text/markdown',
            '.ml': 'text/x-ocaml',
            '.pdf': 'application/pdf',
            '.php': 'application/x-php',
            '.png': 'image/png',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.properties': 'text/plain',
            '.ps1': 'application/x-powershell',
            '.psd': 'image/vnd.adobe.photoshop',
            '.py': 'text/x-python',
            '.pyi': 'text/x-python',
            '.pyw': 'text/x-python',
            '.r': 'text/x-r',
            '.rb': 'application/x-ruby',
            '.readme': 'text/plain',
            '.rest': 'text/x-rst',
            '.rs': 'text/x-rust',
            '.rst': 'text/x-rst',
            '.sass': 'text/x-sass',
            '.scala': 'text/x-scala',
            '.scss': 'text/x-scss',
            '.sh': 'application/x-sh',
            '.sql': 'application/sql',
            '.svg': 'image/svg+xml',
            '.swift': 'text/x-swift',
            '.tex': 'text/x-tex',
            '.tif': 'image/tiff',
            '.tiff': 'image/tiff',
            '.toml': 'application/toml',
            '.ts': 'application/typescript',
            '.tsv': 'text/tab-separated-values',
            '.tsx': 'application/typescript',
            '.map': 'application/json',
            '.txt': 'text/plain',
            '.webp': 'image/webp',
            '.xhtml': 'application/xhtml+xml',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xml': 'application/xml',
            '.yaml': 'application/x-yaml',
            '.yml': 'application/x-yaml',
        }
        return extension_map.get(extension)
    
    def _extract_text(self, file_path: Path) -> Optional[str]:
        """Extract content from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return None
    
    def _extract_json(self, file_path: Path) -> Optional[str]:
        """Extract content from JSON files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert JSON to readable text
                return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            return None
    
    def _extract_csv(self, file_path: Path) -> Optional[str]:
        """Extract content from CSV files"""
        try:
            content_lines = []
            with open(file_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.reader(f)
                for i, row in enumerate(reader):
                    if i < 100:  # Limit to first 100 rows
                        content_lines.append(' | '.join(row))
                    else:
                        break
            return '\n'.join(content_lines)
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return None
    
    def _extract_xml(self, file_path: Path) -> Optional[str]:
        """Extract content from XML files"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Extract all text content
            text_content = []
            for elem in root.iter():
                if elem.text and elem.text.strip():
                    text_content.append(elem.text.strip())
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading XML file {file_path}: {e}")
            return None
    
    def _extract_html(self, file_path: Path) -> Optional[str]:
        """Extract content from HTML files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Simple HTML tag removal (for basic extraction)
            import re
            # Remove script and style elements
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
            # Remove HTML tags
            content = re.sub(r'<[^>]+>', '', content)
            # Clean up whitespace
            content = re.sub(r'\s+', ' ', content)
            
            return content.strip()
        except Exception as e:
            logger.error(f"Error reading HTML file {file_path}: {e}")
            return None
    
    def _extract_pdf(self, file_path: Path) -> Optional[str]:
        """Extract content from PDF files"""
        if not HAS_PDF:
            logger.warning("PyPDF2 not available for PDF extraction")
            return None
        
        try:
            text_content = []
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {e}")
            return None
    
    def _extract_docx(self, file_path: Path) -> Optional[str]:
        """Extract content from DOCX files"""
        if not HAS_DOCX:
            logger.warning("python-docx not available for DOCX extraction")
            return None
        
        try:
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading DOCX file {file_path}: {e}")
            return None
    
    def _extract_excel(self, file_path: Path) -> Optional[str]:
        """Extract content from Excel files"""
        if not HAS_EXCEL:
            logger.warning("openpyxl not available for Excel extraction")
            return None
        
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text_content = []
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text_content.append(f"Sheet: {sheet_name}")
                
                for row in sheet.iter_rows(max_row=100, values_only=True):  # Limit to first 100 rows
                    row_text = ' | '.join([str(cell) if cell is not None else '' for cell in row])
                    if row_text.strip():
                        text_content.append(row_text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading Excel file {file_path}: {e}")
            return None
    
    def _extract_image_enhanced(self, file_path: Path) -> Optional[str]:
        """Extract comprehensive content from images using enhanced OCR and metadata"""
        if not HAS_OCR:
            logger.warning("PIL/pytesseract not available for OCR")
            return None

        try:
            content_parts = []

            # Extract basic metadata
            with Image.open(file_path) as image:
                # Basic image info
                metadata_parts = []
                metadata_parts.append(f"Format: {image.format}")
                metadata_parts.append(f"Size: {image.size[0]}x{image.size[1]}")
                metadata_parts.append(f"Mode: {image.mode}")

                # EXIF data if available
                try:
                    exif_data = image._getexif()
                    if exif_data:
                        for tag_id, value in exif_data.items():
                            tag = TAGS.get(tag_id, tag_id)
                            if isinstance(tag, str) and isinstance(value, (str, int, float)):
                                if tag in ['DateTime', 'DateTimeOriginal', 'Make', 'Model', 'Software',
                                         'Artist', 'Copyright', 'ImageDescription', 'UserComment']:
                                    metadata_parts.append(f"{tag}: {value}")
                except:
                    pass  # EXIF not available or corrupted

                if metadata_parts:
                    content_parts.append(f"Image Metadata: {' | '.join(metadata_parts)}")

                # Enhanced OCR with preprocessing
                ocr_text = self._extract_ocr_with_preprocessing(image)
                if ocr_text:
                    content_parts.append(f"Text Content: {ocr_text}")

                # Visual context analysis
                visual_context = self._analyze_image_context(image, file_path)
                if visual_context:
                    content_parts.append(f"Visual Context: {visual_context}")

            if content_parts:
                return "\n\n".join(content_parts)
            else:
                return f"Image file: {file_path.name} (no extractable text content)"

        except Exception as e:
            logger.error(f"Error performing enhanced image extraction on {file_path}: {e}")
            return None

    def _extract_ocr_with_preprocessing(self, image: Image.Image) -> Optional[str]:
        """Extract text using OCR with image preprocessing"""
        try:
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Enhanced OCR configuration
            ocr_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?@#$%^&*()_+-=[]{}|;:,.<>?/~`'

            # Preprocess image if OpenCV is available
            if HAS_CV2:
                # Convert PIL to OpenCV format
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                # Convert to grayscale
                gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

                # Apply denoising
                denoised = cv2.fastNlMeansDenoising(gray)

                # Apply adaptive thresholding
                thresh = cv2.adaptiveThreshold(
                    denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )

                # Convert back to PIL
                processed_image = Image.fromarray(thresh)
            else:
                processed_image = image

            # Perform OCR
            text = pytesseract.image_to_string(processed_image, config=ocr_config)

            # Clean and validate text
            if text:
                # Remove excessive whitespace
                import re
                text = re.sub(r'\s+', ' ', text)
                text = text.strip()

                # Remove very short "words" that are likely OCR errors
                words = text.split()
                cleaned_words = [word for word in words if len(word) >= 2 or word.isdigit()]
                cleaned_text = ' '.join(cleaned_words)

                if len(cleaned_text) >= 3:  # Minimum text length
                    return cleaned_text

            return None

        except Exception as e:
            logger.debug(f"Error in OCR preprocessing: {e}")
            return None

    def _analyze_image_context(self, image: Image.Image, file_path: Path) -> Optional[str]:
        """Analyze image context and characteristics"""
        try:
            context_parts = []

            # Analyze image characteristics
            width, height = image.size
            aspect_ratio = width / height

            # Determine image type based on characteristics
            if aspect_ratio > 2.0:
                context_parts.append("panoramic or banner image")
            elif aspect_ratio < 0.5:
                context_parts.append("tall or portrait image")
            elif 0.9 <= aspect_ratio <= 1.1:
                context_parts.append("square image")

            # Analyze size category
            total_pixels = width * height
            if total_pixels > 2000000:  # > 2MP
                context_parts.append("high resolution")
            elif total_pixels < 100000:  # < 0.1MP
                context_parts.append("thumbnail or icon")

            # Analyze color information
            if image.mode == 'L':
                context_parts.append("grayscale")
            elif image.mode == 'RGBA':
                context_parts.append("with transparency")

            # Analyze filename for context
            filename = file_path.stem.lower()

            # Common image naming patterns
            patterns = {
                'screenshot': ['screenshot', 'screen', 'capture', 'snap'],
                'photo': ['photo', 'img', 'pic', 'picture', 'image'],
                'logo': ['logo', 'brand', 'icon', 'symbol'],
                'diagram': ['diagram', 'chart', 'graph', 'flow', 'schema'],
                'document': ['doc', 'page', 'scan', 'pdf', 'text'],
                'ui': ['ui', 'interface', 'mockup', 'wireframe', 'design']
            }

            for category, keywords in patterns.items():
                if any(keyword in filename for keyword in keywords):
                    context_parts.append(f"likely {category}")
                    break

            return ", ".join(context_parts) if context_parts else None

        except Exception as e:
            logger.debug(f"Error analyzing image context: {e}")
            return None
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize extracted content"""
        if not content:
            return ""
        
        # Remove excessive whitespace
        import re
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'\n\s*\n', '\n\n', content)
        
        # Remove control characters
        content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\t')
        
        return content.strip()
