"""
Content extraction for various file types
"""

import os
import mimetypes
from typing import Optional, Dict, Any
from pathlib import Path
import logging

# PDF processing
try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

# Document processing
try:
    from docx import Document
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

# Excel processing
try:
    import openpyxl
    HAS_EXCEL = True
except ImportError:
    HAS_EXCEL = False

# Image processing
try:
    from PIL import Image, ExifTags
    from PIL.ExifTags import TAGS
    import pytesseract
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

# Enhanced image processing
try:
    import cv2
    import numpy as np
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False

import json
import csv
from xml.etree import ElementTree as ET

logger = logging.getLogger(__name__)

class ContentExtractor:
    """Extracts text content from various file types"""
    
    def __init__(self):
        """Initialize content extractor"""
        self.extractors = {
            'text/plain': self._extract_text,
            'text/markdown': self._extract_text,
            'application/json': self._extract_json,
            'text/csv': self._extract_csv,
            'application/xml': self._extract_xml,
            'text/xml': self._extract_xml,
            'text/html': self._extract_html,
            'text/css': self._extract_text,
            'application/javascript': self._extract_text,
            'text/javascript': self._extract_text,
            'application/python': self._extract_text,
            'text/x-python': self._extract_text,
        }
        
        if HAS_PDF:
            self.extractors['application/pdf'] = self._extract_pdf
        
        if HAS_DOCX:
            self.extractors['application/vnd.openxmlformats-officedocument.wordprocessingml.document'] = self._extract_docx
        
        if HAS_EXCEL:
            self.extractors['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'] = self._extract_excel
        
        if HAS_OCR:
            # Comprehensive image format support
            self.extractors['image/jpeg'] = self._extract_image_enhanced
            self.extractors['image/jpg'] = self._extract_image_enhanced
            self.extractors['image/png'] = self._extract_image_enhanced
            self.extractors['image/gif'] = self._extract_image_enhanced
            self.extractors['image/bmp'] = self._extract_image_enhanced
            self.extractors['image/tiff'] = self._extract_image_enhanced
            self.extractors['image/tif'] = self._extract_image_enhanced
            self.extractors['image/webp'] = self._extract_image_enhanced
            self.extractors['image/svg+xml'] = self._extract_image_enhanced
            self.extractors['image/x-icon'] = self._extract_image_enhanced
            self.extractors['image/vnd.adobe.photoshop'] = self._extract_image_enhanced

        # Add specialized format extractors
        self.extractors['application/zip'] = self._extract_archive_metadata
        self.extractors['application/vnd.rar'] = self._extract_archive_metadata
        self.extractors['application/x-7z-compressed'] = self._extract_archive_metadata
        self.extractors['application/x-tar'] = self._extract_archive_metadata
        self.extractors['application/gzip'] = self._extract_archive_metadata

        self.extractors['application/x-msdownload'] = self._extract_binary_metadata
        self.extractors['application/octet-stream'] = self._extract_binary_metadata
        self.extractors['application/x-sharedlib'] = self._extract_binary_metadata

        self.extractors['text/x-troff-man'] = self._extract_text
        self.extractors['text/x-pod'] = self._extract_text
        self.extractors['text/x-info'] = self._extract_text
        self.extractors['text/x-patch'] = self._extract_text
        self.extractors['text/x-diff'] = self._extract_text

        self.extractors['font/woff'] = self._extract_font_metadata
        self.extractors['font/woff2'] = self._extract_font_metadata
        self.extractors['font/ttf'] = self._extract_font_metadata
        self.extractors['font/otf'] = self._extract_font_metadata

        # Proprietary format extractors
        self.extractors['application/x-x509-ca-cert'] = self._extract_certificate_metadata
        self.extractors['application/pkcs8'] = self._extract_certificate_metadata
        self.extractors['application/x-pkcs12'] = self._extract_certificate_metadata
        self.extractors['application/x-pem-file'] = self._extract_certificate_metadata
        self.extractors['application/pgp-signature'] = self._extract_signature_metadata
    
    def extract_content(self, file_path: str) -> Optional[str]:
        """Extract text content from a file"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return None
            
            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if not mime_type:
                # Try to determine from extension
                extension = file_path.suffix.lower()
                mime_type = self._get_mime_from_extension(extension)
            
            if not mime_type:
                logger.warning(f"Could not determine MIME type for: {file_path}")
                return None
            
            # Get appropriate extractor
            extractor = self.extractors.get(mime_type)
            if not extractor:
                logger.warning(f"No extractor available for MIME type: {mime_type}")
                return None
            
            # Extract content
            content = extractor(file_path)
            
            if content:
                # Clean and normalize content
                content = self._clean_content(content)
                logger.debug(f"Extracted {len(content)} characters from {file_path}")
                return content
            else:
                logger.warning(f"No content extracted from: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting content from {file_path}: {e}")
            return None
    
    def _get_mime_from_extension(self, extension: str) -> Optional[str]:
        """Get MIME type from file extension"""
        extension_map = {
            '.1': 'text/x-troff-man',
            '.2': 'text/x-troff-man',
            '.3': 'text/x-troff-man',
            '.4': 'text/x-troff-man',
            '.5': 'text/x-troff-man',
            '.6': 'text/x-troff-man',
            '.7': 'text/x-troff-man',
            '.8': 'text/x-troff-man',
            '.9': 'text/x-troff-man',
            '.7z': 'application/x-7z-compressed',
            '.activation': 'text/plain',
            '.adoc': 'text/asciidoc',
            '.ai': 'application/postscript',
            '.arrow': 'application/vnd.apache.arrow.file',
            '.arw': 'image/x-sony-arw',
            '.asciidoc': 'text/asciidoc',
            '.avro': 'application/avro',
            '.bak': 'text/plain',
            '.bin': 'application/octet-stream',
            '.bmp': 'image/bmp',
            '.bz2': 'application/x-bzip2',
            '.cache': 'application/octet-stream',
            '.capnp': 'application/x-capnp',
            '.catalog': 'text/plain',
            '.cert': 'application/x-x509-ca-cert',
            '.checksum': 'text/plain',
            '.crc': 'text/plain',
            '.crt': 'application/x-x509-ca-cert',
            '.csr': 'application/pkcs10',
            '.core': 'application/x-core',
            '.cr2': 'image/x-canon-cr2',
            '.dat': 'application/octet-stream',
            '.db-journal': 'application/octet-stream',
            '.def': 'text/plain',
            '.der': 'application/x-x509-ca-cert',
            '.desc': 'text/plain',
            '.diff': 'text/x-diff',
            '.dll': 'application/x-msdownload',
            '.dng': 'image/x-adobe-dng',
            '.dump': 'application/octet-stream',
            '.dylib': 'application/x-mach-binary',
            '.eot': 'application/vnd.ms-fontobject',
            '.eps': 'application/postscript',
            '.exe': 'application/x-msdownload',
            '.feather': 'application/vnd.apache.arrow.file',
            '.flt': 'application/octet-stream',
            '.bash': 'application/x-sh',
            '.guid': 'text/plain',
            '.bat': 'application/x-msdos-program',
            '.c': 'text/x-csrc',
            '.cc': 'text/x-c++src',
            '.cfg': 'text/plain',
            '.changelog': 'text/plain',
            '.cjs': 'application/javascript',
            '.clj': 'text/x-clojure',
            '.conf': 'text/plain',
            '.config': 'text/plain',
            '.cpp': 'text/x-c++src',
            '.cs': 'text/x-csharp',
            '.css': 'text/css',
            '.csv': 'text/csv',
            '.cxx': 'text/x-c++src',
            '.dockerfile': 'text/plain',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.env': 'text/plain',
            '.gif': 'image/gif',
            '.go': 'text/x-go',
            '.gz': 'application/gzip',
            '.h': 'text/x-chdr',
            '.h5': 'application/x-hdf5',
            '.hash': 'text/plain',
            '.hdf5': 'application/x-hdf5',
            '.help': 'text/plain',
            '.htaccess': 'text/plain',
            '.htpasswd': 'text/plain',
            '.hpp': 'text/x-c++hdr',
            '.hs': 'text/x-haskell',
            '.htm': 'text/html',
            '.html': 'text/html',
            '.ico': 'image/x-icon',
            '.idx': 'application/octet-stream',
            '.index': 'text/plain',
            '.info': 'text/x-info',
            '.ini': 'text/plain',
            '.java': 'text/x-java-source',
            '.jks': 'application/x-java-keystore',
            '.joblib': 'application/octet-stream',
            '.jpeg': 'image/jpeg',
            '.jpg': 'image/jpeg',
            '.js': 'application/javascript',
            '.json': 'application/json',
            '.json5': 'application/json5',
            '.jsonl': 'application/jsonlines',
            '.jsx': 'application/javascript',
            '.key': 'application/pkcs8',
            '.keystore': 'application/x-java-keystore',
            '.kt': 'text/x-kotlin',
            '.latex': 'text/x-tex',
            '.lck': 'text/plain',
            '.license-key': 'text/plain',
            '.less': 'text/x-less',
            '.license': 'text/plain',
            '.lock': 'text/plain',
            '.log': 'text/plain',
            '.lua': 'text/x-lua',
            '.lz4': 'application/x-lz4',
            '.makefile': 'text/plain',
            '.man': 'text/x-troff-man',
            '.manifest': 'text/plain',
            '.mat': 'application/x-matlab-data',
            '.md5': 'text/plain',
            '.meta': 'text/plain',
            '.msgpack': 'application/msgpack',
            '.markdown': 'text/markdown',
            '.md': 'text/markdown',
            '.mdown': 'text/markdown',
            '.mdx': 'text/markdown',
            '.mjs': 'application/javascript',
            '.mkd': 'text/markdown',
            '.ml': 'text/x-ocaml',
            '.nef': 'image/x-nikon-nef',
            '.npy': 'application/octet-stream',
            '.npz': 'application/zip',
            '.orc': 'application/orc',
            '.orig': 'text/plain',
            '.otf': 'font/otf',
            '.p12': 'application/x-pkcs12',
            '.parquet': 'application/vnd.apache.parquet',
            '.patch': 'text/x-patch',
            '.pb': 'application/x-protobuf',
            '.pdf': 'application/pdf',
            '.pem': 'application/x-pem-file',
            '.pfx': 'application/x-pkcs12',
            '.pickle': 'application/octet-stream',
            '.pid': 'text/plain',
            '.pkl': 'application/octet-stream',
            '.php': 'application/x-php',
            '.png': 'image/png',
            '.pod': 'text/x-pod',
            '.proto': 'text/x-protobuf',
            '.protobuf': 'application/x-protobuf',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.properties': 'text/plain',
            '.ps1': 'application/x-powershell',
            '.psd': 'image/vnd.adobe.photoshop',
            '.py': 'text/x-python',
            '.pyi': 'text/x-python',
            '.pyw': 'text/x-python',
            '.r': 'text/x-r',
            '.rar': 'application/vnd.rar',
            '.raw': 'image/x-panasonic-raw',
            '.ref': 'text/plain',
            '.registry': 'text/plain',
            '.rej': 'text/x-reject',
            '.robots': 'text/plain',
            '.rb': 'application/x-ruby',
            '.readme': 'text/plain',
            '.rest': 'text/x-rst',
            '.rs': 'text/x-rust',
            '.rst': 'text/x-rst',
            '.sass': 'text/x-sass',
            '.scala': 'text/x-scala',
            '.scss': 'text/x-scss',
            '.serial': 'text/plain',
            '.session': 'text/plain',
            '.sha1': 'text/plain',
            '.sha256': 'text/plain',
            '.sig': 'application/pgp-signature',
            '.signature': 'application/pgp-signature',
            '.sh': 'application/x-sh',
            '.sitemap': 'application/xml',
            '.so': 'application/x-sharedlib',
            '.spec': 'text/plain',
            '.sql': 'application/sql',
            '.sqlite-shm': 'application/octet-stream',
            '.sqlite-wal': 'application/octet-stream',
            '.svg': 'image/svg+xml',
            '.swp': 'application/octet-stream',
            '.swift': 'text/x-swift',
            '.tar': 'application/x-tar',
            '.tex': 'text/x-tex',
            '.thrift': 'application/x-thrift',
            '.token': 'text/plain',
            '.truststore': 'application/x-java-keystore',
            '.tif': 'image/tiff',
            '.tiff': 'image/tiff',
            '.tmp': 'text/plain',
            '.toml': 'application/toml',
            '.ttf': 'font/ttf',
            '.ts': 'application/typescript',
            '.tsv': 'text/tab-separated-values',
            '.tsx': 'application/typescript',
            '.uuid': 'text/plain',
            '.map': 'application/json',
            '.txt': 'text/plain',
            '.venc': 'application/octet-stream',
            '.webmanifest': 'application/manifest+json',
            '.webp': 'image/webp',
            '.woff': 'font/woff',
            '.woff2': 'font/woff2',
            '.xhtml': 'application/xhtml+xml',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xml': 'application/xml',
            '.xz': 'application/x-xz',
            '.yaml': 'application/x-yaml',
            '.zip': 'application/zip',
            '.zst': 'application/zstd',
            '.yml': 'application/x-yaml',
        }
        return extension_map.get(extension)
    
    def _extract_text(self, file_path: Path) -> Optional[str]:
        """Extract content from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return None
    
    def _extract_json(self, file_path: Path) -> Optional[str]:
        """Extract content from JSON files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert JSON to readable text
                return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            return None
    
    def _extract_csv(self, file_path: Path) -> Optional[str]:
        """Extract content from CSV files"""
        try:
            content_lines = []
            with open(file_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.reader(f)
                for i, row in enumerate(reader):
                    if i < 100:  # Limit to first 100 rows
                        content_lines.append(' | '.join(row))
                    else:
                        break
            return '\n'.join(content_lines)
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return None
    
    def _extract_xml(self, file_path: Path) -> Optional[str]:
        """Extract content from XML files"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Extract all text content
            text_content = []
            for elem in root.iter():
                if elem.text and elem.text.strip():
                    text_content.append(elem.text.strip())
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading XML file {file_path}: {e}")
            return None
    
    def _extract_html(self, file_path: Path) -> Optional[str]:
        """Extract content from HTML files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Simple HTML tag removal (for basic extraction)
            import re
            # Remove script and style elements
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
            # Remove HTML tags
            content = re.sub(r'<[^>]+>', '', content)
            # Clean up whitespace
            content = re.sub(r'\s+', ' ', content)
            
            return content.strip()
        except Exception as e:
            logger.error(f"Error reading HTML file {file_path}: {e}")
            return None
    
    def _extract_pdf(self, file_path: Path) -> Optional[str]:
        """Extract content from PDF files"""
        if not HAS_PDF:
            logger.warning("PyPDF2 not available for PDF extraction")
            return None
        
        try:
            text_content = []
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {e}")
            return None
    
    def _extract_docx(self, file_path: Path) -> Optional[str]:
        """Extract content from DOCX files"""
        if not HAS_DOCX:
            logger.warning("python-docx not available for DOCX extraction")
            return None
        
        try:
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading DOCX file {file_path}: {e}")
            return None
    
    def _extract_excel(self, file_path: Path) -> Optional[str]:
        """Extract content from Excel files"""
        if not HAS_EXCEL:
            logger.warning("openpyxl not available for Excel extraction")
            return None
        
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text_content = []
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text_content.append(f"Sheet: {sheet_name}")
                
                for row in sheet.iter_rows(max_row=100, values_only=True):  # Limit to first 100 rows
                    row_text = ' | '.join([str(cell) if cell is not None else '' for cell in row])
                    if row_text.strip():
                        text_content.append(row_text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading Excel file {file_path}: {e}")
            return None
    
    def _extract_image_enhanced(self, file_path: Path) -> Optional[str]:
        """Extract comprehensive content from images using enhanced OCR and metadata"""
        if not HAS_OCR:
            logger.warning("PIL/pytesseract not available for OCR")
            return None

        try:
            content_parts = []

            # Extract basic metadata
            with Image.open(file_path) as image:
                # Basic image info
                metadata_parts = []
                metadata_parts.append(f"Format: {image.format}")
                metadata_parts.append(f"Size: {image.size[0]}x{image.size[1]}")
                metadata_parts.append(f"Mode: {image.mode}")

                # EXIF data if available
                try:
                    exif_data = image._getexif()
                    if exif_data:
                        for tag_id, value in exif_data.items():
                            tag = TAGS.get(tag_id, tag_id)
                            if isinstance(tag, str) and isinstance(value, (str, int, float)):
                                if tag in ['DateTime', 'DateTimeOriginal', 'Make', 'Model', 'Software',
                                         'Artist', 'Copyright', 'ImageDescription', 'UserComment']:
                                    metadata_parts.append(f"{tag}: {value}")
                except:
                    pass  # EXIF not available or corrupted

                if metadata_parts:
                    content_parts.append(f"Image Metadata: {' | '.join(metadata_parts)}")

                # Enhanced OCR with preprocessing
                ocr_text = self._extract_ocr_with_preprocessing(image)
                if ocr_text:
                    content_parts.append(f"Text Content: {ocr_text}")

                # Visual context analysis
                visual_context = self._analyze_image_context(image, file_path)
                if visual_context:
                    content_parts.append(f"Visual Context: {visual_context}")

            if content_parts:
                return "\n\n".join(content_parts)
            else:
                return f"Image file: {file_path.name} (no extractable text content)"

        except Exception as e:
            logger.error(f"Error performing enhanced image extraction on {file_path}: {e}")
            return None

    def _extract_ocr_with_preprocessing(self, image: Image.Image) -> Optional[str]:
        """Extract text using OCR with image preprocessing"""
        try:
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Enhanced OCR configuration
            ocr_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?@#$%^&*()_+-=[]{}|;:,.<>?/~`'

            # Preprocess image if OpenCV is available
            if HAS_CV2:
                # Convert PIL to OpenCV format
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                # Convert to grayscale
                gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

                # Apply denoising
                denoised = cv2.fastNlMeansDenoising(gray)

                # Apply adaptive thresholding
                thresh = cv2.adaptiveThreshold(
                    denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )

                # Convert back to PIL
                processed_image = Image.fromarray(thresh)
            else:
                processed_image = image

            # Perform OCR
            text = pytesseract.image_to_string(processed_image, config=ocr_config)

            # Clean and validate text
            if text:
                # Remove excessive whitespace
                import re
                text = re.sub(r'\s+', ' ', text)
                text = text.strip()

                # Remove very short "words" that are likely OCR errors
                words = text.split()
                cleaned_words = [word for word in words if len(word) >= 2 or word.isdigit()]
                cleaned_text = ' '.join(cleaned_words)

                if len(cleaned_text) >= 3:  # Minimum text length
                    return cleaned_text

            return None

        except Exception as e:
            logger.debug(f"Error in OCR preprocessing: {e}")
            return None

    def _analyze_image_context(self, image: Image.Image, file_path: Path) -> Optional[str]:
        """Analyze image context and characteristics"""
        try:
            context_parts = []

            # Analyze image characteristics
            width, height = image.size
            aspect_ratio = width / height

            # Determine image type based on characteristics
            if aspect_ratio > 2.0:
                context_parts.append("panoramic or banner image")
            elif aspect_ratio < 0.5:
                context_parts.append("tall or portrait image")
            elif 0.9 <= aspect_ratio <= 1.1:
                context_parts.append("square image")

            # Analyze size category
            total_pixels = width * height
            if total_pixels > 2000000:  # > 2MP
                context_parts.append("high resolution")
            elif total_pixels < 100000:  # < 0.1MP
                context_parts.append("thumbnail or icon")

            # Analyze color information
            if image.mode == 'L':
                context_parts.append("grayscale")
            elif image.mode == 'RGBA':
                context_parts.append("with transparency")

            # Analyze filename for context
            filename = file_path.stem.lower()

            # Common image naming patterns
            patterns = {
                'screenshot': ['screenshot', 'screen', 'capture', 'snap'],
                'photo': ['photo', 'img', 'pic', 'picture', 'image'],
                'logo': ['logo', 'brand', 'icon', 'symbol'],
                'diagram': ['diagram', 'chart', 'graph', 'flow', 'schema'],
                'document': ['doc', 'page', 'scan', 'pdf', 'text'],
                'ui': ['ui', 'interface', 'mockup', 'wireframe', 'design']
            }

            for category, keywords in patterns.items():
                if any(keyword in filename for keyword in keywords):
                    context_parts.append(f"likely {category}")
                    break

            return ", ".join(context_parts) if context_parts else None

        except Exception as e:
            logger.debug(f"Error analyzing image context: {e}")
            return None

    def _extract_archive_metadata(self, file_path: Path) -> Optional[str]:
        """Extract metadata from archive files"""
        try:
            import zipfile
            import tarfile
            import os

            content_parts = []
            extension = file_path.suffix.lower()

            # Basic file info
            stat = file_path.stat()
            content_parts.append(f"Archive Type: {extension}")
            content_parts.append(f"Size: {stat.st_size / (1024*1024):.2f} MB")

            # Try to extract file list for common formats
            if extension == '.zip':
                try:
                    with zipfile.ZipFile(file_path, 'r') as zf:
                        file_list = zf.namelist()[:20]  # First 20 files
                        content_parts.append(f"Contains {len(zf.namelist())} files")
                        if file_list:
                            content_parts.append(f"Sample files: {', '.join(file_list[:5])}")
                except:
                    pass
            elif extension in ['.tar', '.gz', '.bz2', '.xz']:
                try:
                    with tarfile.open(file_path, 'r:*') as tf:
                        members = tf.getnames()[:20]
                        content_parts.append(f"Contains {len(tf.getnames())} files")
                        if members:
                            content_parts.append(f"Sample files: {', '.join(members[:5])}")
                except:
                    pass

            # Analyze filename for context
            filename = file_path.stem.lower()
            if any(keyword in filename for keyword in ['backup', 'archive', 'export', 'dump']):
                content_parts.append("Likely backup or export archive")
            elif any(keyword in filename for keyword in ['source', 'src', 'code']):
                content_parts.append("Likely source code archive")
            elif any(keyword in filename for keyword in ['data', 'dataset']):
                content_parts.append("Likely data archive")

            return "\n".join(content_parts) if content_parts else None

        except Exception as e:
            logger.debug(f"Error extracting archive metadata from {file_path}: {e}")
            return f"Archive file: {file_path.name}"

    def _extract_binary_metadata(self, file_path: Path) -> Optional[str]:
        """Extract metadata from binary files"""
        try:
            content_parts = []
            extension = file_path.suffix.lower()

            # Basic file info
            stat = file_path.stat()
            content_parts.append(f"Binary Type: {extension}")
            content_parts.append(f"Size: {stat.st_size / (1024*1024):.2f} MB")

            # Analyze filename for context
            filename = file_path.stem.lower()

            if extension in ['.exe', '.dll']:
                content_parts.append("Windows executable/library")
                if 'setup' in filename or 'install' in filename:
                    content_parts.append("Likely installer")
                elif 'update' in filename or 'patch' in filename:
                    content_parts.append("Likely update/patch")
            elif extension == '.so':
                content_parts.append("Linux shared library")
            elif extension == '.dylib':
                content_parts.append("macOS dynamic library")
            elif extension in ['.bin', '.dat']:
                content_parts.append("Binary data file")
                if 'config' in filename or 'settings' in filename:
                    content_parts.append("Likely configuration data")
                elif 'cache' in filename or 'temp' in filename:
                    content_parts.append("Likely cache/temporary data")

            return "\n".join(content_parts) if content_parts else None

        except Exception as e:
            logger.debug(f"Error extracting binary metadata from {file_path}: {e}")
            return f"Binary file: {file_path.name}"

    def _extract_font_metadata(self, file_path: Path) -> Optional[str]:
        """Extract metadata from font files"""
        try:
            content_parts = []
            extension = file_path.suffix.lower()

            # Basic file info
            stat = file_path.stat()
            content_parts.append(f"Font Type: {extension.upper()}")
            content_parts.append(f"Size: {stat.st_size / 1024:.1f} KB")

            # Analyze filename for font characteristics
            filename = file_path.stem.lower()

            # Font weight detection
            if any(weight in filename for weight in ['thin', 'light', 'regular', 'medium', 'bold', 'black']):
                for weight in ['thin', 'light', 'regular', 'medium', 'bold', 'black']:
                    if weight in filename:
                        content_parts.append(f"Weight: {weight.title()}")
                        break

            # Font style detection
            if 'italic' in filename:
                content_parts.append("Style: Italic")
            elif 'oblique' in filename:
                content_parts.append("Style: Oblique")

            # Font family detection
            font_families = ['arial', 'helvetica', 'times', 'courier', 'georgia', 'verdana', 'trebuchet']
            for family in font_families:
                if family in filename:
                    content_parts.append(f"Family: {family.title()}")
                    break

            return "\n".join(content_parts) if content_parts else None

        except Exception as e:
            logger.debug(f"Error extracting font metadata from {file_path}: {e}")
            return f"Font file: {file_path.name}"

    def _extract_certificate_metadata(self, file_path: Path) -> Optional[str]:
        """Extract metadata from certificate and key files"""
        try:
            content_parts = []
            extension = file_path.suffix.lower()

            # Basic file info
            stat = file_path.stat()
            content_parts.append(f"Certificate Type: {extension.upper()}")
            content_parts.append(f"Size: {stat.st_size} bytes")

            # Analyze filename for certificate characteristics
            filename = file_path.stem.lower()

            if any(keyword in filename for keyword in ['ca', 'root', 'authority']):
                content_parts.append("Likely Certificate Authority")
            elif any(keyword in filename for keyword in ['server', 'ssl', 'tls']):
                content_parts.append("Likely Server Certificate")
            elif any(keyword in filename for keyword in ['client', 'user']):
                content_parts.append("Likely Client Certificate")
            elif 'private' in filename or 'key' in filename:
                content_parts.append("Likely Private Key")
            elif 'public' in filename:
                content_parts.append("Likely Public Key")

            # Certificate format detection
            if extension in ['.pem', '.crt', '.cert']:
                content_parts.append("PEM/X.509 format")
            elif extension == '.der':
                content_parts.append("DER format")
            elif extension in ['.p12', '.pfx']:
                content_parts.append("PKCS#12 format")
            elif extension == '.jks':
                content_parts.append("Java KeyStore format")

            return "\n".join(content_parts) if content_parts else None

        except Exception as e:
            logger.debug(f"Error extracting certificate metadata from {file_path}: {e}")
            return f"Certificate file: {file_path.name}"

    def _extract_signature_metadata(self, file_path: Path) -> Optional[str]:
        """Extract metadata from signature files"""
        try:
            content_parts = []
            extension = file_path.suffix.lower()

            # Basic file info
            stat = file_path.stat()
            content_parts.append(f"Signature Type: {extension.upper()}")
            content_parts.append(f"Size: {stat.st_size} bytes")

            # Analyze filename for signature characteristics
            filename = file_path.stem.lower()

            if 'pgp' in filename or 'gpg' in filename:
                content_parts.append("PGP/GPG signature")
            elif 'detached' in filename:
                content_parts.append("Detached signature")
            elif 'inline' in filename:
                content_parts.append("Inline signature")

            # Try to read first few bytes to identify signature type
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(16)
                    if header.startswith(b'-----BEGIN PGP'):
                        content_parts.append("ASCII-armored PGP signature")
                    elif header[0:1] in [b'\x89', b'\x8a', b'\x8b']:
                        content_parts.append("Binary PGP signature")
            except:
                pass

            return "\n".join(content_parts) if content_parts else None

        except Exception as e:
            logger.debug(f"Error extracting signature metadata from {file_path}: {e}")
            return f"Signature file: {file_path.name}"
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize extracted content"""
        if not content:
            return ""
        
        # Remove excessive whitespace
        import re
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'\n\s*\n', '\n\n', content)
        
        # Remove control characters
        content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\t')
        
        return content.strip()
