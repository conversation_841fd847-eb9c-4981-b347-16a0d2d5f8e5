"""
Content extraction for various file types
Modern modular extraction system with specialized extractors
"""

import logging
from pathlib import Path
from typing import Optional
from .extractors.content_extractor_manager import ContentExtractorManager

logger = logging.getLogger(__name__)


class ContentExtractor:
    """Modern content extractor using specialized modular extractors"""
    
    def __init__(self):
        """Initialize content extractor with modular system"""
        self.manager = ContentExtractorManager()
        logger.info("Initialized modular content extraction system")
    
    def extract_content(self, file_path: str) -> Optional[str]:
        """Extract text content from a file using the modular system"""
        return self.manager.extract_content(file_path)
    
    def get_supported_extensions(self):
        """Get all supported extensions from the modular system"""
        return self.manager.get_all_supported_extensions()
    
    def get_extraction_stats(self):
        """Get extraction statistics from the modular system"""
        return self.manager.get_extraction_stats()
    
    def get_extractor_info(self):
        """Get detailed extractor information"""
        return self.manager.get_extractor_info()
    
    def supports_file(self, file_path):
        """Check if file is supported"""
        return self.manager.supports_file(Path(file_path))
    
    def test_extractors(self):
        """Test all extractors for functionality"""
        return self.manager.test_extractors()
    
    # Legacy method for backward compatibility
    def _get_mime_from_extension(self, extension: str) -> Optional[str]:
        """Get MIME type from file extension using modular system"""
        all_mappings = self.manager.get_all_mime_mappings()
        return all_mappings.get(extension.lower())
