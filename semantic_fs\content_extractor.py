"""
Content extraction for various file types
"""

import os
import mimetypes
from typing import Optional, Dict, Any
from pathlib import Path
import logging

# PDF processing
try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

# Document processing
try:
    from docx import Document
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

# Excel processing
try:
    import openpyxl
    HAS_EXCEL = True
except ImportError:
    HAS_EXCEL = False

# Image processing
try:
    from PIL import Image
    import pytesseract
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

import json
import csv
from xml.etree import ElementTree as ET

logger = logging.getLogger(__name__)

class ContentExtractor:
    """Extracts text content from various file types"""
    
    def __init__(self):
        """Initialize content extractor"""
        self.extractors = {
            'text/plain': self._extract_text,
            'text/markdown': self._extract_text,
            'application/json': self._extract_json,
            'text/csv': self._extract_csv,
            'application/xml': self._extract_xml,
            'text/xml': self._extract_xml,
            'text/html': self._extract_html,
            'text/css': self._extract_text,
            'application/javascript': self._extract_text,
            'text/javascript': self._extract_text,
            'application/python': self._extract_text,
            'text/x-python': self._extract_text,
        }
        
        if HAS_PDF:
            self.extractors['application/pdf'] = self._extract_pdf
        
        if HAS_DOCX:
            self.extractors['application/vnd.openxmlformats-officedocument.wordprocessingml.document'] = self._extract_docx
        
        if HAS_EXCEL:
            self.extractors['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'] = self._extract_excel
        
        if HAS_OCR:
            self.extractors['image/jpeg'] = self._extract_image_ocr
            self.extractors['image/png'] = self._extract_image_ocr
            self.extractors['image/tiff'] = self._extract_image_ocr
    
    def extract_content(self, file_path: str) -> Optional[str]:
        """Extract text content from a file"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return None
            
            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if not mime_type:
                # Try to determine from extension
                extension = file_path.suffix.lower()
                mime_type = self._get_mime_from_extension(extension)
            
            if not mime_type:
                logger.warning(f"Could not determine MIME type for: {file_path}")
                return None
            
            # Get appropriate extractor
            extractor = self.extractors.get(mime_type)
            if not extractor:
                logger.warning(f"No extractor available for MIME type: {mime_type}")
                return None
            
            # Extract content
            content = extractor(file_path)
            
            if content:
                # Clean and normalize content
                content = self._clean_content(content)
                logger.debug(f"Extracted {len(content)} characters from {file_path}")
                return content
            else:
                logger.warning(f"No content extracted from: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting content from {file_path}: {e}")
            return None
    
    def _get_mime_from_extension(self, extension: str) -> Optional[str]:
        """Get MIME type from file extension"""
        extension_map = {
            '.adoc': 'text/asciidoc',
            '.asciidoc': 'text/asciidoc',
            '.bash': 'application/x-sh',
            '.bat': 'application/x-msdos-program',
            '.c': 'text/x-csrc',
            '.cc': 'text/x-c++src',
            '.cfg': 'text/plain',
            '.changelog': 'text/plain',
            '.cjs': 'application/javascript',
            '.clj': 'text/x-clojure',
            '.conf': 'text/plain',
            '.config': 'text/plain',
            '.cpp': 'text/x-c++src',
            '.cs': 'text/x-csharp',
            '.css': 'text/css',
            '.csv': 'text/csv',
            '.cxx': 'text/x-c++src',
            '.dockerfile': 'text/plain',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.env': 'text/plain',
            '.go': 'text/x-go',
            '.h': 'text/x-chdr',
            '.hpp': 'text/x-c++hdr',
            '.hs': 'text/x-haskell',
            '.htm': 'text/html',
            '.html': 'text/html',
            '.ini': 'text/plain',
            '.java': 'text/x-java-source',
            '.js': 'application/javascript',
            '.json': 'application/json',
            '.json5': 'application/json5',
            '.jsonl': 'application/jsonlines',
            '.jsx': 'application/javascript',
            '.kt': 'text/x-kotlin',
            '.latex': 'text/x-tex',
            '.less': 'text/x-less',
            '.license': 'text/plain',
            '.log': 'text/plain',
            '.lua': 'text/x-lua',
            '.makefile': 'text/plain',
            '.markdown': 'text/markdown',
            '.md': 'text/markdown',
            '.mdown': 'text/markdown',
            '.mdx': 'text/markdown',
            '.mjs': 'application/javascript',
            '.mkd': 'text/markdown',
            '.ml': 'text/x-ocaml',
            '.pdf': 'application/pdf',
            '.php': 'application/x-php',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.properties': 'text/plain',
            '.ps1': 'application/x-powershell',
            '.py': 'text/x-python',
            '.pyi': 'text/x-python',
            '.pyw': 'text/x-python',
            '.r': 'text/x-r',
            '.rb': 'application/x-ruby',
            '.readme': 'text/plain',
            '.rest': 'text/x-rst',
            '.rs': 'text/x-rust',
            '.rst': 'text/x-rst',
            '.sass': 'text/x-sass',
            '.scala': 'text/x-scala',
            '.scss': 'text/x-scss',
            '.sh': 'application/x-sh',
            '.sql': 'application/sql',
            '.swift': 'text/x-swift',
            '.tex': 'text/x-tex',
            '.toml': 'application/toml',
            '.ts': 'application/typescript',
            '.tsv': 'text/tab-separated-values',
            '.tsx': 'application/typescript',
            '.map': 'application/json',
            '.txt': 'text/plain',
            '.xhtml': 'application/xhtml+xml',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xml': 'application/xml',
            '.yaml': 'application/x-yaml',
            '.yml': 'application/x-yaml',
        }
        return extension_map.get(extension)
    
    def _extract_text(self, file_path: Path) -> Optional[str]:
        """Extract content from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return None
    
    def _extract_json(self, file_path: Path) -> Optional[str]:
        """Extract content from JSON files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert JSON to readable text
                return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            return None
    
    def _extract_csv(self, file_path: Path) -> Optional[str]:
        """Extract content from CSV files"""
        try:
            content_lines = []
            with open(file_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.reader(f)
                for i, row in enumerate(reader):
                    if i < 100:  # Limit to first 100 rows
                        content_lines.append(' | '.join(row))
                    else:
                        break
            return '\n'.join(content_lines)
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return None
    
    def _extract_xml(self, file_path: Path) -> Optional[str]:
        """Extract content from XML files"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Extract all text content
            text_content = []
            for elem in root.iter():
                if elem.text and elem.text.strip():
                    text_content.append(elem.text.strip())
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading XML file {file_path}: {e}")
            return None
    
    def _extract_html(self, file_path: Path) -> Optional[str]:
        """Extract content from HTML files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Simple HTML tag removal (for basic extraction)
            import re
            # Remove script and style elements
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
            # Remove HTML tags
            content = re.sub(r'<[^>]+>', '', content)
            # Clean up whitespace
            content = re.sub(r'\s+', ' ', content)
            
            return content.strip()
        except Exception as e:
            logger.error(f"Error reading HTML file {file_path}: {e}")
            return None
    
    def _extract_pdf(self, file_path: Path) -> Optional[str]:
        """Extract content from PDF files"""
        if not HAS_PDF:
            logger.warning("PyPDF2 not available for PDF extraction")
            return None
        
        try:
            text_content = []
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {e}")
            return None
    
    def _extract_docx(self, file_path: Path) -> Optional[str]:
        """Extract content from DOCX files"""
        if not HAS_DOCX:
            logger.warning("python-docx not available for DOCX extraction")
            return None
        
        try:
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading DOCX file {file_path}: {e}")
            return None
    
    def _extract_excel(self, file_path: Path) -> Optional[str]:
        """Extract content from Excel files"""
        if not HAS_EXCEL:
            logger.warning("openpyxl not available for Excel extraction")
            return None
        
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text_content = []
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text_content.append(f"Sheet: {sheet_name}")
                
                for row in sheet.iter_rows(max_row=100, values_only=True):  # Limit to first 100 rows
                    row_text = ' | '.join([str(cell) if cell is not None else '' for cell in row])
                    if row_text.strip():
                        text_content.append(row_text)
            
            return '\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading Excel file {file_path}: {e}")
            return None
    
    def _extract_image_ocr(self, file_path: Path) -> Optional[str]:
        """Extract text from images using OCR"""
        if not HAS_OCR:
            logger.warning("PIL/pytesseract not available for OCR")
            return None
        
        try:
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            return text.strip() if text else None
        except Exception as e:
            logger.error(f"Error performing OCR on {file_path}: {e}")
            return None
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize extracted content"""
        if not content:
            return ""
        
        # Remove excessive whitespace
        import re
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'\n\s*\n', '\n\n', content)
        
        # Remove control characters
        content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\t')
        
        return content.strip()
