# Semantic Filesystem Configuration
# Copy this file to .env and customize the settings

# =============================================================================
# LLM Configuration
# =============================================================================
# Choose between Ollama (local) or OpenAI (cloud) for LLM processing

# Ollama Settings (Recommended - Local LLM)
USE_OLLAMA=true
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=phi  # Options: phi (fast), mistral (better quality), llama2, etc.

# OpenAI Settings (Fallback - Cloud LLM)
# Only used if Ollama is not available or USE_OLLAMA=false
OPENAI_API_KEY=your_openai_api_key_here
LLM_MODEL=gpt-3.5-turbo

# =============================================================================
# Database Configuration
# =============================================================================
# SQLite database for file metadata (default is fine for most users)
DATABASE_URL=sqlite:///./semantic_fs.db

# Vector database path for embeddings storage
VECTOR_DB_PATH=./vector_db

# =============================================================================
# Embedding Configuration
# =============================================================================
# Sentence transformer model for semantic embeddings
# Options: all-MiniLM-L6-v2 (fast), all-mpnet-base-v2 (better quality)
EMBEDDING_MODEL=all-MiniLM-L6-v2

# =============================================================================
# File Processing Configuration
# =============================================================================
# Maximum file size to process (in bytes)
MAX_FILE_SIZE=104857600  # 100MB

# Enable automatic tag generation
AUTO_TAG_ENABLED=true

# Enable content summarization
CONTENT_SUMMARY_ENABLED=true

# Enable relationship tracking
RELATIONSHIP_TRACKING_ENABLED=true

# =============================================================================
# API Server Configuration
# =============================================================================
# Host and port for the web API
API_HOST=localhost
API_PORT=8000

# CORS origins (comma-separated)
CORS_ORIGINS=*

# =============================================================================
# Logging Configuration
# =============================================================================
# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Enable file logging (logs to files in logs/ directory)
ENABLE_FILE_LOGGING=true

# Enable structured JSON logging (better for log aggregation)
ENABLE_JSON_LOGGING=false

# Enable log file rotation (prevents files from growing too large)
ENABLE_LOG_ROTATION=true

# Maximum log file size in bytes (default: 10MB)
MAX_LOG_FILE_SIZE=10485760

# Number of backup log files to keep
LOG_BACKUP_COUNT=5

# =============================================================================
# Security Configuration
# =============================================================================
# Enable authentication (set to true for production)
AUTH_ENABLED=false

# Require authentication for read operations
REQUIRE_AUTH_FOR_READ=false

# Require authentication for write operations (recommended)
REQUIRE_AUTH_FOR_WRITE=true

# JWT secret key (auto-generated if empty)
JWT_SECRET_KEY=

# API keys (comma-separated key:name pairs)
# Example: API_KEYS=abc123:admin,def456:readonly
API_KEYS=

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Security headers
SECURITY_HEADERS_ENABLED=true

# =============================================================================
# File System Monitoring (Future Feature)
# =============================================================================
# Directories to monitor for automatic indexing (comma-separated)
# WATCH_DIRECTORIES=/path/to/documents,/path/to/projects
