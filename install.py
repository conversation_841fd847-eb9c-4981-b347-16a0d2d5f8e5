#!/usr/bin/env python3
"""
Installation script for Semantic Filesystem
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, but you have {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor} is compatible")
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing dependencies...")
    
    # Core dependencies that should work on most systems
    core_deps = [
        "fastapi>=0.104.1",
        "uvicorn>=0.24.0", 
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "sqlalchemy>=2.0.23",
        "click>=8.1.7",
        "rich>=13.7.0",
        "python-dotenv>=1.0.0",
    ]
    
    # Try to install core dependencies first
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    
    # Vector database and ML dependencies (may require compilation)
    ml_deps = [
        "chromadb>=0.4.18",
        "sentence-transformers>=2.2.2",
    ]
    
    print("\n🤖 Installing ML dependencies (this may take a few minutes)...")
    for dep in ml_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Warning: Failed to install {dep}")
            print("   The system will work with reduced functionality")
    
    # Optional dependencies
    optional_deps = [
        ("openai>=1.3.7", "OpenAI integration"),
        ("PyPDF2>=3.0.1", "PDF processing"),
        ("python-docx>=1.1.0", "Word document processing"),
        ("openpyxl>=3.1.2", "Excel processing"),
        ("Pillow>=10.1.0", "Image processing"),
    ]
    
    print("\n📄 Installing file processing dependencies...")
    for dep, description in optional_deps:
        if not run_command(f"pip install {dep}", f"Installing {description}"):
            print(f"⚠️  Warning: Failed to install {dep}")
            print(f"   {description} will not be available")
    
    return True

def setup_environment():
    """Set up environment configuration"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("⚙️  Setting up environment configuration...")
        try:
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ Created .env file from template")
            print("💡 Edit .env file to configure your OpenAI API key and other settings")
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    
    return True

def test_installation():
    """Test if the installation works"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        from semantic_fs import SemanticFilesystem
        print("✅ Core modules imported successfully")
        
        # Test basic functionality
        semantic_fs = SemanticFilesystem()
        print("✅ Semantic filesystem initialized successfully")
        
        return True
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def main():
    """Main installation process"""
    print("🧠 Semantic Filesystem Installation")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed due to dependency issues")
        print("💡 Try installing dependencies manually:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        print("\n⚠️  Environment setup had issues, but installation can continue")
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed")
        print("💡 The system may still work, but some features might be unavailable")
    
    print("\n🎉 Installation completed!")
    print("\n🚀 Quick start:")
    print("   1. Edit .env file with your OpenAI API key (optional)")
    print("   2. Test the system: python test_semantic_fs.py")
    print("   3. Start the web interface: python main.py serve")
    print("   4. Or use CLI: python main.py search 'your query here'")
    
    print("\n📚 Documentation:")
    print("   - README.md for detailed usage")
    print("   - .env.example for configuration options")
    print("   - python main.py --help for CLI commands")

if __name__ == "__main__":
    main()
