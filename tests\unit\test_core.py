"""
Unit tests for the core semantic filesystem functionality
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import os

from semantic_fs.core import SemanticFilesystem
from semantic_fs.models import SemanticQuery, FileRecord
from semantic_fs.exceptions import FileSystemError, FileProcessingError


@pytest.mark.unit
class TestSemanticFilesystem:
    """Test the main SemanticFilesystem class"""
    
    def test_singleton_pattern(self, semantic_fs_with_mocks):
        """Test that SemanticFilesystem follows singleton pattern"""
        fs1 = SemanticFilesystem()
        fs2 = SemanticFilesystem()
        assert fs1 is fs2
    
    def test_initialization(self, semantic_fs_with_mocks):
        """Test semantic filesystem initialization"""
        fs = semantic_fs_with_mocks
        assert fs._initialized is True
        assert hasattr(fs, 'embedding_engine')
        assert hasattr(fs, 'content_extractor')
        assert hasattr(fs, 'llm_processor')
    
    def test_index_file_success(self, semantic_fs_with_mocks, test_files):
        """Test successful file indexing"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # Mock file record creation
            mock_file_record = Mock(spec=FileRecord)
            mock_file_record.id = 1
            mock_db.query.return_value.filter.return_value.first.return_value = None
            mock_db.add.return_value = None
            mock_db.flush.return_value = None
            
            result = fs.index_file(str(test_file))
            
            # Verify the mocks were called
            assert fs.content_extractor.extract_content.called
            assert fs.embedding_engine.generate_embedding.called
            assert fs.llm_processor.generate_tags.called
            assert fs.llm_processor.generate_summary.called
    
    def test_index_file_nonexistent(self, semantic_fs_with_mocks):
        """Test indexing a non-existent file"""
        fs = semantic_fs_with_mocks
        
        with pytest.raises(FileSystemError) as exc_info:
            fs.index_file("/nonexistent/file.txt")
        
        assert "File does not exist" in str(exc_info.value)
    
    def test_index_file_too_large(self, semantic_fs_with_mocks, temp_dir):
        """Test indexing a file that's too large"""
        fs = semantic_fs_with_mocks
        
        # Create a large file
        large_file = temp_dir / "large_file.txt"
        large_file.write_text("x" * (100 * 1024 * 1024 + 1))  # Larger than max size
        
        with pytest.raises(FileProcessingError) as exc_info:
            fs.index_file(str(large_file))
        
        assert "File too large" in str(exc_info.value)
    
    def test_query_success(self, semantic_fs_with_mocks):
        """Test successful query execution"""
        fs = semantic_fs_with_mocks
        query = SemanticQuery(query="test document", limit=5)
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # Mock database query results
            mock_file_record = Mock(spec=FileRecord)
            mock_file_record.id = 1
            mock_file_record.path = "/test/file.txt"
            mock_file_record.filename = "file.txt"
            mock_file_record.file_type = "text/plain"
            mock_file_record.size = 1000
            mock_file_record.content_summary = "Test summary"
            mock_file_record.auto_tags = ["test", "document"]
            mock_file_record.user_tags = []
            
            mock_db.query.return_value.filter.return_value.all.return_value = [mock_file_record]
            
            result = fs.query(query)
            
            assert result is not None
            assert result.query == query.query
            assert isinstance(result.execution_time, float)
            assert result.execution_time >= 0
    
    def test_query_empty_string(self, semantic_fs_with_mocks):
        """Test query with empty string"""
        fs = semantic_fs_with_mocks
        query = SemanticQuery(query="", limit=5)
        
        result = fs.query(query)
        
        assert result.total_found == 0
        assert len(result.results) == 0
    
    def test_query_with_context(self, semantic_fs_with_mocks, test_files):
        """Test indexing file with context"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        context = {
            "project": {
                "name": "Test Project",
                "type": "research"
            }
        }
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            result = fs.index_file(str(test_file), context=context)
            
            # Verify context was processed
            assert fs.content_extractor.extract_content.called
    
    def test_file_permissions(self, semantic_fs_with_mocks, temp_dir):
        """Test handling of file permission errors"""
        fs = semantic_fs_with_mocks
        
        # Create a file and make it unreadable (on Unix systems)
        test_file = temp_dir / "unreadable.txt"
        test_file.write_text("test content")
        
        # Mock os.access to return False (no read permission)
        with patch('os.access', return_value=False):
            with pytest.raises(FileSystemError) as exc_info:
                fs.index_file(str(test_file))
            
            assert "not readable" in str(exc_info.value)
    
    def test_update_existing_file(self, semantic_fs_with_mocks, test_files):
        """Test updating an existing file in the index"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            
            # Mock existing file record
            existing_record = Mock(spec=FileRecord)
            existing_record.id = 1
            existing_record.path = str(test_file)
            existing_record.embedding_id = "existing_embedding"
            
            mock_db.query.return_value.filter.return_value.first.return_value = existing_record
            
            result = fs.index_file(str(test_file))
            
            # Verify update process
            assert fs.embedding_engine.delete_embedding.called
            assert fs.content_extractor.extract_content.called


@pytest.mark.unit
class TestSemanticQuery:
    """Test the SemanticQuery model"""
    
    def test_valid_query(self):
        """Test creating a valid query"""
        query = SemanticQuery(query="test query", limit=10)
        assert query.query == "test query"
        assert query.limit == 10
        assert query.filters is None
        assert query.include_content is False
    
    def test_query_with_filters(self):
        """Test query with filters"""
        filters = {"file_type": "pdf", "size_min": 1000}
        query = SemanticQuery(
            query="test query",
            limit=5,
            filters=filters,
            include_content=True
        )
        assert query.filters == filters
        assert query.include_content is True
    
    def test_query_validation(self):
        """Test query validation"""
        # Test minimum limit
        query = SemanticQuery(query="test", limit=1)
        assert query.limit == 1
        
        # Test maximum limit
        query = SemanticQuery(query="test", limit=100)
        assert query.limit == 100
    
    def test_default_values(self):
        """Test default values"""
        query = SemanticQuery(query="test")
        assert query.limit == 10
        assert query.filters is None
        assert query.include_content is False


@pytest.mark.unit
class TestErrorHandling:
    """Test error handling in core functionality"""
    
    def test_content_extraction_error(self, semantic_fs_with_mocks, test_files):
        """Test handling of content extraction errors"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        # Mock content extractor to raise an exception
        fs.content_extractor.extract_content.side_effect = Exception("Extraction failed")
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            result = fs.index_file(str(test_file))
            
            # Should handle the error gracefully
            assert result is None or hasattr(result, 'id')
    
    def test_embedding_generation_error(self, semantic_fs_with_mocks, test_files):
        """Test handling of embedding generation errors"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        # Mock embedding engine to raise an exception
        fs.embedding_engine.generate_embedding.side_effect = Exception("Embedding failed")
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            mock_db = MagicMock()
            mock_get_db.return_value.__enter__.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            result = fs.index_file(str(test_file))
            
            # Should handle the error gracefully
            assert result is None or hasattr(result, 'id')
    
    def test_database_error_handling(self, semantic_fs_with_mocks, test_files):
        """Test handling of database errors"""
        fs = semantic_fs_with_mocks
        test_file = test_files["test_document.txt"]
        
        with patch('semantic_fs.core.get_db') as mock_get_db:
            # Mock database to raise an exception
            mock_get_db.side_effect = Exception("Database connection failed")
            
            with pytest.raises(Exception):
                fs.index_file(str(test_file))
