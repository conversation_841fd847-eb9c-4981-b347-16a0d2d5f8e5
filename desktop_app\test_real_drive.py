"""
REAL-WORLD STRESS TEST: Index actual C: drive
This will test the desktop engine with your actual file system
"""

import os
import sys
import time
import psutil
from pathlib import Path
from datetime import datetime

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine, IndexingProgress


def get_system_info():
    """Get system information for the test"""
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('C:\\')
    
    return {
        'total_memory_gb': memory.total / (1024**3),
        'available_memory_gb': memory.available / (1024**3),
        'cpu_count': psutil.cpu_count(),
        'disk_total_gb': disk.total / (1024**3),
        'disk_used_gb': disk.used / (1024**3),
        'disk_free_gb': disk.free / (1024**3)
    }


def test_c_drive_indexing():
    """Test indexing the actual C: drive"""
    print("🚀 REAL-WORLD C: DRIVE INDEXING TEST")
    print("=" * 60)
    
    # System info
    sys_info = get_system_info()
    print(f"💻 System Info:")
    print(f"   RAM: {sys_info['total_memory_gb']:.1f}GB total, {sys_info['available_memory_gb']:.1f}GB available")
    print(f"   CPU: {sys_info['cpu_count']} cores")
    print(f"   C: Drive: {sys_info['disk_used_gb']:.1f}GB used / {sys_info['disk_total_gb']:.1f}GB total")
    print()
    
    # Initialize engine
    config_dir = Path.home() / ".semantic_fs_test"
    config_dir.mkdir(exist_ok=True)
    
    print(f"📁 Config directory: {config_dir}")
    
    engine = DesktopSemanticEngine(config_dir)
    
    # Test 1: Scope estimation for C: drive
    print("🔍 PHASE 1: Estimating C: drive scope...")
    print("   This will scan your entire C: drive to count files")
    print("   (This is safe - no files will be modified)")
    
    c_drive = Path("C:\\")
    
    start_time = time.time()
    try:
        scope = engine.estimate_indexing_scope([c_drive])
        estimation_time = time.time() - start_time
        
        print(f"\n📊 C: DRIVE ANALYSIS RESULTS:")
        print(f"   Total indexable files: {scope['total_files']:,}")
        print(f"   Total size: {scope['total_size_gb']:.2f} GB")
        print(f"   File types found: {len(scope['file_types'])}")
        print(f"   Estimation time: {estimation_time:.1f} seconds")
        print(f"   Estimated indexing time: {scope['estimated_time_hours']:.1f} hours")
        
        # Show top file types
        print(f"\n📋 Top file types:")
        sorted_types = sorted(scope['file_types'].items(), key=lambda x: x[1], reverse=True)
        for ext, count in sorted_types[:10]:
            print(f"   {ext or 'no extension'}: {count:,} files")
        
        # Memory check
        current_memory = psutil.Process().memory_info().rss / (1024**3)
        print(f"\n💾 Memory usage after estimation: {current_memory:.2f} GB")
        
        # Ask user if they want to proceed with actual indexing
        if scope['total_files'] > 100000:
            print(f"\n⚠️  WARNING: Found {scope['total_files']:,} files!")
            print(f"   This is a LARGE dataset that will take ~{scope['estimated_time_hours']:.1f} hours")
            print(f"   Recommended: Start with a smaller test first")
            
            proceed = input("\n🤔 Proceed with full C: drive indexing? (y/N): ").lower().strip()
            if proceed != 'y':
                print("✅ Scope estimation completed successfully!")
                print("💡 Consider testing with a smaller directory first (e.g., Documents folder)")
                return True
        
        # Test 2: Actual indexing (if user agrees)
        print(f"\n🚀 PHASE 2: Starting actual indexing...")
        print(f"   This will index {scope['total_files']:,} files")
        print(f"   Press Ctrl+C to stop at any time")
        
        # Progress tracking
        progress_updates = []
        last_update_time = time.time()
        
        def progress_callback(progress: IndexingProgress):
            nonlocal last_update_time, progress_updates
            
            current_time = time.time()
            progress_updates.append({
                'time': current_time,
                'processed': progress.processed_files,
                'total': progress.total_files,
                'percentage': progress.progress_percentage,
                'files_per_second': progress.files_per_second,
                'memory_gb': psutil.Process().memory_info().rss / (1024**3)
            })
            
            # Update every 10 seconds
            if current_time - last_update_time >= 10:
                print(f"   📈 Progress: {progress.processed_files:,}/{progress.total_files:,} "
                      f"({progress.progress_percentage:.1f}%) - "
                      f"{progress.files_per_second:.1f} files/sec - "
                      f"Memory: {progress_updates[-1]['memory_gb']:.2f}GB")
                last_update_time = current_time
        
        # Start indexing
        indexing_start_time = time.time()
        indexing_started = engine.start_full_indexing([c_drive], progress_callback)
        
        if not indexing_started:
            print("❌ Failed to start indexing")
            return False
        
        print("✅ Indexing started successfully!")
        
        # Monitor progress
        try:
            while engine.is_indexing:
                time.sleep(1)
                
                # Check memory usage
                current_memory = psutil.Process().memory_info().rss / (1024**3)
                if current_memory > 8:  # 8GB limit
                    print(f"\n⚠️  High memory usage: {current_memory:.2f}GB")
                    print("   Consider stopping and optimizing for large datasets")
        
        except KeyboardInterrupt:
            print(f"\n🛑 Indexing interrupted by user")
            engine.stop_indexing()
            time.sleep(2)
        
        # Final results
        indexing_time = time.time() - indexing_start_time
        stats = engine.get_indexing_stats()
        
        print(f"\n🎉 INDEXING COMPLETED!")
        print(f"   Files indexed: {stats['total_indexed_files']:,}")
        print(f"   Time taken: {indexing_time/60:.1f} minutes")
        print(f"   Average speed: {stats['total_indexed_files']/(indexing_time/60):.1f} files/minute")
        print(f"   Final memory usage: {stats['memory_usage_gb']:.2f} GB")
        
        # Test search on real data
        if stats['total_indexed_files'] > 0:
            print(f"\n🔍 Testing search on real data...")
            
            test_queries = [
                "python code",
                "configuration file", 
                "document",
                "image photo",
                "readme"
            ]
            
            for query in test_queries:
                try:
                    results = engine.search_files(query, limit=3)
                    print(f"   '{query}': {len(results)} results")
                    if results:
                        print(f"      Best match: {Path(results[0]['path']).name} (score: {results[0]['relevance_score']:.3f})")
                except Exception as e:
                    print(f"   '{query}': Search error - {e}")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_documents_folder():
    """Test with just the Documents folder (safer option)"""
    print("📁 DOCUMENTS FOLDER TEST (Safer Option)")
    print("=" * 50)
    
    documents_path = Path.home() / "Documents"
    if not documents_path.exists():
        print("❌ Documents folder not found")
        return False
    
    config_dir = Path.home() / ".semantic_fs_test"
    config_dir.mkdir(exist_ok=True)
    
    engine = DesktopSemanticEngine(config_dir)
    
    print(f"🔍 Analyzing Documents folder: {documents_path}")
    
    try:
        scope = engine.estimate_indexing_scope([documents_path])
        
        print(f"📊 DOCUMENTS FOLDER ANALYSIS:")
        print(f"   Files: {scope['total_files']:,}")
        print(f"   Size: {scope['total_size_gb']:.2f} GB")
        print(f"   Estimated time: {scope['estimated_time_hours']:.1f} hours")
        
        if scope['total_files'] > 0:
            proceed = input(f"\n🤔 Index {scope['total_files']:,} files from Documents? (y/N): ").lower().strip()
            if proceed == 'y':
                print("🚀 Starting Documents indexing...")
                
                def progress_callback(progress):
                    if progress.processed_files % 100 == 0:
                        print(f"   Progress: {progress.processed_files}/{progress.total_files} ({progress.progress_percentage:.1f}%)")
                
                engine.start_full_indexing([documents_path], progress_callback)
                
                while engine.is_indexing:
                    time.sleep(1)
                
                stats = engine.get_indexing_stats()
                print(f"✅ Indexed {stats['total_indexed_files']:,} files from Documents!")
                
                # Test search
                results = engine.search_files("document", limit=5)
                print(f"🔍 Search test: Found {len(results)} results for 'document'")
        
        return True
        
    except Exception as e:
        print(f"❌ Documents test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 REAL-WORLD DRIVE INDEXING TEST")
    print("=" * 60)
    print("Choose your test:")
    print("1. Full C: drive indexing (STRESS TEST)")
    print("2. Documents folder only (SAFER)")
    print("3. Exit")
    
    choice = input("\nEnter choice (1/2/3): ").strip()
    
    if choice == "1":
        return test_c_drive_indexing()
    elif choice == "2":
        return test_documents_folder()
    elif choice == "3":
        print("👋 Exiting...")
        return True
    else:
        print("❌ Invalid choice")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 TEST COMPLETED!")
        else:
            print("\n❌ TEST FAILED!")
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
