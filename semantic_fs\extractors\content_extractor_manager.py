"""
Content Extractor Manager
Coordinates all specialized extractors and provides unified interface
"""

import logging
import mimetypes
from pathlib import Path
from typing import Optional, Dict, Set, List
from .base_extractor import BaseExtractor
from .text_extractor import TextExtractor
from .image_extractor import ImageExtractor
from .archive_extractor import ArchiveExtractor
from .binary_extractor import BinaryExtractor
from .specialized_extractor import SpecializedExtractor

logger = logging.getLogger(__name__)


class ContentExtractorManager:
    """Manages all content extractors and provides unified extraction interface"""
    
    def __init__(self):
        """Initialize the content extractor manager"""
        self.extractors: List[BaseExtractor] = []
        self.extension_to_extractor: Dict[str, BaseExtractor] = {}
        self.mime_to_extractor: Dict[str, BaseExtractor] = {}
        
        # Initialize all extractors
        self._initialize_extractors()
        
        # Build lookup tables
        self._build_lookup_tables()
    
    def _initialize_extractors(self):
        """Initialize all specialized extractors"""
        try:
            # Order matters - more specific extractors first
            self.extractors = [
                ImageExtractor(),
                ArchiveExtractor(),
                BinaryExtractor(),
                SpecializedExtractor(),
                TextExtractor(),  # Text extractor last as fallback
            ]
            
            logger.info(f"Initialized {len(self.extractors)} content extractors")
            
        except Exception as e:
            logger.error(f"Error initializing extractors: {e}")
            # Fallback to text extractor only
            self.extractors = [TextExtractor()]
    
    def _build_lookup_tables(self):
        """Build lookup tables for fast extractor selection"""
        try:
            for extractor in self.extractors:
                # Map extensions to extractors
                for extension in extractor.get_supported_extensions():
                    if extension not in self.extension_to_extractor:
                        self.extension_to_extractor[extension] = extractor
                
                # Map MIME types to extractors
                for extension, mime_type in extractor.get_mime_mappings().items():
                    if mime_type not in self.mime_to_extractor:
                        self.mime_to_extractor[mime_type] = extractor
            
            logger.info(f"Built lookup tables: {len(self.extension_to_extractor)} extensions, {len(self.mime_to_extractor)} MIME types")
            
        except Exception as e:
            logger.error(f"Error building lookup tables: {e}")
    
    def get_all_supported_extensions(self) -> Set[str]:
        """Get all supported file extensions across all extractors"""
        all_extensions = set()
        for extractor in self.extractors:
            all_extensions.update(extractor.get_supported_extensions())
        return all_extensions
    
    def get_all_mime_mappings(self) -> Dict[str, str]:
        """Get all MIME type mappings across all extractors"""
        all_mappings = {}
        for extractor in self.extractors:
            all_mappings.update(extractor.get_mime_mappings())
        return all_mappings
    
    def supports_file(self, file_path: Path) -> bool:
        """Check if any extractor supports the given file"""
        extension = file_path.suffix.lower()
        return extension in self.extension_to_extractor
    
    def get_extractor_for_file(self, file_path: Path) -> Optional[BaseExtractor]:
        """Get the appropriate extractor for a file"""
        try:
            extension = file_path.suffix.lower()
            
            # First try direct extension lookup
            if extension in self.extension_to_extractor:
                return self.extension_to_extractor[extension]
            
            # Try MIME type lookup
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and mime_type in self.mime_to_extractor:
                return self.mime_to_extractor[mime_type]
            
            # Try custom MIME type detection
            for extractor in self.extractors:
                custom_mime = extractor.get_mime_type(extension)
                if custom_mime and custom_mime in self.mime_to_extractor:
                    return self.mime_to_extractor[custom_mime]
            
            return None
            
        except Exception as e:
            logger.debug(f"Error getting extractor for {file_path}: {e}")
            return None
    
    def extract_content(self, file_path: str) -> Optional[str]:
        """Extract content from a file using the appropriate extractor"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return None
            
            # Get appropriate extractor
            extractor = self.get_extractor_for_file(file_path)
            if not extractor:
                logger.warning(f"No extractor available for: {file_path}")
                return None
            
            # Extract content
            content = extractor.extract_content(file_path)
            
            if content:
                logger.debug(f"Extracted {len(content)} characters from {file_path} using {extractor.__class__.__name__}")
                return content
            else:
                logger.warning(f"No content extracted from: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting content from {file_path}: {e}")
            return None
    
    def get_extraction_stats(self) -> Dict[str, int]:
        """Get statistics about supported file types"""
        stats = {}
        
        for extractor in self.extractors:
            extractor_name = extractor.__class__.__name__
            extension_count = len(extractor.get_supported_extensions())
            mime_count = len(extractor.get_mime_mappings())
            
            stats[extractor_name] = {
                'extensions': extension_count,
                'mime_types': mime_count
            }
        
        # Overall stats
        stats['total'] = {
            'extensions': len(self.get_all_supported_extensions()),
            'mime_types': len(self.get_all_mime_mappings()),
            'extractors': len(self.extractors)
        }
        
        return stats
    
    def get_extractor_info(self) -> Dict[str, Dict]:
        """Get detailed information about each extractor"""
        info = {}
        
        for extractor in self.extractors:
            extractor_name = extractor.__class__.__name__
            extensions = sorted(list(extractor.get_supported_extensions()))
            
            info[extractor_name] = {
                'extensions': extensions,
                'extension_count': len(extensions),
                'mime_types': len(extractor.get_mime_mappings()),
                'description': extractor.__doc__.strip().split('\n')[0] if extractor.__doc__ else "No description"
            }
        
        return info
    
    def test_extractors(self) -> Dict[str, bool]:
        """Test all extractors for basic functionality"""
        results = {}
        
        for extractor in self.extractors:
            extractor_name = extractor.__class__.__name__
            
            try:
                # Test basic methods
                extensions = extractor.get_supported_extensions()
                mime_mappings = extractor.get_mime_mappings()
                
                # Basic validation
                is_valid = (
                    isinstance(extensions, set) and
                    isinstance(mime_mappings, dict) and
                    len(extensions) > 0 and
                    len(mime_mappings) > 0
                )
                
                results[extractor_name] = is_valid
                
            except Exception as e:
                logger.error(f"Error testing {extractor_name}: {e}")
                results[extractor_name] = False
        
        return results
    
    def get_mime_type(self, file_path: Path) -> Optional[str]:
        """Get MIME type for a file using all available methods"""
        try:
            extension = file_path.suffix.lower()
            
            # Try system MIME type detection first
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type:
                return mime_type
            
            # Try custom MIME type mappings
            all_mappings = self.get_all_mime_mappings()
            if extension in all_mappings:
                return all_mappings[extension]
            
            return None
            
        except Exception as e:
            logger.debug(f"Error getting MIME type for {file_path}: {e}")
            return None
