{"openapi": "3.0.0", "info": {"title": "Semantic Filesystem API", "version": "1.0.0", "description": "API for semantic file search and management"}, "servers": [{"url": "http://localhost:8000", "description": "Development server"}], "paths": {"/api/search": {"post": {"summary": "Search files using natural language", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Natural language search query"}, "limit": {"type": "integer", "default": 10, "description": "Maximum number of results"}}, "required": ["query"]}}}}, "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string"}, "results": {"type": "array", "items": {"type": "object", "properties": {"file": {"type": "object", "properties": {"id": {"type": "integer"}, "filename": {"type": "string"}, "path": {"type": "string"}, "content_summary": {"type": "string"}, "auto_tags": {"type": "array", "items": {"type": "string"}}}}, "relevance_score": {"type": "number"}, "match_reason": {"type": "string"}}}}, "total_found": {"type": "integer"}, "execution_time": {"type": "number"}}}}}}}}}, "/api/index": {"post": {"summary": "Index a file", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"file_path": {"type": "string"}, "context": {"type": "object"}}, "required": ["file_path"]}}}}, "responses": {"200": {"description": "File indexed successfully"}}}}}}