#!/usr/bin/env python3

import pandas as pd
import numpy as np

def process_customer_data(filename):
    """Process customer data from CSV file"""
    df = pd.read_csv(filename)
    # Clean and process data
    df = df.dropna()
    df['total_spent'] = df['quantity'] * df['price']
    return df

if __name__ == '__main__':
    data = process_customer_data('customers.csv')
    print(f'Processed {len(data)} customer records')