#!/usr/bin/env python3
"""
Simple test script for the Semantic Filesystem
"""

import os
import tempfile
from pathlib import Path
import sys

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from semantic_fs import SemanticFilesystem, SemanticQuery

def create_test_files():
    """Create some test files for demonstration"""
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    # Create test text files
    files = [
        ("ai_research_paper.txt", "This paper discusses the latest advances in artificial intelligence and machine learning. We explore transformer architectures, attention mechanisms, and their applications in natural language processing. The research was conducted over 6 months and shows promising results for future AI development."),
        
        ("budget_report_q3.txt", "Quarterly Budget Report Q3 2023\n\nRevenue: $2.5M\nExpenses: $1.8M\nProfit: $700K\n\nKey highlights:\n- 15% growth in sales\n- Reduced operational costs\n- Successful product launch"),
        
        ("python_data_processor.py", "#!/usr/bin/env python3\n\nimport pandas as pd\nimport numpy as np\n\ndef process_customer_data(filename):\n    \"\"\"Process customer data from CSV file\"\"\"\n    df = pd.read_csv(filename)\n    # Clean and process data\n    df = df.dropna()\n    df['total_spent'] = df['quantity'] * df['price']\n    return df\n\nif __name__ == '__main__':\n    data = process_customer_data('customers.csv')\n    print(f'Processed {len(data)} customer records')"),
        
        ("video_script_ptx.txt", "YouTube Video Script: PTX Tutorial\n\nIntroduction:\nWelcome to this comprehensive tutorial on PTX (Parallel Thread Execution). In this video, we'll cover the fundamentals of GPU programming and how PTX enables low-level control over NVIDIA GPUs.\n\nMain Content:\n- PTX instruction set\n- Memory management\n- Thread synchronization\n- Performance optimization\n\nThis script was created 6 months ago for our AI programming series."),
        
        ("meeting_notes.txt", "Team Meeting Notes - Product Launch\nDate: 2023-10-15\n\nAttendees: John, Sarah, Mike, Lisa\n\nAgenda:\n1. Product launch timeline\n2. Marketing strategy\n3. Budget allocation\n\nAction items:\n- Finalize presentation slides\n- Review quarterly projections\n- Schedule follow-up meeting"),
    ]
    
    for filename, content in files:
        file_path = test_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Created: {file_path}")
    
    return test_dir

def test_semantic_filesystem():
    """Test the semantic filesystem functionality"""
    print("🧠 Testing Semantic Filesystem\n")
    
    # Create test files
    print("📁 Creating test files...")
    test_dir = create_test_files()
    print()
    
    # Initialize semantic filesystem
    print("🚀 Initializing Semantic Filesystem...")
    try:
        semantic_fs = SemanticFilesystem()
        print("✅ Semantic Filesystem initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize: {e}")
        return
    print()
    
    # Index test files
    print("📊 Indexing test files...")
    indexed_files = []
    for file_path in test_dir.glob("*.txt"):
        try:
            # Add context for the video script
            context = None
            if "ptx" in file_path.name.lower():
                context = {
                    "youtube_video": {
                        "title": "PTX Tutorial",
                        "topic": "GPU programming",
                        "date": "2023-04-01"
                    }
                }
            
            result = semantic_fs.index_file(str(file_path), context)
            if result:
                indexed_files.append(file_path.name)
                print(f"✅ Indexed: {file_path.name}")
            else:
                print(f"❌ Failed to index: {file_path.name}")
        except Exception as e:
            print(f"❌ Error indexing {file_path.name}: {e}")
    
    # Also index the Python file
    py_file = test_dir / "python_data_processor.py"
    if py_file.exists():
        try:
            result = semantic_fs.index_file(str(py_file))
            if result:
                indexed_files.append(py_file.name)
                print(f"✅ Indexed: {py_file.name}")
        except Exception as e:
            print(f"❌ Error indexing {py_file.name}: {e}")
    
    print(f"\n📈 Successfully indexed {len(indexed_files)} files")
    print()
    
    # Test queries
    test_queries = [
        "python script that processes customer data",
        "budget report from Q3",
        "video script about PTX from 6 months ago",
        "meeting notes about product launch",
        "research paper about AI and machine learning",
        "files related to YouTube video",
    ]
    
    print("🔍 Testing semantic queries...\n")
    
    for query_text in test_queries:
        print(f"Query: '{query_text}'")
        try:
            query = SemanticQuery(query=query_text, limit=3)
            result = semantic_fs.query(query)
            
            print(f"  📊 Found {result.total_found} results in {result.execution_time:.2f}s")
            if result.interpretation:
                print(f"  💭 Interpreted as: {result.interpretation}")
            
            for i, search_result in enumerate(result.results, 1):
                file_info = search_result.file
                print(f"    {i}. {file_info.filename} (score: {search_result.relevance_score:.1%})")
                print(f"       Reason: {search_result.match_reason}")
                if file_info.auto_tags:
                    print(f"       Tags: {', '.join(file_info.auto_tags)}")
            
            print()
            
        except Exception as e:
            print(f"  ❌ Query failed: {e}")
            print()
    
    print("🎉 Semantic Filesystem test completed!")
    print("\n💡 Try running the web interface:")
    print("   python main.py serve")
    print("   Then open http://localhost:8000")

if __name__ == "__main__":
    test_semantic_filesystem()
