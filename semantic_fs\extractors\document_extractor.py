"""
Document Content Extractor
Specialized extractor for office documents (PDF, Word, Excel, PowerPoint)
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Set
from .base_extractor import BaseExtractor

# PDF processing
try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

# Document processing
try:
    from docx import Document
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

# Excel processing
try:
    import openpyxl
    HAS_EXCEL = True
except ImportError:
    HAS_EXCEL = False

logger = logging.getLogger(__name__)


class DocumentExtractor(BaseExtractor):
    """Specialized extractor for office documents"""
    
    def get_supported_extensions(self) -> Set[str]:
        """Get set of supported document extensions"""
        return {
            # PDF documents
            '.pdf',
            
            # Microsoft Office
            '.docx', '.doc', '.rtf',
            '.xlsx', '.xls', '.csv',
            '.pptx', '.ppt',
            
            # OpenOffice/LibreOffice
            '.odt', '.ods', '.odp',
            
            # Other document formats
            '.pages', '.numbers', '.key'
        }
    
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get mapping of document extensions to MIME types"""
        return {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.rtf': 'application/rtf',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.csv': 'text/csv',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.odt': 'application/vnd.oasis.opendocument.text',
            '.ods': 'application/vnd.oasis.opendocument.spreadsheet',
            '.odp': 'application/vnd.oasis.opendocument.presentation',
            '.pages': 'application/x-iwork-pages-sffpages',
            '.numbers': 'application/x-iwork-numbers-sffnumbers',
            '.key': 'application/x-iwork-keynote-sffkey'
        }
    
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract content from document files"""
        try:
            extension = file_path.suffix.lower()
            if extension not in self.get_supported_extensions():
                return None
            
            content_parts = []
            
            # 1. Extract basic file info
            file_info = self._get_file_info(file_path)
            content_parts.append(f"Document: {file_info['name']} ({file_info['size_mb']} MB)")
            
            # 2. Extract document content based on type
            if extension == '.pdf':
                document_content = self._extract_pdf_content(file_path)
            elif extension == '.docx':
                document_content = self._extract_docx_content(file_path)
            elif extension in ['.xlsx', '.xls']:
                document_content = self._extract_excel_content(file_path)
            elif extension == '.csv':
                document_content = self._extract_csv_content(file_path)
            else:
                document_content = f"Document type: {extension.upper()}"
            
            if document_content:
                content_parts.append(f"Content: {document_content}")
            
            # 3. Extract filename context
            filename_context = self._analyze_filename_context(file_path)
            if filename_context:
                content_parts.append(f"Context: {filename_context}")
            
            return "\n\n".join(content_parts) if content_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting document content from {file_path}: {e}")
            return f"Document file: {file_path.name} (extraction error)"
    
    def _extract_pdf_content(self, file_path: Path) -> Optional[str]:
        """Extract content from PDF files"""
        if not HAS_PDF:
            return "PDF file (PyPDF2 not available for text extraction)"
        
        try:
            text_content = []
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                page_count = len(pdf_reader.pages)
                
                # Extract text from first few pages
                max_pages = min(5, page_count)
                for i in range(max_pages):
                    page = pdf_reader.pages[i]
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
                
                result = '\n'.join(text_content)
                if page_count > max_pages:
                    result += f"\n... [PDF has {page_count} total pages, showing first {max_pages}]"
                
                return self._clean_content(result) if result else None
                
        except Exception as e:
            logger.debug(f"Error reading PDF file {file_path}: {e}")
            return f"PDF file ({e})"
    
    def _extract_docx_content(self, file_path: Path) -> Optional[str]:
        """Extract content from DOCX files"""
        if not HAS_DOCX:
            return "Word document (python-docx not available for text extraction)"
        
        try:
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            result = '\n'.join(text_content)
            return self._clean_content(result) if result else None
            
        except Exception as e:
            logger.debug(f"Error reading DOCX file {file_path}: {e}")
            return f"Word document ({e})"
    
    def _extract_excel_content(self, file_path: Path) -> Optional[str]:
        """Extract content from Excel files"""
        if not HAS_EXCEL:
            return "Excel spreadsheet (openpyxl not available for text extraction)"
        
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text_content = []
            
            for sheet_name in workbook.sheetnames[:3]:  # First 3 sheets
                sheet = workbook[sheet_name]
                text_content.append(f"Sheet: {sheet_name}")
                
                row_count = 0
                for row in sheet.iter_rows(values_only=True):
                    if row_count >= 20:  # Limit to first 20 rows per sheet
                        text_content.append("... [more rows]")
                        break
                    
                    row_text = ' | '.join([str(cell) if cell is not None else '' for cell in row])
                    if row_text.strip():
                        text_content.append(row_text)
                        row_count += 1
                
                text_content.append("")  # Empty line between sheets
            
            result = '\n'.join(text_content)
            return self._clean_content(result) if result else None
            
        except Exception as e:
            logger.debug(f"Error reading Excel file {file_path}: {e}")
            return f"Excel spreadsheet ({e})"
    
    def _extract_csv_content(self, file_path: Path) -> Optional[str]:
        """Extract content from CSV files"""
        try:
            import csv
            content_lines = []
            
            with open(file_path, 'r', encoding='utf-8', newline='', errors='ignore') as f:
                reader = csv.reader(f)
                row_count = 0
                
                for row in reader:
                    if row_count >= 50:  # Limit to first 50 rows
                        content_lines.append("... [more rows]")
                        break
                    
                    row_text = ' | '.join(row)
                    if row_text.strip():
                        content_lines.append(row_text)
                        row_count += 1
            
            result = '\n'.join(content_lines)
            return self._clean_content(result) if result else None
            
        except Exception as e:
            logger.debug(f"Error reading CSV file {file_path}: {e}")
            return f"CSV file ({e})"
