"""
Detailed Unsupported File Analyzer
Shows exactly what file types are still unsupported and why
"""

import os
import sys
import time
import threading
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine


class DetailedUnsupportedAnalyzer:
    """Analyzer that shows detailed breakdown of unsupported files"""
    
    def __init__(self):
        self.extension_counts = Counter()
        self.unsupported_files = defaultdict(list)  # extension -> list of file paths
        self.total_files = 0
        self.total_size = 0
        self.scanning = False
        self.start_time = None
        self.lock = threading.Lock()
        
        # Get supported extensions from our modular system
        try:
            from semantic_fs.content_extractor import ContentExtractor
            extractor = ContentExtractor()
            self.supported_extensions = extractor.get_supported_extensions()
            print(f"✅ Loaded {len(self.supported_extensions)} supported extensions from modular system")
        except Exception as e:
            print(f"❌ Error loading modular system: {e}")
            # Fallback to manual list
            self.supported_extensions = set()
        
        # Excluded directories
        self.excluded_dirs = {
            'node_modules', '.git', '.svn', '__pycache__', '.pytest_cache',
            'venv', 'env', '.venv', 'build', 'dist', '.tox',
            'Windows', 'Program Files', 'Program Files (x86)',
            '$Recycle.Bin', 'System Volume Information',
            '.Trash', '.Trashes'
        }
        
        # Common extensionless files that should be treated as text
        self.extensionless_text_files = {
            'readme', 'license', 'changelog', 'authors', 'contributors',
            'copying', 'install', 'news', 'todo', 'fixme', 'version',
            'release', 'history', 'makefile', 'dockerfile', 'gemfile',
            'rakefile', 'procfile', 'vagrantfile', 'gruntfile', 'gulpfile'
        }
    
    def _should_skip_directory(self, dir_path: Path) -> bool:
        """Check if directory should be skipped"""
        dir_name = dir_path.name.lower()
        return dir_name in self.excluded_dirs
    
    def _is_extension_supported(self, extension: str, filename: str = "") -> bool:
        """Check if extension is supported, including extensionless files"""
        if extension:
            return extension.lower() in self.supported_extensions
        else:
            # For files with no extension, check if filename matches known patterns
            filename_lower = filename.lower()
            return filename_lower in self.extensionless_text_files
    
    def scan_directory(self, root_path: Path):
        """Scan directory and collect detailed unsupported file info"""
        print(f"🔍 Starting detailed analysis of: {root_path}")
        
        self.scanning = True
        self.start_time = time.time()
        
        try:
            for dirpath, dirnames, filenames in os.walk(root_path):
                current_dir = Path(dirpath)
                
                # Skip excluded directories
                dirnames[:] = [d for d in dirnames 
                             if not self._should_skip_directory(current_dir / d)]
                
                # Skip if current directory should be excluded
                if self._should_skip_directory(current_dir):
                    continue
                
                for filename in filenames:
                    file_path = current_dir / filename
                    
                    try:
                        if file_path.is_file():
                            # Get file extension
                            extension = file_path.suffix.lower()
                            
                            # For extensionless files, check if it's a known text file
                            if not extension:
                                filename_lower = file_path.name.lower()
                                if filename_lower in self.extensionless_text_files:
                                    extension = f"({filename_lower})"  # Mark as special
                            
                            # Get file size
                            try:
                                file_size = file_path.stat().st_size
                            except (OSError, PermissionError):
                                file_size = 0
                            
                            # Update counts
                            with self.lock:
                                self.extension_counts[extension] += 1
                                self.total_files += 1
                                self.total_size += file_size
                                
                                # Track unsupported files with details
                                if not self._is_extension_supported(extension, file_path.name):
                                    self.unsupported_files[extension].append({
                                        'path': str(file_path),
                                        'size': file_size,
                                        'name': file_path.name
                                    })
                    
                    except (OSError, PermissionError):
                        # Skip files we can't access
                        continue
        
        except KeyboardInterrupt:
            print("\n🛑 Scan interrupted by user")
        except Exception as e:
            print(f"\n💥 Error during scan: {e}")
        finally:
            self.scanning = False
            self._print_detailed_analysis()
    
    def _print_detailed_analysis(self):
        """Print detailed analysis of unsupported files"""
        print("\n" + "=" * 80)
        print("🔍 DETAILED UNSUPPORTED FILE ANALYSIS")
        print("=" * 80)
        
        total_supported = sum(count for ext, count in self.extension_counts.items() 
                            if self._is_extension_supported(ext, ""))
        total_unsupported = self.total_files - total_supported
        
        print(f"📊 OVERALL STATISTICS:")
        print(f"   Total files scanned: {self.total_files:,}")
        print(f"   ✅ Supported files: {total_supported:,} ({total_supported/max(self.total_files,1)*100:.1f}%)")
        print(f"   ❌ Unsupported files: {total_unsupported:,} ({total_unsupported/max(self.total_files,1)*100:.1f}%)")
        
        if self.unsupported_files:
            print(f"\n🎯 DETAILED BREAKDOWN OF {total_unsupported:,} UNSUPPORTED FILES:")
            print("-" * 80)
            
            # Sort by count (descending)
            sorted_unsupported = sorted(self.unsupported_files.items(), 
                                      key=lambda x: len(x[1]), reverse=True)
            
            for i, (extension, file_list) in enumerate(sorted_unsupported):
                if not extension:
                    ext_display = "(no extension)"
                else:
                    ext_display = extension
                
                count = len(file_list)
                total_size = sum(f['size'] for f in file_list)
                avg_size = total_size / count if count > 0 else 0
                
                print(f"\n{i+1:2d}. ❌ {ext_display}")
                print(f"     Count: {count:,} files")
                print(f"     Total size: {total_size / (1024*1024):.2f} MB")
                print(f"     Average size: {avg_size / 1024:.1f} KB")
                
                # Show sample files
                sample_files = file_list[:5]  # First 5 files
                print(f"     Sample files:")
                for j, file_info in enumerate(sample_files):
                    file_path = Path(file_info['path'])
                    size_kb = file_info['size'] / 1024
                    print(f"       {j+1}. {file_path.name} ({size_kb:.1f} KB)")
                    print(f"          Path: {file_path.parent}")
                
                if len(file_list) > 5:
                    print(f"       ... and {len(file_list) - 5} more files")
                
                # Analyze what this extension might be
                analysis = self._analyze_extension_type(extension, file_list)
                if analysis:
                    print(f"     💡 Analysis: {analysis}")
        
        print(f"\n🎯 RECOMMENDATIONS:")
        if total_unsupported == 0:
            print("   🎉 PERFECT! 100% file support achieved!")
        elif total_unsupported < 50:
            print("   🎉 EXCELLENT! Less than 50 unsupported files")
            print("   💡 Consider adding support for the remaining extensions above")
        elif total_unsupported < 200:
            print("   👍 VERY GOOD! Less than 200 unsupported files")
            print("   💡 Focus on the top 5 unsupported extensions for maximum impact")
        else:
            print("   ⚠️  NEEDS IMPROVEMENT! Many unsupported files")
            print("   💡 Prioritize the most common unsupported extensions")
        
        print(f"\n📈 POTENTIAL IMPROVEMENT:")
        print(f"   Adding support for all unsupported extensions would increase")
        print(f"   coverage from {total_supported/max(self.total_files,1)*100:.1f}% to 100.0%")
        print(f"   That's an additional {total_unsupported:,} files!")
    
    def _analyze_extension_type(self, extension: str, file_list: list) -> str:
        """Analyze what type of files these might be"""
        if not extension or extension.startswith('('):
            return "Extensionless files - likely configuration or system files"
        
        # Analyze file names for patterns
        names = [f['name'].lower() for f in file_list[:10]]  # Sample first 10 names
        
        # Common patterns
        if any('temp' in name or 'tmp' in name for name in names):
            return "Likely temporary files"
        elif any('cache' in name for name in names):
            return "Likely cache files"
        elif any('backup' in name or 'bak' in name for name in names):
            return "Likely backup files"
        elif any('log' in name for name in names):
            return "Likely log files"
        elif extension.startswith('.'):
            # Try to guess by extension
            ext_lower = extension[1:].lower()  # Remove the dot
            if ext_lower in ['lock', 'pid', 'tmp', 'temp']:
                return "System/process files"
            elif ext_lower in ['bak', 'backup', 'old', 'orig']:
                return "Backup files"
            elif ext_lower in ['cache', 'idx', 'index']:
                return "Index/cache files"
            elif len(ext_lower) <= 3 and ext_lower.isalpha():
                return "Possible proprietary format"
            elif ext_lower.isdigit():
                return "Numbered files (possibly man pages or backups)"
        
        return "Unknown file type - needs investigation"


def main():
    """Main function"""
    print("🔍 DETAILED UNSUPPORTED FILE ANALYZER")
    print("Shows exactly what files are still unsupported")
    print("=" * 80)
    
    # Find target directory
    print("🔍 SELECT DIRECTORY TO ANALYZE:")
    print("1. Samsung USB Drive (E:\\)")
    print("2. Current directory")
    print("3. Custom path")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        target_dir = Path("E:\\")
        if not target_dir.exists():
            print("❌ USB drive not found at E:\\")
            return
    elif choice == "2":
        target_dir = Path.cwd()
    elif choice == "3":
        custom_path = input("Enter path: ").strip()
        target_dir = Path(custom_path)
        if not target_dir.exists():
            print(f"❌ Path not found: {custom_path}")
            return
    else:
        print("❌ Invalid choice")
        return
    
    # Create analyzer and start
    analyzer = DetailedUnsupportedAnalyzer()
    
    try:
        analyzer.scan_directory(target_dir)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
