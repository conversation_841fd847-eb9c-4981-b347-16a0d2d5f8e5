<PERSON>
Software Engineer

Contact Information:
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johnsmith

Professional Summary:
Experienced software engineer with 5+ years developing web applications using Python, JavaScript, and React. Passionate about creating scalable solutions and working with cross-functional teams.

Experience:
Senior Software Engineer | TechCorp Inc. | 2022-Present
- Led development of microservices architecture serving 1M+ users
- Implemented CI/CD pipelines reducing deployment time by 60%
- Mentored junior developers and conducted code reviews

Software Engineer | StartupXYZ | 2020-2022
- Built full-stack web applications using Django and React
- Collaborated with product team to define technical requirements
- Optimized database queries improving performance by 40%

Education:
Bachelor of Science in Computer Science
University of Technology | 2016-2020

Skills:
- Programming: Python, JavaScript, TypeScript, Java
- Frameworks: Django, React, Node.js, Express
- Databases: PostgreSQL, MongoDB, Redis
- Tools: Docker, Kubernetes, AWS, Git

Last updated: April 2024
