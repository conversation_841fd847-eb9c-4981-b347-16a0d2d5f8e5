"""
Quick test to verify search functionality is fixed
"""

import sys
import tempfile
from pathlib import Path

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine


def test_search_fix():
    """Test that search functionality is now working"""
    print("🔍 Testing Search Fix...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_files"
        test_dir.mkdir()
        
        # Create test files
        (test_dir / "document1.txt").write_text("This is a test document about machine learning")
        (test_dir / "document2.txt").write_text("Python programming tutorial for beginners")
        (test_dir / "document3.md").write_text("# Project README\nThis project is about data analysis")
        
        # Initialize engine
        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir()
        
        engine = DesktopSemanticEngine(config_dir)
        
        # Index the files
        print("Indexing test files...")
        indexing_started = engine.start_full_indexing([test_dir])
        
        if indexing_started:
            # Wait for indexing to complete
            import time
            timeout = 30
            start_time = time.time()
            
            while engine.is_indexing and (time.time() - start_time) < timeout:
                time.sleep(0.5)
            
            # Check if files were indexed
            stats = engine.get_indexing_stats()
            print(f"Indexed {stats['total_indexed_files']} files")
            
            if stats['total_indexed_files'] > 0:
                # Test search functionality
                print("Testing search...")
                try:
                    results = engine.search_files("machine learning", limit=5)
                    print(f"✅ Search successful! Found {len(results)} results")
                    
                    if results:
                        print("First result:")
                        result = results[0]
                        print(f"  Filename: {result.get('filename', 'N/A')}")
                        print(f"  Path: {result.get('path', 'N/A')}")
                        print(f"  Relevance: {result.get('relevance_score', 'N/A')}")
                    
                    return True
                    
                except Exception as e:
                    print(f"❌ Search failed: {e}")
                    return False
            else:
                print("⚠️ No files were indexed")
                return False
        else:
            print("❌ Failed to start indexing")
            return False


if __name__ == "__main__":
    success = test_search_fix()
    if success:
        print("🎉 SEARCH FIX VERIFIED!")
    else:
        print("❌ Search still has issues")
