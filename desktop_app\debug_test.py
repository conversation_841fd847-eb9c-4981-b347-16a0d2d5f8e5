"""
Debug test for desktop engine
"""

import os
import sys
import tempfile
from pathlib import Path

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine


def debug_file_discovery():
    """Debug file discovery issues"""
    print("🔍 Debugging File Discovery...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_files"
        test_dir.mkdir()
        
        # Create simple test files
        print(f"Creating test files in: {test_dir}")
        
        # Create a few simple files
        (test_dir / "test1.txt").write_text("Test file 1")
        (test_dir / "test2.py").write_text("print('hello')")
        (test_dir / "test3.md").write_text("# Test markdown")
        
        print(f"Created files:")
        for file in test_dir.iterdir():
            print(f"  - {file.name} ({file.stat().st_size} bytes)")
        
        # Test basic os.walk
        print(f"\nTesting os.walk on {test_dir}:")
        for root, dirs, files in os.walk(test_dir):
            print(f"  Root: {root}")
            print(f"  Dirs: {dirs}")
            print(f"  Files: {files}")
        
        # Test Path.iterdir()
        print(f"\nTesting Path.iterdir():")
        for item in test_dir.iterdir():
            print(f"  - {item} (is_file: {item.is_file()})")
        
        # Test engine file discovery
        print(f"\nTesting engine file discovery:")
        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir()
        
        engine = DesktopSemanticEngine(config_dir)
        
        # Test _walk_files method
        print(f"Testing _walk_files with path: {test_dir}")
        print(f"Is system directory: {engine._is_system_directory(test_dir)}")

        files_found = list(engine._walk_files(test_dir))
        print(f"Engine found {len(files_found)} files:")
        for file in files_found:
            print(f"  - {file}")
        
        # Test _should_index_file
        print(f"\nTesting file filtering:")
        for file in files_found:
            should_index = engine._should_index_file(file)
            print(f"  - {file.name}: {should_index}")


if __name__ == "__main__":
    debug_file_discovery()
