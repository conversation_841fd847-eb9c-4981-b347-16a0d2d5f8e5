{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "02b014457fe70612098c541b8a6074b5", "files": {"z_48e1513074be697d___init___py": {"hash": "2677140dd30804ae1011bcd11ab7c871", "index": {"url": "z_48e1513074be697d___init___py.html", "file": "semantic_fs\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_api_py": {"hash": "3025d4c373af6e3454c0669b51b51fd7", "index": {"url": "z_48e1513074be697d_api_py.html", "file": "semantic_fs\\api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 184, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_cache_py": {"hash": "c30c0840d8d33f5daf86f2d07cf860c6", "index": {"url": "z_48e1513074be697d_cache_py.html", "file": "semantic_fs\\cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_cli_py": {"hash": "d585314d6d2a91cea447c78d768d673d", "index": {"url": "z_48e1513074be697d_cli_py.html", "file": "semantic_fs\\cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 566, "n_excluded": 0, "n_missing": 566, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_config_py": {"hash": "c1f9cb2ccd6555159a649de056428128", "index": {"url": "z_48e1513074be697d_config_py.html", "file": "semantic_fs\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 110, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_content_extractor_py": {"hash": "625796f2c3d1d07427e374915c0161ab", "index": {"url": "z_48e1513074be697d_content_extractor_py.html", "file": "semantic_fs\\content_extractor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 193, "n_excluded": 0, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_core_py": {"hash": "48a72c5acc62088ce13e7986f43354ce", "index": {"url": "z_48e1513074be697d_core_py.html", "file": "semantic_fs\\core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_database_py": {"hash": "94cbb86507315c0bc57eb1242f8550c7", "index": {"url": "z_48e1513074be697d_database_py.html", "file": "semantic_fs\\database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_embedding_engine_py": {"hash": "77447e942ee93559fb17db3865673bdf", "index": {"url": "z_48e1513074be697d_embedding_engine_py.html", "file": "semantic_fs\\embedding_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_exceptions_py": {"hash": "cce200f20a4ab3de37801576822e5e49", "index": {"url": "z_48e1513074be697d_exceptions_py.html", "file": "semantic_fs\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 78, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_file_monitor_py": {"hash": "aa12c94ac652fa4f1f54c3f661da0f5d", "index": {"url": "z_48e1513074be697d_file_monitor_py.html", "file": "semantic_fs\\file_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 224, "n_excluded": 0, "n_missing": 181, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_llm_processor_py": {"hash": "6cc28290c77bfa7fa3268ca8db12d845", "index": {"url": "z_48e1513074be697d_llm_processor_py.html", "file": "semantic_fs\\llm_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 205, "n_excluded": 0, "n_missing": 181, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_logging_config_py": {"hash": "2cae36f3f91d15bde6e79e1e702227a0", "index": {"url": "z_48e1513074be697d_logging_config_py.html", "file": "semantic_fs\\logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_migrations_py": {"hash": "aedac6d9d98fdf4bee65612d1ad61ea1", "index": {"url": "z_48e1513074be697d_migrations_py.html", "file": "semantic_fs\\migrations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_models_py": {"hash": "7274fc778523f82c3b21ad0186e13c70", "index": {"url": "z_48e1513074be697d_models_py.html", "file": "semantic_fs\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_security_py": {"hash": "630204493be594fdb083656674e54ae2", "index": {"url": "z_48e1513074be697d_security_py.html", "file": "semantic_fs\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_48e1513074be697d_signal_handler_py": {"hash": "57d6abfe9bed0e34dc6905d8b4d92a4d", "index": {"url": "z_48e1513074be697d_signal_handler_py.html", "file": "semantic_fs\\signal_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 79, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}