"""
Advanced configuration management with validation, hot-reload, and environment-specific configs
"""

import os
import json
import yaml
import time
import logging
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from pydantic import ValidationError

from .config import Settings

logger = logging.getLogger(__name__)


@dataclass
class ConfigChange:
    """Represents a configuration change"""
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime
    source: str  # 'file', 'env', 'api', etc.


@dataclass
class ConfigValidationResult:
    """Result of configuration validation"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    changes: List[ConfigChange] = field(default_factory=list)


class ConfigFileWatcher(FileSystemEventHandler):
    """Watches configuration files for changes"""
    
    def __init__(self, config_manager: 'ConfigManager'):
        super().__init__()
        self.config_manager = config_manager
        self.debounce_time = 1.0  # 1 second debounce
        self.last_modified = {}
    
    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix not in {'.env', '.yaml', '.yml', '.json'}:
            return
        
        # Debounce rapid file changes
        now = time.time()
        if file_path in self.last_modified:
            if now - self.last_modified[file_path] < self.debounce_time:
                return
        
        self.last_modified[file_path] = now
        
        logger.info(f"Configuration file changed: {file_path}")
        self.config_manager._handle_file_change(file_path)


class ConfigManager:
    """Advanced configuration manager with validation and hot-reload"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize configuration manager
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config_dir = config_dir or Path(".")
        self.settings: Optional[Settings] = None
        self.change_handlers: List[Callable[[ConfigChange], None]] = []
        self.validation_handlers: List[Callable[[Settings], ConfigValidationResult]] = []
        
        # File watching
        self.observer: Optional[Observer] = None
        self.watcher: Optional[ConfigFileWatcher] = None
        self.hot_reload_enabled = True
        
        # Environment-specific configs
        self.environment = os.getenv("ENVIRONMENT", "development")
        self.config_files = self._discover_config_files()
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Load initial configuration
        self.reload_config()
    
    def _discover_config_files(self) -> List[Path]:
        """Discover configuration files in order of precedence"""
        files = []
        
        # Base configuration files
        base_files = [
            self.config_dir / ".env",
            self.config_dir / "config.yaml",
            self.config_dir / "config.yml",
            self.config_dir / "config.json"
        ]
        
        # Environment-specific files
        env_files = [
            self.config_dir / f".env.{self.environment}",
            self.config_dir / f"config.{self.environment}.yaml",
            self.config_dir / f"config.{self.environment}.yml",
            self.config_dir / f"config.{self.environment}.json"
        ]
        
        # Add existing files in order of precedence
        for file_path in base_files + env_files:
            if file_path.exists():
                files.append(file_path)
        
        return files
    
    def reload_config(self) -> ConfigValidationResult:
        """Reload configuration from all sources"""
        with self.lock:
            logger.info("Reloading configuration...")
            
            try:
                # Store old settings for comparison
                old_settings = self.settings
                
                # Load new settings
                new_settings = self._load_settings()
                
                # Validate new settings
                validation_result = self._validate_settings(new_settings)
                
                if validation_result.is_valid:
                    # Detect changes
                    if old_settings:
                        changes = self._detect_changes(old_settings, new_settings)
                        validation_result.changes = changes
                    
                    # Apply new settings
                    self.settings = new_settings
                    
                    # Notify change handlers
                    for change in validation_result.changes:
                        self._notify_change_handlers(change)
                    
                    logger.info("Configuration reloaded successfully")
                else:
                    logger.error(f"Configuration validation failed: {validation_result.errors}")
                
                return validation_result
                
            except Exception as e:
                logger.error(f"Error reloading configuration: {e}")
                return ConfigValidationResult(
                    is_valid=False,
                    errors=[f"Failed to reload configuration: {str(e)}"]
                )
    
    def _load_settings(self) -> Settings:
        """Load settings from all configuration sources"""
        # Start with environment variables
        env_vars = dict(os.environ)
        
        # Load from configuration files
        config_data = {}
        for config_file in self.config_files:
            try:
                file_data = self._load_config_file(config_file)
                config_data.update(file_data)
                logger.debug(f"Loaded configuration from {config_file}")
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
        
        # Filter environment variables to only include known settings
        filtered_env_vars = {}
        settings_fields = Settings.__fields__.keys()

        for key, value in env_vars.items():
            # Convert environment variable names to lowercase for matching
            key_lower = key.lower()
            if key_lower in settings_fields:
                filtered_env_vars[key_lower] = value

        # Merge filtered environment variables (they take precedence)
        config_data.update(filtered_env_vars)

        # Create settings instance
        return Settings(**config_data)
    
    def _load_config_file(self, file_path: Path) -> Dict[str, Any]:
        """Load configuration from a file"""
        if not file_path.exists():
            return {}
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            if file_path.suffix == '.json':
                return json.loads(content)
            elif file_path.suffix in {'.yaml', '.yml'}:
                return yaml.safe_load(content) or {}
            elif file_path.name.startswith('.env'):
                return self._parse_env_file(content)
            else:
                logger.warning(f"Unknown config file format: {file_path}")
                return {}
                
        except Exception as e:
            logger.error(f"Error loading config file {file_path}: {e}")
            return {}
    
    def _parse_env_file(self, content: str) -> Dict[str, str]:
        """Parse .env file content"""
        env_vars = {}
        
        for line in content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                env_vars[key] = value
        
        return env_vars
    
    def _validate_settings(self, settings: Settings) -> ConfigValidationResult:
        """Validate settings using registered validators"""
        result = ConfigValidationResult(is_valid=True)
        
        # Run built-in validation
        try:
            # Pydantic validation is already done during Settings creation
            pass
        except ValidationError as e:
            result.is_valid = False
            result.errors.extend([str(error) for error in e.errors()])
        
        # Run custom validators
        for validator in self.validation_handlers:
            try:
                validator_result = validator(settings)
                if not validator_result.is_valid:
                    result.is_valid = False
                    result.errors.extend(validator_result.errors)
                result.warnings.extend(validator_result.warnings)
            except Exception as e:
                result.is_valid = False
                result.errors.append(f"Validator error: {str(e)}")
        
        return result
    
    def _detect_changes(self, old_settings: Settings, new_settings: Settings) -> List[ConfigChange]:
        """Detect changes between old and new settings"""
        changes = []
        
        # Compare all fields
        for field_name in old_settings.__fields__:
            old_value = getattr(old_settings, field_name)
            new_value = getattr(new_settings, field_name)
            
            if old_value != new_value:
                changes.append(ConfigChange(
                    key=field_name,
                    old_value=old_value,
                    new_value=new_value,
                    timestamp=datetime.utcnow(),
                    source="file"
                ))
        
        return changes
    
    def _notify_change_handlers(self, change: ConfigChange):
        """Notify registered change handlers"""
        for handler in self.change_handlers:
            try:
                handler(change)
            except Exception as e:
                logger.error(f"Error in change handler: {e}")
    
    def _handle_file_change(self, file_path: Path):
        """Handle configuration file change"""
        if not self.hot_reload_enabled:
            return
        
        logger.info(f"Configuration file changed: {file_path}")
        
        # Reload configuration
        result = self.reload_config()
        
        if result.is_valid:
            logger.info(f"Configuration hot-reloaded successfully ({len(result.changes)} changes)")
        else:
            logger.error(f"Configuration hot-reload failed: {result.errors}")
    
    def start_watching(self):
        """Start watching configuration files for changes"""
        if not self.hot_reload_enabled or self.observer:
            return
        
        try:
            self.observer = Observer()
            self.watcher = ConfigFileWatcher(self)
            
            # Watch the configuration directory
            self.observer.schedule(self.watcher, str(self.config_dir), recursive=False)
            self.observer.start()
            
            logger.info("Configuration file watching started")
            
        except Exception as e:
            logger.error(f"Failed to start configuration watching: {e}")
    
    def stop_watching(self):
        """Stop watching configuration files"""
        if self.observer:
            try:
                self.observer.stop()
                self.observer.join(timeout=5)
                self.observer = None
                self.watcher = None
                logger.info("Configuration file watching stopped")
            except Exception as e:
                logger.error(f"Error stopping configuration watching: {e}")
    
    def add_change_handler(self, handler: Callable[[ConfigChange], None]):
        """Add a configuration change handler"""
        if handler not in self.change_handlers:
            self.change_handlers.append(handler)
    
    def remove_change_handler(self, handler: Callable[[ConfigChange], None]):
        """Remove a configuration change handler"""
        if handler in self.change_handlers:
            self.change_handlers.remove(handler)
    
    def add_validator(self, validator: Callable[[Settings], ConfigValidationResult]):
        """Add a custom configuration validator"""
        if validator not in self.validation_handlers:
            self.validation_handlers.append(validator)
    
    def remove_validator(self, validator: Callable[[Settings], ConfigValidationResult]):
        """Remove a custom configuration validator"""
        if validator in self.validation_handlers:
            self.validation_handlers.remove(validator)
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary"""
        if not self.settings:
            return {}
        
        return {
            "environment": self.environment,
            "config_files": [str(f) for f in self.config_files],
            "hot_reload_enabled": self.hot_reload_enabled,
            "watching": self.observer is not None,
            "settings_count": len(self.settings.__fields__),
            "last_reload": datetime.utcnow().isoformat()
        }
    
    def export_config(self, format: str = "json") -> str:
        """Export current configuration"""
        if not self.settings:
            return ""
        
        config_dict = self.settings.dict()
        
        if format.lower() == "json":
            return json.dumps(config_dict, indent=2, default=str)
        elif format.lower() in {"yaml", "yml"}:
            return yaml.dump(config_dict, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def validate_config_file(self, file_path: Union[str, Path]) -> ConfigValidationResult:
        """Validate a configuration file without loading it"""
        file_path = Path(file_path)
        
        try:
            # Load the file
            config_data = self._load_config_file(file_path)
            
            # Create temporary settings
            temp_settings = Settings(**config_data)
            
            # Validate
            return self._validate_settings(temp_settings)
            
        except Exception as e:
            return ConfigValidationResult(
                is_valid=False,
                errors=[f"Failed to validate config file: {str(e)}"]
            )


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def reload_config() -> ConfigValidationResult:
    """Reload configuration (convenience function)"""
    return get_config_manager().reload_config()


def get_current_settings() -> Optional[Settings]:
    """Get current settings (convenience function)"""
    return get_config_manager().settings


def start_config_watching():
    """Start configuration file watching (convenience function)"""
    get_config_manager().start_watching()


def stop_config_watching():
    """Stop configuration file watching (convenience function)"""
    get_config_manager().stop_watching()


# Built-in configuration validators
def validate_database_config(settings: Settings) -> ConfigValidationResult:
    """Validate database configuration"""
    result = ConfigValidationResult(is_valid=True)

    # Check database URL format
    if not settings.database_url:
        result.is_valid = False
        result.errors.append("Database URL is required")
    elif not any(settings.database_url.startswith(prefix) for prefix in ["sqlite://", "postgresql://", "mysql://"]):
        result.warnings.append("Database URL format may not be supported")

    # Check vector database path
    vector_path = Path(settings.vector_db_path)
    if not vector_path.parent.exists():
        result.warnings.append(f"Vector database parent directory does not exist: {vector_path.parent}")

    return result


def validate_llm_config(settings: Settings) -> ConfigValidationResult:
    """Validate LLM configuration"""
    result = ConfigValidationResult(is_valid=True)

    # Check if at least one LLM option is configured
    has_openai = bool(settings.openai_api_key)
    has_ollama = settings.use_ollama and settings.ollama_base_url

    if not has_openai and not has_ollama:
        result.warnings.append("No LLM configuration found - some features may be limited")

    # Validate OpenAI config
    if has_openai:
        if len(settings.openai_api_key) < 20:
            result.warnings.append("OpenAI API key appears to be invalid (too short)")

    # Validate Ollama config
    if has_ollama:
        if not settings.ollama_base_url.startswith(("http://", "https://")):
            result.is_valid = False
            result.errors.append("Ollama base URL must start with http:// or https://")

    return result


def validate_security_config(settings: Settings) -> ConfigValidationResult:
    """Validate security configuration"""
    result = ConfigValidationResult(is_valid=True)

    # Check authentication settings
    if settings.auth_enabled:
        if not settings.jwt_secret_key:
            result.warnings.append("JWT secret key not set - one will be auto-generated")
        elif len(settings.jwt_secret_key) < 32:
            result.warnings.append("JWT secret key is short - consider using a longer key for production")

        if not settings.api_keys:
            result.warnings.append("No API keys configured - default key will be generated")

    # Check rate limiting
    if settings.rate_limit_enabled:
        if settings.rate_limit_requests < 1:
            result.is_valid = False
            result.errors.append("Rate limit requests must be at least 1")

        if settings.rate_limit_window < 1:
            result.is_valid = False
            result.errors.append("Rate limit window must be at least 1 second")

    # Check CORS origins
    if "*" in settings.cors_origins and len(settings.cors_origins) > 1:
        result.warnings.append("CORS origins contains '*' with other origins - '*' will override others")

    return result


def validate_performance_config(settings: Settings) -> ConfigValidationResult:
    """Validate performance-related configuration"""
    result = ConfigValidationResult(is_valid=True)

    # Check file size limits
    if settings.max_file_size > 1024 * 1024 * 1024:  # 1GB
        result.warnings.append("Max file size is very large - this may impact performance")

    if settings.max_file_size < 1024:  # 1KB
        result.warnings.append("Max file size is very small - many files may be rejected")

    # Check log settings
    if settings.max_log_file_size > 100 * 1024 * 1024:  # 100MB
        result.warnings.append("Max log file size is very large - consider smaller size for better performance")

    if settings.log_backup_count > 20:
        result.warnings.append("High log backup count may consume significant disk space")

    return result


# Register built-in validators
def register_builtin_validators():
    """Register built-in configuration validators"""
    manager = get_config_manager()
    manager.add_validator(validate_database_config)
    manager.add_validator(validate_llm_config)
    manager.add_validator(validate_security_config)
    manager.add_validator(validate_performance_config)
