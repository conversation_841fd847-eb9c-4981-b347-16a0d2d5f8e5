"""
Database migration management for the semantic filesystem
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional, List, Dict, Any
from contextlib import contextmanager

from alembic import command
from alembic.config import Config
from alembic.runtime.migration import MigrationContext
from alembic.script import ScriptDirectory
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.exc import SQLAlchemyError

from .config import settings
from .database import engine
from .models import Base

logger = logging.getLogger(__name__)


class MigrationManager:
    """Manages database migrations for the semantic filesystem"""
    
    def __init__(self):
        """Initialize the migration manager"""
        self.project_root = Path(__file__).parent.parent
        self.alembic_cfg_path = self.project_root / "alembic.ini"
        self.migrations_dir = self.project_root / "migrations"
        
        # Ensure migrations directory exists
        self.migrations_dir.mkdir(exist_ok=True)
        (self.migrations_dir / "versions").mkdir(exist_ok=True)
        
        # Create Alembic config
        self.alembic_cfg = Config(str(self.alembic_cfg_path))
        self.alembic_cfg.set_main_option("script_location", str(self.migrations_dir))
        self.alembic_cfg.set_main_option("sqlalchemy.url", settings.database_url)
    
    def get_current_revision(self) -> Optional[str]:
        """Get the current database revision"""
        try:
            with engine.connect() as connection:
                context = MigrationContext.configure(connection)
                return context.get_current_revision()
        except Exception as e:
            logger.warning(f"Could not get current revision: {e}")
            return None
    
    def get_head_revision(self) -> Optional[str]:
        """Get the head revision from migration scripts"""
        try:
            script = ScriptDirectory.from_config(self.alembic_cfg)
            return script.get_current_head()
        except Exception as e:
            logger.warning(f"Could not get head revision: {e}")
            return None
    
    def is_database_up_to_date(self) -> bool:
        """Check if database is up to date with migrations"""
        current = self.get_current_revision()
        head = self.get_head_revision()
        
        if current is None and head is None:
            # No migrations exist yet
            return True
        
        return current == head
    
    def get_pending_migrations(self) -> List[str]:
        """Get list of pending migrations"""
        try:
            script = ScriptDirectory.from_config(self.alembic_cfg)
            with engine.connect() as connection:
                context = MigrationContext.configure(connection)
                current = context.get_current_revision()
                
                if current is None:
                    # All migrations are pending
                    return [rev.revision for rev in script.walk_revisions()]
                
                # Get revisions between current and head
                pending = []
                for rev in script.walk_revisions(current, "head"):
                    if rev.revision != current:
                        pending.append(rev.revision)
                
                return pending
        except Exception as e:
            logger.error(f"Error getting pending migrations: {e}")
            return []
    
    def create_initial_migration(self, message: str = "Initial migration") -> bool:
        """Create the initial migration from current models"""
        try:
            logger.info("Creating initial migration...")
            
            # Check if any migrations already exist
            versions_dir = self.migrations_dir / "versions"
            if any(versions_dir.glob("*.py")):
                logger.warning("Migrations already exist, skipping initial migration")
                return True
            
            # Create the migration
            command.revision(
                self.alembic_cfg,
                message=message,
                autogenerate=True
            )
            
            logger.info("Initial migration created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error creating initial migration: {e}")
            return False
    
    def create_migration(self, message: str, autogenerate: bool = True) -> bool:
        """Create a new migration"""
        try:
            logger.info(f"Creating migration: {message}")
            
            command.revision(
                self.alembic_cfg,
                message=message,
                autogenerate=autogenerate
            )
            
            logger.info("Migration created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error creating migration: {e}")
            return False
    
    def upgrade_database(self, revision: str = "head") -> bool:
        """Upgrade database to specified revision"""
        try:
            logger.info(f"Upgrading database to revision: {revision}")
            
            command.upgrade(self.alembic_cfg, revision)
            
            logger.info("Database upgrade completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error upgrading database: {e}")
            return False
    
    def downgrade_database(self, revision: str) -> bool:
        """Downgrade database to specified revision"""
        try:
            logger.info(f"Downgrading database to revision: {revision}")
            
            command.downgrade(self.alembic_cfg, revision)
            
            logger.info("Database downgrade completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error downgrading database: {e}")
            return False
    
    def get_migration_history(self) -> List[Dict[str, Any]]:
        """Get migration history"""
        try:
            script = ScriptDirectory.from_config(self.alembic_cfg)
            history = []
            
            for rev in script.walk_revisions():
                history.append({
                    "revision": rev.revision,
                    "down_revision": rev.down_revision,
                    "message": rev.doc,
                    "create_date": getattr(rev, 'create_date', None)
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting migration history: {e}")
            return []
    
    def initialize_database(self) -> bool:
        """Initialize database with proper migration tracking"""
        try:
            logger.info("Initializing database...")
            
            # Check if database exists and has tables
            inspector = inspect(engine)
            existing_tables = inspector.get_table_names()
            
            if not existing_tables:
                # Fresh database - create all tables and stamp with head
                logger.info("Creating fresh database...")
                Base.metadata.create_all(bind=engine)
                
                # Create initial migration if it doesn't exist
                if not self.get_head_revision():
                    self.create_initial_migration()
                
                # Stamp database with head revision
                command.stamp(self.alembic_cfg, "head")
                
            else:
                # Existing database - check if it needs migration setup
                current_rev = self.get_current_revision()
                if current_rev is None:
                    logger.info("Setting up migration tracking for existing database...")
                    
                    # Create initial migration if it doesn't exist
                    if not self.get_head_revision():
                        self.create_initial_migration()
                    
                    # Stamp with head revision
                    command.stamp(self.alembic_cfg, "head")
                else:
                    # Database already has migration tracking
                    logger.info("Database migration tracking already initialized")
            
            logger.info("Database initialization completed")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            return False
    
    def auto_upgrade(self) -> bool:
        """Automatically upgrade database if needed"""
        try:
            if not self.is_database_up_to_date():
                logger.info("Database is not up to date, running migrations...")
                return self.upgrade_database()
            else:
                logger.info("Database is up to date")
                return True
                
        except Exception as e:
            logger.error(f"Error in auto upgrade: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get migration status information"""
        return {
            "current_revision": self.get_current_revision(),
            "head_revision": self.get_head_revision(),
            "is_up_to_date": self.is_database_up_to_date(),
            "pending_migrations": self.get_pending_migrations(),
            "database_url": settings.database_url
        }


# Global migration manager instance
_migration_manager: Optional[MigrationManager] = None


def get_migration_manager() -> MigrationManager:
    """Get the global migration manager instance"""
    global _migration_manager
    if _migration_manager is None:
        _migration_manager = MigrationManager()
    return _migration_manager


def initialize_database():
    """Initialize database with migration support"""
    return get_migration_manager().initialize_database()


def auto_upgrade_database():
    """Automatically upgrade database if needed"""
    return get_migration_manager().auto_upgrade()


def create_migration(message: str, autogenerate: bool = True):
    """Create a new migration"""
    return get_migration_manager().create_migration(message, autogenerate)


def upgrade_database(revision: str = "head"):
    """Upgrade database to specified revision"""
    return get_migration_manager().upgrade_database(revision)


def get_migration_status():
    """Get migration status"""
    return get_migration_manager().get_status()
