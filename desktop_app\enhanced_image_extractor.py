"""
Enhanced Image Content Extractor with OCR and Contextual Analysis
Extracts text, metadata, and contextual information from images
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
import logging

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

# Image processing
try:
    from PIL import Image, ExifTags
    from PIL.ExifTags import TAGS
    HAS_PIL = True
except ImportError:
    HAS_PIL = False

# OCR processing
try:
    import pytesseract
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

# Advanced image analysis
try:
    import cv2
    import numpy as np
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False

logger = logging.getLogger(__name__)


class EnhancedImageExtractor:
    """Enhanced image content extractor with OCR and contextual analysis"""
    
    def __init__(self):
        """Initialize the enhanced image extractor"""
        self.supported_formats = {
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', 
            '.webp', '.svg', '.ico', '.psd', '.ai', '.eps'
        }
        
        # OCR configuration for better accuracy
        self.ocr_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?@#$%^&*()_+-=[]{}|;:,.<>?/~`'
        
        # Image analysis thresholds
        self.min_text_confidence = 30  # Minimum OCR confidence
        self.min_text_length = 3       # Minimum text length to consider
        
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract comprehensive content from image file"""
        try:
            if not file_path.exists():
                logger.warning(f"Image file does not exist: {file_path}")
                return None
            
            extension = file_path.suffix.lower()
            if extension not in self.supported_formats:
                logger.warning(f"Unsupported image format: {extension}")
                return None
            
            # Extract all available information
            content_parts = []
            
            # 1. Extract EXIF metadata
            metadata = self._extract_metadata(file_path)
            if metadata:
                content_parts.append(f"Image Metadata: {metadata}")
            
            # 2. Extract OCR text
            ocr_text = self._extract_ocr_text(file_path)
            if ocr_text:
                content_parts.append(f"Text Content: {ocr_text}")
            
            # 3. Extract visual context
            visual_context = self._analyze_visual_context(file_path)
            if visual_context:
                content_parts.append(f"Visual Analysis: {visual_context}")
            
            # 4. Extract filename context
            filename_context = self._extract_filename_context(file_path)
            if filename_context:
                content_parts.append(f"Filename Context: {filename_context}")
            
            if content_parts:
                return "\n\n".join(content_parts)
            else:
                return f"Image file: {file_path.name} (no extractable text content)"
                
        except Exception as e:
            logger.error(f"Error extracting content from image {file_path}: {e}")
            return None
    
    def _extract_metadata(self, file_path: Path) -> Optional[str]:
        """Extract EXIF and other metadata from image"""
        if not HAS_PIL:
            return None
        
        try:
            with Image.open(file_path) as image:
                metadata_parts = []
                
                # Basic image info
                metadata_parts.append(f"Format: {image.format}")
                metadata_parts.append(f"Size: {image.size[0]}x{image.size[1]}")
                metadata_parts.append(f"Mode: {image.mode}")
                
                # EXIF data
                exif_data = image._getexif()
                if exif_data:
                    exif_info = []
                    for tag_id, value in exif_data.items():
                        tag = TAGS.get(tag_id, tag_id)
                        if isinstance(tag, str) and isinstance(value, (str, int, float)):
                            # Filter useful metadata
                            if tag in ['DateTime', 'DateTimeOriginal', 'Make', 'Model', 'Software', 
                                     'Artist', 'Copyright', 'ImageDescription', 'UserComment']:
                                exif_info.append(f"{tag}: {value}")
                    
                    if exif_info:
                        metadata_parts.extend(exif_info)
                
                return " | ".join(metadata_parts) if metadata_parts else None
                
        except Exception as e:
            logger.debug(f"Error extracting metadata from {file_path}: {e}")
            return None
    
    def _extract_ocr_text(self, file_path: Path) -> Optional[str]:
        """Extract text using OCR with preprocessing"""
        if not HAS_OCR or not HAS_PIL:
            return None
        
        try:
            # Open and preprocess image for better OCR
            with Image.open(file_path) as image:
                # Convert to RGB if necessary
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # Enhance image for OCR if OpenCV is available
                if HAS_CV2:
                    image = self._preprocess_for_ocr(image)
                
                # Perform OCR with configuration
                text = pytesseract.image_to_string(image, config=self.ocr_config)
                
                # Clean and validate text
                if text:
                    text = self._clean_ocr_text(text)
                    if len(text) >= self.min_text_length:
                        return text
                
                return None
                
        except Exception as e:
            logger.debug(f"Error performing OCR on {file_path}: {e}")
            return None
    
    def _preprocess_for_ocr(self, pil_image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR accuracy using OpenCV"""
        if not HAS_CV2:
            return pil_image
        
        try:
            # Convert PIL to OpenCV format
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Convert back to PIL
            return Image.fromarray(thresh)
            
        except Exception as e:
            logger.debug(f"Error preprocessing image: {e}")
            return pil_image
    
    def _clean_ocr_text(self, text: str) -> str:
        """Clean and normalize OCR text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # Remove very short "words" that are likely OCR errors
        words = text.split()
        cleaned_words = [word for word in words if len(word) >= 2 or word.isdigit()]
        
        return ' '.join(cleaned_words)
    
    def _analyze_visual_context(self, file_path: Path) -> Optional[str]:
        """Analyze visual context and characteristics"""
        if not HAS_PIL:
            return None
        
        try:
            with Image.open(file_path) as image:
                context_parts = []
                
                # Analyze image characteristics
                width, height = image.size
                aspect_ratio = width / height
                
                # Determine image type based on characteristics
                if aspect_ratio > 2.0:
                    context_parts.append("panoramic or banner image")
                elif aspect_ratio < 0.5:
                    context_parts.append("tall or portrait image")
                elif 0.9 <= aspect_ratio <= 1.1:
                    context_parts.append("square image")
                
                # Analyze size category
                total_pixels = width * height
                if total_pixels > 2000000:  # > 2MP
                    context_parts.append("high resolution")
                elif total_pixels < 100000:  # < 0.1MP
                    context_parts.append("thumbnail or icon")
                
                # Analyze color information
                if image.mode == 'L':
                    context_parts.append("grayscale")
                elif image.mode == 'RGBA':
                    context_parts.append("with transparency")
                
                return ", ".join(context_parts) if context_parts else None
                
        except Exception as e:
            logger.debug(f"Error analyzing visual context of {file_path}: {e}")
            return None
    
    def _extract_filename_context(self, file_path: Path) -> Optional[str]:
        """Extract contextual information from filename and path"""
        try:
            context_parts = []
            
            # Analyze filename
            filename = file_path.stem.lower()
            
            # Common image naming patterns
            patterns = {
                'screenshot': ['screenshot', 'screen', 'capture', 'snap'],
                'photo': ['photo', 'img', 'pic', 'picture', 'image'],
                'logo': ['logo', 'brand', 'icon', 'symbol'],
                'diagram': ['diagram', 'chart', 'graph', 'flow', 'schema'],
                'document': ['doc', 'page', 'scan', 'pdf', 'text'],
                'ui': ['ui', 'interface', 'mockup', 'wireframe', 'design'],
                'avatar': ['avatar', 'profile', 'user', 'person', 'face'],
                'banner': ['banner', 'header', 'hero', 'cover', 'background']
            }
            
            for category, keywords in patterns.items():
                if any(keyword in filename for keyword in keywords):
                    context_parts.append(f"likely {category}")
                    break
            
            # Check for date patterns
            import re
            if re.search(r'\d{4}[-_]\d{2}[-_]\d{2}', filename):
                context_parts.append("dated content")
            
            # Check parent directory for context
            parent_dir = file_path.parent.name.lower()
            if parent_dir in ['screenshots', 'photos', 'images', 'pics', 'documents', 'downloads']:
                context_parts.append(f"from {parent_dir} folder")
            
            return ", ".join(context_parts) if context_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting filename context from {file_path}: {e}")
            return None


def test_image_extraction():
    """Test the enhanced image extractor"""
    extractor = EnhancedImageExtractor()
    
    print("🖼️  ENHANCED IMAGE EXTRACTOR TEST")
    print("=" * 50)
    
    # Check dependencies
    print("📋 Dependency Status:")
    print(f"   PIL/Pillow: {'✅' if HAS_PIL else '❌'}")
    print(f"   pytesseract: {'✅' if HAS_OCR else '❌'}")
    print(f"   OpenCV: {'✅' if HAS_CV2 else '❌'}")
    
    if not HAS_PIL:
        print("\n❌ PIL/Pillow not available - image processing disabled")
        return False
    
    if not HAS_OCR:
        print("\n⚠️  pytesseract not available - OCR disabled")
    
    print(f"\n✅ Enhanced image extractor ready!")
    print(f"📊 Supported formats: {len(extractor.supported_formats)}")
    
    return True


if __name__ == "__main__":
    test_image_extraction()
