<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for semantic_fs\cli.py: 0%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>semantic_fs\cli.py</b>:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">566 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">0<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">566<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_48e1513074be697d_cache_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_48e1513074be697d_config_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">Command-line interface for the semantic filesystem</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">click</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">import</span> <span class="nam">json</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">os</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">subprocess</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">platform</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">import</span> <span class="nam">time</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">pathlib</span> <span class="key">import</span> <span class="nam">Path</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">rich</span><span class="op">.</span><span class="nam">console</span> <span class="key">import</span> <span class="nam">Console</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="nam">rich</span><span class="op">.</span><span class="nam">table</span> <span class="key">import</span> <span class="nam">Table</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">from</span> <span class="nam">rich</span><span class="op">.</span><span class="nam">panel</span> <span class="key">import</span> <span class="nam">Panel</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="nam">rich</span><span class="op">.</span><span class="nam">progress</span> <span class="key">import</span> <span class="nam">Progress</span><span class="op">,</span> <span class="nam">SpinnerColumn</span><span class="op">,</span> <span class="nam">TextColumn</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="nam">rich</span><span class="op">.</span><span class="nam">syntax</span> <span class="key">import</span> <span class="nam">Syntax</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="key">import</span> <span class="nam">logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">core</span> <span class="key">import</span> <span class="nam">SemanticFilesystem</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">models</span> <span class="key">import</span> <span class="nam">SemanticQuery</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">config</span> <span class="key">import</span> <span class="nam">settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="nam">console</span> <span class="op">=</span> <span class="nam">Console</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">group</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--verbose'</span><span class="op">,</span> <span class="str">'-v'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Enable verbose logging'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--log-level'</span><span class="op">,</span> <span class="nam">type</span><span class="op">=</span><span class="nam">click</span><span class="op">.</span><span class="nam">Choice</span><span class="op">(</span><span class="op">[</span><span class="str">'DEBUG'</span><span class="op">,</span> <span class="str">'INFO'</span><span class="op">,</span> <span class="str">'WARNING'</span><span class="op">,</span> <span class="str">'ERROR'</span><span class="op">,</span> <span class="str">'CRITICAL'</span><span class="op">]</span><span class="op">,</span> <span class="nam">case_sensitive</span><span class="op">=</span><span class="key">False</span><span class="op">)</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Set logging level'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--no-file-logging'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Disable file logging'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--json-logging'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Enable JSON structured logging'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t"><span class="key">def</span> <span class="nam">cli</span><span class="op">(</span><span class="nam">verbose</span><span class="op">,</span> <span class="nam">log_level</span><span class="op">,</span> <span class="nam">no_file_logging</span><span class="op">,</span> <span class="nam">json_logging</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="str">"""&#129504; Semantic Filesystem - LLM-powered file organization"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">logging_config</span> <span class="key">import</span> <span class="nam">setup_logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="com"># Determine log level</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="key">if</span> <span class="nam">log_level</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">        <span class="nam">level</span> <span class="op">=</span> <span class="nam">log_level</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="key">elif</span> <span class="nam">verbose</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="nam">level</span> <span class="op">=</span> <span class="str">"DEBUG"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">        <span class="nam">level</span> <span class="op">=</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">log_level</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="com"># Setup comprehensive logging</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="nam">setup_logging</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="nam">log_level</span><span class="op">=</span><span class="nam">level</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">        <span class="nam">enable_file_logging</span><span class="op">=</span><span class="key">not</span> <span class="nam">no_file_logging</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="nam">enable_json_logging</span><span class="op">=</span><span class="nam">json_logging</span> <span class="key">or</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">enable_json_logging</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="nam">enable_rotation</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">enable_log_rotation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'query'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--limit'</span><span class="op">,</span> <span class="str">'-l'</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="num">10</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Maximum number of results'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--json-output'</span><span class="op">,</span> <span class="str">'-j'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Output results as JSON'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t"><span class="key">def</span> <span class="nam">search</span><span class="op">(</span><span class="nam">query</span><span class="op">,</span> <span class="nam">limit</span><span class="op">,</span> <span class="nam">json_output</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="str">"""Search files using natural language"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">semantic_fs</span> <span class="op">=</span> <span class="nam">SemanticFilesystem</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">    <span class="key">with</span> <span class="nam">Progress</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">        <span class="nam">SpinnerColumn</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">        <span class="nam">TextColumn</span><span class="op">(</span><span class="str">"[progress.description]{task.description}"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="nam">console</span><span class="op">=</span><span class="nam">console</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="op">)</span> <span class="key">as</span> <span class="nam">progress</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">        <span class="nam">task</span> <span class="op">=</span> <span class="nam">progress</span><span class="op">.</span><span class="nam">add_task</span><span class="op">(</span><span class="str">"Searching..."</span><span class="op">,</span> <span class="nam">total</span><span class="op">=</span><span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">        <span class="nam">semantic_query</span> <span class="op">=</span> <span class="nam">SemanticQuery</span><span class="op">(</span><span class="nam">query</span><span class="op">=</span><span class="nam">query</span><span class="op">,</span> <span class="nam">limit</span><span class="op">=</span><span class="nam">limit</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">semantic_fs</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">semantic_query</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">    <span class="key">if</span> <span class="nam">json_output</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">        <span class="com"># Convert to JSON-serializable format</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">        <span class="nam">output</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="str">"query"</span><span class="op">:</span> <span class="nam">result</span><span class="op">.</span><span class="nam">query</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="str">"total_found"</span><span class="op">:</span> <span class="nam">result</span><span class="op">.</span><span class="nam">total_found</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">            <span class="str">"execution_time"</span><span class="op">:</span> <span class="nam">result</span><span class="op">.</span><span class="nam">execution_time</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">            <span class="str">"interpretation"</span><span class="op">:</span> <span class="nam">result</span><span class="op">.</span><span class="nam">interpretation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">            <span class="str">"results"</span><span class="op">:</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="key">for</span> <span class="nam">search_result</span> <span class="key">in</span> <span class="nam">result</span><span class="op">.</span><span class="nam">results</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">            <span class="nam">output</span><span class="op">[</span><span class="str">"results"</span><span class="op">]</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">                <span class="str">"file"</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">                    <span class="str">"id"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">                    <span class="str">"path"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">path</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">                    <span class="str">"filename"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">filename</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">                    <span class="str">"file_type"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">file_type</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">                    <span class="str">"size"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">size</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                    <span class="str">"content_summary"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">content_summary</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                    <span class="str">"auto_tags"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">auto_tags</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                    <span class="str">"user_tags"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span><span class="op">.</span><span class="nam">user_tags</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">                <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">                <span class="str">"relevance_score"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">relevance_score</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">                <span class="str">"match_reason"</span><span class="op">:</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">match_reason</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">json</span><span class="op">.</span><span class="nam">dumps</span><span class="op">(</span><span class="nam">output</span><span class="op">,</span> <span class="nam">indent</span><span class="op">=</span><span class="num">2</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="nam">str</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">        <span class="com"># Rich formatted output</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">Panel</span><span class="op">(</span><span class="str">f"&#128269; Query: [bold]{query}[/bold]"</span><span class="op">,</span> <span class="nam">title</span><span class="op">=</span><span class="str">"Search"</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">        <span class="key">if</span> <span class="nam">result</span><span class="op">.</span><span class="nam">interpretation</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128173; Interpreted as: [italic]{result.interpretation}[/italic]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9201;&#65039;  Found {result.total_found} results in {result.execution_time:.2f}s\n"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">result</span><span class="op">.</span><span class="nam">results</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]No files found matching your query.[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">            <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">        <span class="key">for</span> <span class="nam">i</span><span class="op">,</span> <span class="nam">search_result</span> <span class="key">in</span> <span class="nam">enumerate</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">results</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">            <span class="nam">file_info</span> <span class="op">=</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">file</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">            <span class="com"># Create result table</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">            <span class="nam">table</span> <span class="op">=</span> <span class="nam">Table</span><span class="op">(</span><span class="nam">show_header</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">box</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">padding</span><span class="op">=</span><span class="op">(</span><span class="num">0</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Field"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold cyan"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Value"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"&#128193; File"</span><span class="op">,</span> <span class="nam">file_info</span><span class="op">.</span><span class="nam">filename</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"&#128205; Path"</span><span class="op">,</span> <span class="nam">file_info</span><span class="op">.</span><span class="nam">path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"&#128202; Score"</span><span class="op">,</span> <span class="str">f"{search_result.relevance_score:.1%}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"&#127919; Match"</span><span class="op">,</span> <span class="nam">search_result</span><span class="op">.</span><span class="nam">match_reason</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">            <span class="key">if</span> <span class="nam">file_info</span><span class="op">.</span><span class="nam">content_summary</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"&#128221; Summary"</span><span class="op">,</span> <span class="nam">file_info</span><span class="op">.</span><span class="nam">content_summary</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">            <span class="key">if</span> <span class="nam">file_info</span><span class="op">.</span><span class="nam">auto_tags</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">                <span class="nam">tags</span> <span class="op">=</span> <span class="str">" "</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="op">[</span><span class="str">f"[blue]#{tag}[/blue]"</span> <span class="key">for</span> <span class="nam">tag</span> <span class="key">in</span> <span class="nam">file_info</span><span class="op">.</span><span class="nam">auto_tags</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"&#127991;&#65039;  Tags"</span><span class="op">,</span> <span class="nam">tags</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">Panel</span><span class="op">(</span><span class="nam">table</span><span class="op">,</span> <span class="nam">title</span><span class="op">=</span><span class="str">f"Result {i}"</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'path'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--context'</span><span class="op">,</span> <span class="str">'-c'</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Context information as JSON'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t"><span class="key">def</span> <span class="nam">index</span><span class="op">(</span><span class="nam">path</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">    <span class="str">"""Index a file or directory"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">    <span class="nam">semantic_fs</span> <span class="op">=</span> <span class="nam">SemanticFilesystem</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="nam">path_obj</span> <span class="op">=</span> <span class="nam">Path</span><span class="op">(</span><span class="nam">path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">    <span class="com"># Check if path exists</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">path_obj</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Path does not exist: {path}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">        <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">    <span class="com"># Parse context if provided</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="nam">context_data</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">    <span class="key">if</span> <span class="nam">context</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="nam">context_data</span> <span class="op">=</span> <span class="nam">json</span><span class="op">.</span><span class="nam">loads</span><span class="op">(</span><span class="nam">context</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="key">except</span> <span class="nam">json</span><span class="op">.</span><span class="nam">JSONDecodeError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]Invalid JSON in context parameter[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">            <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">    <span class="key">if</span> <span class="nam">path_obj</span><span class="op">.</span><span class="nam">is_file</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">        <span class="com"># Index single file</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">        <span class="key">with</span> <span class="nam">Progress</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">            <span class="nam">SpinnerColumn</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">            <span class="nam">TextColumn</span><span class="op">(</span><span class="str">"[progress.description]{task.description}"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">            <span class="nam">console</span><span class="op">=</span><span class="nam">console</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="op">)</span> <span class="key">as</span> <span class="nam">progress</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">            <span class="nam">task</span> <span class="op">=</span> <span class="nam">progress</span><span class="op">.</span><span class="nam">add_task</span><span class="op">(</span><span class="str">f"Indexing {path_obj.name}..."</span><span class="op">,</span> <span class="nam">total</span><span class="op">=</span><span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">            <span class="nam">result</span> <span class="op">=</span> <span class="nam">semantic_fs</span><span class="op">.</span><span class="nam">index_file</span><span class="op">(</span><span class="nam">str</span><span class="op">(</span><span class="nam">path_obj</span><span class="op">)</span><span class="op">,</span> <span class="nam">context_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">        <span class="key">if</span> <span class="nam">result</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; Successfully indexed: [green]{path_obj}[/green]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#10060; Failed to index: [red]{path_obj}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">    <span class="key">elif</span> <span class="nam">path_obj</span><span class="op">.</span><span class="nam">is_dir</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">        <span class="com"># Index directory</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">        <span class="nam">all_files</span> <span class="op">=</span> <span class="nam">list</span><span class="op">(</span><span class="nam">path_obj</span><span class="op">.</span><span class="nam">rglob</span><span class="op">(</span><span class="str">'*'</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="nam">files_to_index</span> <span class="op">=</span> <span class="op">[</span><span class="nam">f</span> <span class="key">for</span> <span class="nam">f</span> <span class="key">in</span> <span class="nam">all_files</span> <span class="key">if</span> <span class="nam">f</span><span class="op">.</span><span class="nam">is_file</span><span class="op">(</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">        <span class="nam">file_count</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">files_to_index</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">        <span class="key">if</span> <span class="nam">file_count</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[yellow]No files found in directory: {path_obj}[/yellow]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">            <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="key">with</span> <span class="nam">Progress</span><span class="op">(</span><span class="nam">console</span><span class="op">=</span><span class="nam">console</span><span class="op">)</span> <span class="key">as</span> <span class="nam">progress</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">            <span class="nam">task</span> <span class="op">=</span> <span class="nam">progress</span><span class="op">.</span><span class="nam">add_task</span><span class="op">(</span><span class="str">f"Indexing {file_count} files..."</span><span class="op">,</span> <span class="nam">total</span><span class="op">=</span><span class="nam">file_count</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">            <span class="nam">indexed_count</span> <span class="op">=</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">            <span class="key">for</span> <span class="nam">file_path</span> <span class="key">in</span> <span class="nam">files_to_index</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">                <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">                    <span class="nam">result</span> <span class="op">=</span> <span class="nam">semantic_fs</span><span class="op">.</span><span class="nam">index_file</span><span class="op">(</span><span class="nam">str</span><span class="op">(</span><span class="nam">file_path</span><span class="op">)</span><span class="op">,</span> <span class="nam">context_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">                    <span class="key">if</span> <span class="nam">result</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">                        <span class="nam">indexed_count</span> <span class="op">+=</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">                        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; Indexed: [green]{file_path.name}[/green]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">                    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">                        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9888;&#65039;  Skipped: [yellow]{file_path.name}[/yellow]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">                    <span class="nam">progress</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="nam">task</span><span class="op">,</span> <span class="nam">advance</span><span class="op">=</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">                <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">                    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#10060; Error indexing {file_path.name}: [red]{e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">                    <span class="nam">progress</span><span class="op">.</span><span class="nam">update</span><span class="op">(</span><span class="nam">task</span><span class="op">,</span> <span class="nam">advance</span><span class="op">=</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"\n&#9989; Successfully indexed {indexed_count}/{file_count} files"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--limit'</span><span class="op">,</span> <span class="str">'-l'</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Maximum number of files to show'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--json-output'</span><span class="op">,</span> <span class="str">'-j'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Output results as JSON'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t"><span class="key">def</span> <span class="nam">list</span><span class="op">(</span><span class="nam">limit</span><span class="op">,</span> <span class="nam">json_output</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">    <span class="str">"""List indexed files with ID and metadata"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">get_db</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">models</span> <span class="key">import</span> <span class="nam">FileRecord</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">        <span class="key">with</span> <span class="nam">get_db</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">            <span class="nam">files</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">)</span><span class="op">.</span><span class="nam">order_by</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">modified_at</span><span class="op">.</span><span class="nam">desc</span><span class="op">(</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">limit</span><span class="op">(</span><span class="nam">limit</span><span class="op">)</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">            <span class="key">if</span> <span class="nam">json_output</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">                <span class="com"># JSON output</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">                <span class="nam">output</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">                <span class="key">for</span> <span class="nam">file_record</span> <span class="key">in</span> <span class="nam">files</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">                    <span class="nam">output</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">                        <span class="str">"id"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">                        <span class="str">"filename"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">filename</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">                        <span class="str">"path"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">path</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">                        <span class="str">"file_type"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">file_type</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">                        <span class="str">"size"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">size</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">                        <span class="str">"created_at"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">created_at</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">                        <span class="str">"modified_at"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">modified_at</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">                        <span class="str">"content_summary"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">content_summary</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">                        <span class="str">"auto_tags"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">auto_tags</span> <span class="key">or</span> <span class="op">[</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">                        <span class="str">"user_tags"</span><span class="op">:</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">user_tags</span> <span class="key">or</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">                    <span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">json</span><span class="op">.</span><span class="nam">dumps</span><span class="op">(</span><span class="nam">output</span><span class="op">,</span> <span class="nam">indent</span><span class="op">=</span><span class="num">2</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">                <span class="com"># Rich formatted output</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">                <span class="key">if</span> <span class="key">not</span> <span class="nam">files</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">                    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[yellow]No files indexed yet. Use 'index' command to add files.[/yellow]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">                    <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">                <span class="nam">table</span> <span class="op">=</span> <span class="nam">Table</span><span class="op">(</span><span class="nam">title</span><span class="op">=</span><span class="str">f"&#128203; Indexed Files (showing {len(files)} most recent)"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"ID"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold cyan"</span><span class="op">,</span> <span class="nam">width</span><span class="op">=</span><span class="num">6</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Filename"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Type"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"green"</span><span class="op">,</span> <span class="nam">width</span><span class="op">=</span><span class="num">12</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Size"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"yellow"</span><span class="op">,</span> <span class="nam">width</span><span class="op">=</span><span class="num">10</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Modified"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"blue"</span><span class="op">,</span> <span class="nam">width</span><span class="op">=</span><span class="num">12</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">                <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Tags"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"magenta"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">                <span class="key">for</span> <span class="nam">file_record</span> <span class="key">in</span> <span class="nam">files</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">                    <span class="nam">size_mb</span> <span class="op">=</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">size</span> <span class="op">/</span> <span class="op">(</span><span class="num">1024</span> <span class="op">*</span> <span class="num">1024</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">                    <span class="nam">size_str</span> <span class="op">=</span> <span class="str">f"{size_mb:.1f} MB"</span> <span class="key">if</span> <span class="nam">size_mb</span> <span class="op">>=</span> <span class="num">1</span> <span class="key">else</span> <span class="str">f"{file_record.size / 1024:.1f} KB"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">                    <span class="nam">tags</span> <span class="op">=</span> <span class="str">", "</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">file_record</span><span class="op">.</span><span class="nam">auto_tags</span><span class="op">[</span><span class="op">:</span><span class="num">3</span><span class="op">]</span><span class="op">)</span> <span class="key">if</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">auto_tags</span> <span class="key">else</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">                    <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">file_record</span><span class="op">.</span><span class="nam">auto_tags</span> <span class="key">or</span> <span class="op">[</span><span class="op">]</span><span class="op">)</span> <span class="op">></span> <span class="num">3</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">                        <span class="nam">tags</span> <span class="op">+=</span> <span class="str">"..."</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">                    <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">                        <span class="nam">str</span><span class="op">(</span><span class="nam">file_record</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">                        <span class="nam">file_record</span><span class="op">.</span><span class="nam">filename</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">                        <span class="nam">file_record</span><span class="op">.</span><span class="nam">file_type</span><span class="op">.</span><span class="nam">split</span><span class="op">(</span><span class="str">'/'</span><span class="op">)</span><span class="op">[</span><span class="op">-</span><span class="num">1</span><span class="op">]</span> <span class="key">if</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">file_type</span> <span class="key">else</span> <span class="str">"unknown"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">                        <span class="nam">size_str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">                        <span class="nam">file_record</span><span class="op">.</span><span class="nam">modified_at</span><span class="op">.</span><span class="nam">strftime</span><span class="op">(</span><span class="str">'%Y-%m-%d'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">                        <span class="nam">tags</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">                    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">table</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"\n&#128161; Use 'open &lt;id>' to open a file or 'search \"&lt;query>\"' to find specific files"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Error listing files: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t"><span class="key">def</span> <span class="nam">cache</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">    <span class="str">"""Show cache statistics and management"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">cache</span> <span class="key">import</span> <span class="nam">get_memory_cache</span><span class="op">,</span> <span class="nam">get_query_cache</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">    <span class="nam">cache</span> <span class="op">=</span> <span class="nam">get_memory_cache</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">    <span class="nam">query_cache</span> <span class="op">=</span> <span class="nam">get_query_cache</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">    <span class="com"># Get cache stats</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">    <span class="nam">stats</span> <span class="op">=</span> <span class="nam">cache</span><span class="op">.</span><span class="nam">get_stats</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128452;&#65039;  [bold]Cache Statistics[/bold]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128202; Total entries: {stats['total_entries']}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; Active entries: {stats['active_entries']}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9200; Expired entries: {stats['expired_entries']}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128190; Memory usage: {stats['memory_usage_mb']:.2f} MB"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">    <span class="com"># Cleanup expired entries</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">    <span class="nam">cleaned</span> <span class="op">=</span> <span class="nam">cache</span><span class="op">.</span><span class="nam">cleanup_expired</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">    <span class="key">if</span> <span class="nam">cleaned</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#129529; Cleaned up {cleaned} expired entries"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"\n&#128161; Cache commands:"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; Use --clear to clear all cache"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; Use --clear-queries to clear query cache only"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t"><span class="key">def</span> <span class="nam">stats</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">    <span class="str">"""Show filesystem statistics"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">get_db</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">models</span> <span class="key">import</span> <span class="nam">FileRecord</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">    <span class="key">from</span> <span class="nam">sqlalchemy</span> <span class="key">import</span> <span class="nam">func</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">        <span class="key">with</span> <span class="nam">get_db</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">            <span class="nam">total_files</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">func</span><span class="op">.</span><span class="nam">count</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">scalar</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">            <span class="nam">total_size</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">func</span><span class="op">.</span><span class="nam">sum</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">size</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">scalar</span><span class="op">(</span><span class="op">)</span> <span class="key">or</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">            <span class="com"># Get file type distribution</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">            <span class="nam">file_types</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">                <span class="nam">FileRecord</span><span class="op">.</span><span class="nam">file_type</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">                <span class="nam">func</span><span class="op">.</span><span class="nam">count</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">.</span><span class="nam">label</span><span class="op">(</span><span class="str">'count'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">            <span class="op">)</span><span class="op">.</span><span class="nam">group_by</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">file_type</span><span class="op">)</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">            <span class="com"># Create stats table</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">            <span class="nam">table</span> <span class="op">=</span> <span class="nam">Table</span><span class="op">(</span><span class="nam">title</span><span class="op">=</span><span class="str">"&#128202; Semantic Filesystem Statistics"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Metric"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold cyan"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Value"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"green"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Total Files"</span><span class="op">,</span> <span class="nam">str</span><span class="op">(</span><span class="nam">total_files</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">            <span class="nam">table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Total Size"</span><span class="op">,</span> <span class="str">f"{total_size / (1024*1024):.1f} MB"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">table</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">            <span class="com"># File types table</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">            <span class="key">if</span> <span class="nam">file_types</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">                <span class="nam">types_table</span> <span class="op">=</span> <span class="nam">Table</span><span class="op">(</span><span class="nam">title</span><span class="op">=</span><span class="str">"&#128193; File Types"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">                <span class="nam">types_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Type"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">                <span class="nam">types_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Count"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"green"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">                <span class="key">for</span> <span class="nam">file_type</span> <span class="key">in</span> <span class="nam">file_types</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">                    <span class="nam">types_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="nam">file_type</span><span class="op">.</span><span class="nam">file_type</span> <span class="key">or</span> <span class="str">"Unknown"</span><span class="op">,</span> <span class="nam">str</span><span class="op">(</span><span class="nam">file_type</span><span class="op">.</span><span class="nam">count</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">types_table</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Error getting statistics: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t"><span class="key">def</span> <span class="nam">serve</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">    <span class="str">"""Start the web API server"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">    <span class="key">import</span> <span class="nam">uvicorn</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128640; Starting Semantic Filesystem API on http://{settings.api_host}:{settings.api_port}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128214; API documentation available at http://localhost:8000/docs"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#127760; Web interface available at http://localhost:8000"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9203; Initializing server..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t">        <span class="nam">uvicorn</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t">            <span class="str">"semantic_fs.api:app"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">            <span class="nam">host</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">api_host</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t">            <span class="nam">port</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">api_port</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">            <span class="nam">reload</span><span class="op">=</span><span class="key">False</span><span class="op">,</span>  <span class="com"># Disable reload to avoid issues</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">            <span class="nam">log_level</span><span class="op">=</span><span class="str">"info"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Failed to start server: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'path'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--recursive/--no-recursive'</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Monitor subdirectories recursively'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t"><span class="key">def</span> <span class="nam">watch</span><span class="op">(</span><span class="nam">path</span><span class="op">,</span> <span class="nam">recursive</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">    <span class="str">"""Start monitoring a directory for file changes with improved Windows support"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">core</span> <span class="key">import</span> <span class="nam">SemanticFilesystem</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">file_monitor</span> <span class="key">import</span> <span class="nam">FileMonitor</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">signal_handler</span> <span class="key">import</span> <span class="nam">setup_signal_handling</span><span class="op">,</span> <span class="nam">is_shutdown_initiated</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t361" href="#t361">361</a></span><span class="t">    <span class="key">import</span> <span class="nam">sys</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t362" href="#t362">362</a></span><span class="t">    <span class="key">import</span> <span class="nam">platform</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t363" href="#t363">363</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t364" href="#t364">364</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128269; Starting file monitor for: {path}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t365" href="#t365">365</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128193; Recursive monitoring: {'enabled' if recursive else 'disabled'}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t366" href="#t366">366</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128187; Platform: {platform.system()}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t367" href="#t367">367</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t368" href="#t368">368</a></span><span class="t">    <span class="nam">monitor</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t369" href="#t369">369</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t370" href="#t370">370</a></span><span class="t">        <span class="com"># Setup signal handling</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t371" href="#t371">371</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128737;&#65039;  Setting up signal handling..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t372" href="#t372">372</a></span><span class="t">        <span class="nam">signal_manager</span> <span class="op">=</span> <span class="nam">setup_signal_handling</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t373" href="#t373">373</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t374" href="#t374">374</a></span><span class="t">        <span class="com"># Initialize semantic filesystem</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t375" href="#t375">375</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128640; Initializing semantic filesystem..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t376" href="#t376">376</a></span><span class="t">        <span class="nam">semantic_fs</span> <span class="op">=</span> <span class="nam">SemanticFilesystem</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t377" href="#t377">377</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t378" href="#t378">378</a></span><span class="t">        <span class="com"># Create file monitor</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t379" href="#t379">379</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128225; Creating file monitor..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t380" href="#t380">380</a></span><span class="t">        <span class="nam">monitor</span> <span class="op">=</span> <span class="nam">FileMonitor</span><span class="op">(</span><span class="nam">semantic_fs</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t381" href="#t381">381</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t382" href="#t382">382</a></span><span class="t">        <span class="com"># Add watch path</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t383" href="#t383">383</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128194; Adding watch path: {path}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t384" href="#t384">384</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">monitor</span><span class="op">.</span><span class="nam">add_watch_path</span><span class="op">(</span><span class="nam">path</span><span class="op">,</span> <span class="nam">recursive</span><span class="op">=</span><span class="nam">recursive</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t385" href="#t385">385</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; Failed to add watch path[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t386" href="#t386">386</a></span><span class="t">            <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t387" href="#t387">387</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t388" href="#t388">388</a></span><span class="t">        <span class="com"># Start monitoring</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t389" href="#t389">389</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9654;&#65039;  Starting file monitoring..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t390" href="#t390">390</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">monitor</span><span class="op">.</span><span class="nam">start</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t391" href="#t391">391</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; Failed to start file monitor[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t392" href="#t392">392</a></span><span class="t">            <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t393" href="#t393">393</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t394" href="#t394">394</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; File monitor started successfully!"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t395" href="#t395">395</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128221; Supported file types: TXT, MD, JSON, CSV, PY, JS, HTML, CSS, PDF, DOCX, XLSX, XML, YAML, LOG"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t396" href="#t396">396</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9201;&#65039;  Files are processed with 2-second debouncing"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t397" href="#t397">397</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128721; Press Ctrl+C to stop monitoring"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t398" href="#t398">398</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t399" href="#t399">399</a></span><span class="t">        <span class="com"># Keep the main thread alive until shutdown is initiated</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t400" href="#t400">400</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t401" href="#t401">401</a></span><span class="t">            <span class="key">while</span> <span class="nam">monitor</span><span class="op">.</span><span class="nam">is_running</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">is_shutdown_initiated</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t402" href="#t402">402</a></span><span class="t">                <span class="nam">time</span><span class="op">.</span><span class="nam">sleep</span><span class="op">(</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t403" href="#t403">403</a></span><span class="t">        <span class="key">except</span> <span class="nam">KeyboardInterrupt</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t404" href="#t404">404</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"\n&#128721; Keyboard interrupt received..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t405" href="#t405">405</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t406" href="#t406">406</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">is_shutdown_initiated</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t407" href="#t407">407</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128721; Stopping file monitor..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t408" href="#t408">408</a></span><span class="t">            <span class="key">if</span> <span class="nam">monitor</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t409" href="#t409">409</a></span><span class="t">                <span class="nam">monitor</span><span class="op">.</span><span class="nam">stop</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t410" href="#t410">410</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t411" href="#t411">411</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; File monitor stopped"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t412" href="#t412">412</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t413" href="#t413">413</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t414" href="#t414">414</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t415" href="#t415">415</a></span><span class="t">        <span class="key">if</span> <span class="nam">monitor</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t416" href="#t416">416</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t417" href="#t417">417</a></span><span class="t">                <span class="nam">monitor</span><span class="op">.</span><span class="nam">stop</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t418" href="#t418">418</a></span><span class="t">            <span class="key">except</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t419" href="#t419">419</a></span><span class="t">                <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t420" href="#t420">420</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t421" href="#t421">421</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t422" href="#t422">422</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'file_identifier'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t423" href="#t423">423</a></span><span class="t"><span class="key">def</span> <span class="nam">open</span><span class="op">(</span><span class="nam">file_identifier</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t424" href="#t424">424</a></span><span class="t">    <span class="str">"""Open a file in the default viewer (by ID or path)"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t425" href="#t425">425</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">get_db</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t426" href="#t426">426</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">models</span> <span class="key">import</span> <span class="nam">FileRecord</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t427" href="#t427">427</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t428" href="#t428">428</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t429" href="#t429">429</a></span><span class="t">        <span class="key">with</span> <span class="nam">get_db</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t430" href="#t430">430</a></span><span class="t">            <span class="nam">file_record</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t431" href="#t431">431</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t432" href="#t432">432</a></span><span class="t">            <span class="com"># Try to parse as ID first</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t433" href="#t433">433</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t434" href="#t434">434</a></span><span class="t">                <span class="nam">file_id</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">file_identifier</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t435" href="#t435">435</a></span><span class="t">                <span class="nam">file_record</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">file_id</span><span class="op">)</span><span class="op">.</span><span class="nam">first</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t436" href="#t436">436</a></span><span class="t">            <span class="key">except</span> <span class="nam">ValueError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t437" href="#t437">437</a></span><span class="t">                <span class="com"># If not a number, treat as path</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t438" href="#t438">438</a></span><span class="t">                <span class="nam">file_record</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">path</span> <span class="op">==</span> <span class="nam">file_identifier</span><span class="op">)</span><span class="op">.</span><span class="nam">first</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t439" href="#t439">439</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t440" href="#t440">440</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">file_record</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t441" href="#t441">441</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]File not found: {file_identifier}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t442" href="#t442">442</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128161; Use 'list' command to see available files and their IDs"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t443" href="#t443">443</a></span><span class="t">                <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t444" href="#t444">444</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t445" href="#t445">445</a></span><span class="t">            <span class="nam">file_path</span> <span class="op">=</span> <span class="nam">Path</span><span class="op">(</span><span class="nam">file_record</span><span class="op">.</span><span class="nam">path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t446" href="#t446">446</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">file_path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t447" href="#t447">447</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]File no longer exists at: {file_record.path}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t448" href="#t448">448</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128161; The file may have been moved or deleted. Consider removing it from the index."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t449" href="#t449">449</a></span><span class="t">                <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t450" href="#t450">450</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t451" href="#t451">451</a></span><span class="t">            <span class="com"># Open file with default application</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t452" href="#t452">452</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t453" href="#t453">453</a></span><span class="t">                <span class="nam">system</span> <span class="op">=</span> <span class="nam">platform</span><span class="op">.</span><span class="nam">system</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t454" href="#t454">454</a></span><span class="t">                <span class="key">if</span> <span class="nam">system</span> <span class="op">==</span> <span class="str">"Windows"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t455" href="#t455">455</a></span><span class="t">                    <span class="nam">os</span><span class="op">.</span><span class="nam">startfile</span><span class="op">(</span><span class="nam">str</span><span class="op">(</span><span class="nam">file_path</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t456" href="#t456">456</a></span><span class="t">                <span class="key">elif</span> <span class="nam">system</span> <span class="op">==</span> <span class="str">"Darwin"</span><span class="op">:</span>  <span class="com"># macOS</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t457" href="#t457">457</a></span><span class="t">                    <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="op">[</span><span class="str">"open"</span><span class="op">,</span> <span class="nam">str</span><span class="op">(</span><span class="nam">file_path</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t458" href="#t458">458</a></span><span class="t">                <span class="key">else</span><span class="op">:</span>  <span class="com"># Linux and others</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t459" href="#t459">459</a></span><span class="t">                    <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="op">[</span><span class="str">"xdg-open"</span><span class="op">,</span> <span class="nam">str</span><span class="op">(</span><span class="nam">file_path</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t460" href="#t460">460</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t461" href="#t461">461</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; Opened [green]{file_record.filename}[/green] in default application"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t462" href="#t462">462</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t463" href="#t463">463</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t464" href="#t464">464</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Error opening file: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t465" href="#t465">465</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128161; File path: {file_record.path}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t466" href="#t466">466</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t467" href="#t467">467</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t468" href="#t468">468</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Error finding file: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t469" href="#t469">469</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t470" href="#t470">470</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t471" href="#t471">471</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'file_id'</span><span class="op">,</span> <span class="nam">type</span><span class="op">=</span><span class="nam">int</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t472" href="#t472">472</a></span><span class="t"><span class="key">def</span> <span class="nam">delete</span><span class="op">(</span><span class="nam">file_id</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t473" href="#t473">473</a></span><span class="t">    <span class="str">"""Remove a file from the index"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t474" href="#t474">474</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">database</span> <span class="key">import</span> <span class="nam">get_db</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t475" href="#t475">475</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">models</span> <span class="key">import</span> <span class="nam">FileRecord</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t476" href="#t476">476</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t477" href="#t477">477</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t478" href="#t478">478</a></span><span class="t">        <span class="nam">semantic_fs</span> <span class="op">=</span> <span class="nam">SemanticFilesystem</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t479" href="#t479">479</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t480" href="#t480">480</a></span><span class="t">        <span class="key">with</span> <span class="nam">get_db</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">db</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t481" href="#t481">481</a></span><span class="t">            <span class="nam">file_record</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">FileRecord</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">file_id</span><span class="op">)</span><span class="op">.</span><span class="nam">first</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t482" href="#t482">482</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">file_record</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t483" href="#t483">483</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]File with ID {file_id} not found[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t484" href="#t484">484</a></span><span class="t">                <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t485" href="#t485">485</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t486" href="#t486">486</a></span><span class="t">            <span class="nam">filename</span> <span class="op">=</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">filename</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t487" href="#t487">487</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t488" href="#t488">488</a></span><span class="t">            <span class="com"># Delete from vector database</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t489" href="#t489">489</a></span><span class="t">            <span class="key">if</span> <span class="nam">file_record</span><span class="op">.</span><span class="nam">embedding_id</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t490" href="#t490">490</a></span><span class="t">                <span class="nam">semantic_fs</span><span class="op">.</span><span class="nam">embedding_engine</span><span class="op">.</span><span class="nam">delete_embedding</span><span class="op">(</span><span class="nam">file_record</span><span class="op">.</span><span class="nam">embedding_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t491" href="#t491">491</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t492" href="#t492">492</a></span><span class="t">            <span class="com"># Delete from database</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t493" href="#t493">493</a></span><span class="t">            <span class="nam">db</span><span class="op">.</span><span class="nam">delete</span><span class="op">(</span><span class="nam">file_record</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t494" href="#t494">494</a></span><span class="t">            <span class="nam">db</span><span class="op">.</span><span class="nam">commit</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t495" href="#t495">495</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t496" href="#t496">496</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; Removed [green]{filename}[/green] from index"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t497" href="#t497">497</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t498" href="#t498">498</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t499" href="#t499">499</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Error deleting file: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t500" href="#t500">500</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t501" href="#t501">501</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t502" href="#t502">502</a></span><span class="t"><span class="key">def</span> <span class="nam">config</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t503" href="#t503">503</a></span><span class="t">    <span class="str">"""Show current configuration"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t504" href="#t504">504</a></span><span class="t">    <span class="nam">config_table</span> <span class="op">=</span> <span class="nam">Table</span><span class="op">(</span><span class="nam">title</span><span class="op">=</span><span class="str">"&#9881;&#65039; Configuration"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t505" href="#t505">505</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Setting"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold cyan"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t506" href="#t506">506</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Value"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"green"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t507" href="#t507">507</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t508" href="#t508">508</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Database URL"</span><span class="op">,</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">database_url</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t509" href="#t509">509</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Vector DB Path"</span><span class="op">,</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">vector_db_path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t510" href="#t510">510</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Embedding Model"</span><span class="op">,</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">embedding_model</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t511" href="#t511">511</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"LLM Model"</span><span class="op">,</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">llm_model</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t512" href="#t512">512</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"API Host"</span><span class="op">,</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">api_host</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t513" href="#t513">513</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"API Port"</span><span class="op">,</span> <span class="nam">str</span><span class="op">(</span><span class="nam">settings</span><span class="op">.</span><span class="nam">api_port</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t514" href="#t514">514</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Max File Size"</span><span class="op">,</span> <span class="str">f"{settings.max_file_size / (1024*1024):.1f} MB"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t515" href="#t515">515</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Auto-tagging"</span><span class="op">,</span> <span class="str">"&#9989;"</span> <span class="key">if</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">auto_tag_enabled</span> <span class="key">else</span> <span class="str">"&#10060;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t516" href="#t516">516</a></span><span class="t">    <span class="nam">config_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="str">"Content Summary"</span><span class="op">,</span> <span class="str">"&#9989;"</span> <span class="key">if</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">content_summary_enabled</span> <span class="key">else</span> <span class="str">"&#10060;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t517" href="#t517">517</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t518" href="#t518">518</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">config_table</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t519" href="#t519">519</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t520" href="#t520">520</a></span><span class="t"><span class="com"># Database migration commands</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t521" href="#t521">521</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">group</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t522" href="#t522">522</a></span><span class="t"><span class="key">def</span> <span class="nam">db</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t523" href="#t523">523</a></span><span class="t">    <span class="str">"""Database management commands"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t524" href="#t524">524</a></span><span class="t">    <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t525" href="#t525">525</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t526" href="#t526">526</a></span><span class="t"><span class="op">@</span><span class="nam">db</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t527" href="#t527">527</a></span><span class="t"><span class="key">def</span> <span class="nam">init</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t528" href="#t528">528</a></span><span class="t">    <span class="str">"""Initialize database with migration support"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t529" href="#t529">529</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">migrations</span> <span class="key">import</span> <span class="nam">get_migration_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t530" href="#t530">530</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t531" href="#t531">531</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128452;&#65039;  Initializing database..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t532" href="#t532">532</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t533" href="#t533">533</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t534" href="#t534">534</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">get_migration_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t535" href="#t535">535</a></span><span class="t">        <span class="key">if</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">initialize_database</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t536" href="#t536">536</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Database initialized successfully"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t537" href="#t537">537</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t538" href="#t538">538</a></span><span class="t">            <span class="com"># Show status</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t539" href="#t539">539</a></span><span class="t">            <span class="nam">status</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">get_status</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t540" href="#t540">540</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128202; Current revision: {status['current_revision'] or 'None'}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t541" href="#t541">541</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128202; Head revision: {status['head_revision'] or 'None'}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t542" href="#t542">542</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t543" href="#t543">543</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; Failed to initialize database[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t544" href="#t544">544</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t545" href="#t545">545</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t546" href="#t546">546</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t547" href="#t547">547</a></span><span class="t"><span class="op">@</span><span class="nam">db</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t548" href="#t548">548</a></span><span class="t"><span class="key">def</span> <span class="nam">status</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t549" href="#t549">549</a></span><span class="t">    <span class="str">"""Show database migration status"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t550" href="#t550">550</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">migrations</span> <span class="key">import</span> <span class="nam">get_migration_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t551" href="#t551">551</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t552" href="#t552">552</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t553" href="#t553">553</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">get_migration_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t554" href="#t554">554</a></span><span class="t">        <span class="nam">status</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">get_status</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t555" href="#t555">555</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t556" href="#t556">556</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128202; Database Migration Status"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t557" href="#t557">557</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Database URL: {status['database_url']}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t558" href="#t558">558</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Current revision: {status['current_revision'] or 'None'}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t559" href="#t559">559</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Head revision: {status['head_revision'] or 'None'}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t560" href="#t560">560</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Up to date: {'&#9989; Yes' if status['is_up_to_date'] else '&#10060; No'}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t561" href="#t561">561</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t562" href="#t562">562</a></span><span class="t">        <span class="nam">pending</span> <span class="op">=</span> <span class="nam">status</span><span class="op">[</span><span class="str">'pending_migrations'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t563" href="#t563">563</a></span><span class="t">        <span class="key">if</span> <span class="nam">pending</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t564" href="#t564">564</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Pending migrations: {len(pending)}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t565" href="#t565">565</a></span><span class="t">            <span class="key">for</span> <span class="nam">rev</span> <span class="key">in</span> <span class="nam">pending</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t566" href="#t566">566</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"  - {rev}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t567" href="#t567">567</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t568" href="#t568">568</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"No pending migrations"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t569" href="#t569">569</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t570" href="#t570">570</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t571" href="#t571">571</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t572" href="#t572">572</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t573" href="#t573">573</a></span><span class="t"><span class="op">@</span><span class="nam">db</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t574" href="#t574">574</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'message'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t575" href="#t575">575</a></span><span class="t"><span class="key">def</span> <span class="nam">migrate</span><span class="op">(</span><span class="nam">message</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t576" href="#t576">576</a></span><span class="t">    <span class="str">"""Create a new migration"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t577" href="#t577">577</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">migrations</span> <span class="key">import</span> <span class="nam">get_migration_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t578" href="#t578">578</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t579" href="#t579">579</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128221; Creating migration: {message}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t580" href="#t580">580</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t581" href="#t581">581</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t582" href="#t582">582</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">get_migration_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t583" href="#t583">583</a></span><span class="t">        <span class="key">if</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">create_migration</span><span class="op">(</span><span class="nam">message</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t584" href="#t584">584</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Migration created successfully"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t585" href="#t585">585</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t586" href="#t586">586</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; Failed to create migration[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t587" href="#t587">587</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t588" href="#t588">588</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t589" href="#t589">589</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t590" href="#t590">590</a></span><span class="t"><span class="op">@</span><span class="nam">db</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t591" href="#t591">591</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--revision'</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'head'</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Target revision (default: head)'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t592" href="#t592">592</a></span><span class="t"><span class="key">def</span> <span class="nam">upgrade</span><span class="op">(</span><span class="nam">revision</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t593" href="#t593">593</a></span><span class="t">    <span class="str">"""Upgrade database to specified revision"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t594" href="#t594">594</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">migrations</span> <span class="key">import</span> <span class="nam">get_migration_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t595" href="#t595">595</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t596" href="#t596">596</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#11014;&#65039;  Upgrading database to: {revision}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t597" href="#t597">597</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t598" href="#t598">598</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t599" href="#t599">599</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">get_migration_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t600" href="#t600">600</a></span><span class="t">        <span class="key">if</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">upgrade_database</span><span class="op">(</span><span class="nam">revision</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t601" href="#t601">601</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Database upgrade completed"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t602" href="#t602">602</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t603" href="#t603">603</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; Database upgrade failed[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t604" href="#t604">604</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t605" href="#t605">605</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t606" href="#t606">606</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t607" href="#t607">607</a></span><span class="t"><span class="com"># Logging management commands</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t608" href="#t608">608</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">group</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t609" href="#t609">609</a></span><span class="t"><span class="key">def</span> <span class="nam">logs</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t610" href="#t610">610</a></span><span class="t">    <span class="str">"""Logging management commands"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t611" href="#t611">611</a></span><span class="t">    <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t612" href="#t612">612</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t613" href="#t613">613</a></span><span class="t"><span class="op">@</span><span class="nam">logs</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t614" href="#t614">614</a></span><span class="t"><span class="key">def</span> <span class="nam">status</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t615" href="#t615">615</a></span><span class="t">    <span class="str">"""Show logging status and statistics"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t616" href="#t616">616</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">logging_config</span> <span class="key">import</span> <span class="nam">get_log_stats</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t617" href="#t617">617</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t618" href="#t618">618</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t619" href="#t619">619</a></span><span class="t">        <span class="nam">stats</span> <span class="op">=</span> <span class="nam">get_log_stats</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t620" href="#t620">620</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t621" href="#t621">621</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128202; Logging Status"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t622" href="#t622">622</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Log directory: {stats['log_directory']}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t623" href="#t623">623</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t624" href="#t624">624</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t625" href="#t625">625</a></span><span class="t">        <span class="key">if</span> <span class="nam">stats</span><span class="op">[</span><span class="str">'files'</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t626" href="#t626">626</a></span><span class="t">            <span class="nam">log_table</span> <span class="op">=</span> <span class="nam">Table</span><span class="op">(</span><span class="nam">title</span><span class="op">=</span><span class="str">"Log Files"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t627" href="#t627">627</a></span><span class="t">            <span class="nam">log_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"File"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"bold cyan"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t628" href="#t628">628</a></span><span class="t">            <span class="nam">log_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Size"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"green"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t629" href="#t629">629</a></span><span class="t">            <span class="nam">log_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Last Modified"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"yellow"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t630" href="#t630">630</a></span><span class="t">            <span class="nam">log_table</span><span class="op">.</span><span class="nam">add_column</span><span class="op">(</span><span class="str">"Path"</span><span class="op">,</span> <span class="nam">style</span><span class="op">=</span><span class="str">"dim"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t631" href="#t631">631</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t632" href="#t632">632</a></span><span class="t">            <span class="key">for</span> <span class="nam">name</span><span class="op">,</span> <span class="nam">info</span> <span class="key">in</span> <span class="nam">stats</span><span class="op">[</span><span class="str">'files'</span><span class="op">]</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t633" href="#t633">633</a></span><span class="t">                <span class="key">if</span> <span class="nam">info</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'exists'</span><span class="op">,</span> <span class="key">True</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t634" href="#t634">634</a></span><span class="t">                    <span class="nam">size_str</span> <span class="op">=</span> <span class="str">f"{info['size_mb']} MB"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t635" href="#t635">635</a></span><span class="t">                    <span class="nam">modified</span> <span class="op">=</span> <span class="nam">info</span><span class="op">[</span><span class="str">'modified'</span><span class="op">]</span><span class="op">[</span><span class="op">:</span><span class="num">19</span><span class="op">]</span><span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">'T'</span><span class="op">,</span> <span class="str">' '</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t636" href="#t636">636</a></span><span class="t">                    <span class="nam">log_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="nam">size_str</span><span class="op">,</span> <span class="nam">modified</span><span class="op">,</span> <span class="nam">info</span><span class="op">[</span><span class="str">'path'</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t637" href="#t637">637</a></span><span class="t">                <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t638" href="#t638">638</a></span><span class="t">                    <span class="nam">log_table</span><span class="op">.</span><span class="nam">add_row</span><span class="op">(</span><span class="nam">name</span><span class="op">,</span> <span class="str">"N/A"</span><span class="op">,</span> <span class="str">"N/A"</span><span class="op">,</span> <span class="nam">info</span><span class="op">[</span><span class="str">'path'</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t639" href="#t639">639</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t640" href="#t640">640</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">log_table</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t641" href="#t641">641</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t642" href="#t642">642</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"No log files found"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t643" href="#t643">643</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t644" href="#t644">644</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t645" href="#t645">645</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t646" href="#t646">646</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t647" href="#t647">647</a></span><span class="t"><span class="op">@</span><span class="nam">logs</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t648" href="#t648">648</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">argument</span><span class="op">(</span><span class="str">'file_type'</span><span class="op">,</span> <span class="nam">type</span><span class="op">=</span><span class="nam">click</span><span class="op">.</span><span class="nam">Choice</span><span class="op">(</span><span class="op">[</span><span class="str">'main'</span><span class="op">,</span> <span class="str">'errors'</span><span class="op">,</span> <span class="str">'all'</span><span class="op">]</span><span class="op">)</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'main'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t649" href="#t649">649</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--lines'</span><span class="op">,</span> <span class="str">'-n'</span><span class="op">,</span> <span class="nam">type</span><span class="op">=</span><span class="nam">int</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Number of lines to show'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t650" href="#t650">650</a></span><span class="t"><span class="key">def</span> <span class="nam">tail</span><span class="op">(</span><span class="nam">file_type</span><span class="op">,</span> <span class="nam">lines</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t651" href="#t651">651</a></span><span class="t">    <span class="str">"""Show recent log entries"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t652" href="#t652">652</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">logging_config</span> <span class="key">import</span> <span class="nam">get_logging_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t653" href="#t653">653</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t654" href="#t654">654</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t655" href="#t655">655</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">get_logging_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t656" href="#t656">656</a></span><span class="t">        <span class="nam">log_files</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">get_log_files</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t657" href="#t657">657</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t658" href="#t658">658</a></span><span class="t">        <span class="nam">files_to_show</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t659" href="#t659">659</a></span><span class="t">        <span class="key">if</span> <span class="nam">file_type</span> <span class="op">==</span> <span class="str">'all'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t660" href="#t660">660</a></span><span class="t">            <span class="nam">files_to_show</span> <span class="op">=</span> <span class="nam">list</span><span class="op">(</span><span class="nam">log_files</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t661" href="#t661">661</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t662" href="#t662">662</a></span><span class="t">            <span class="nam">files_to_show</span> <span class="op">=</span> <span class="op">[</span><span class="op">(</span><span class="nam">file_type</span><span class="op">,</span> <span class="nam">log_files</span><span class="op">[</span><span class="nam">file_type</span><span class="op">]</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t663" href="#t663">663</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t664" href="#t664">664</a></span><span class="t">        <span class="key">for</span> <span class="nam">name</span><span class="op">,</span> <span class="nam">path</span> <span class="key">in</span> <span class="nam">files_to_show</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t665" href="#t665">665</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t666" href="#t666">666</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[yellow]Log file {name} does not exist: {path}[/yellow]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t667" href="#t667">667</a></span><span class="t">                <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t668" href="#t668">668</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t669" href="#t669">669</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"\n&#128196; {name.title()} Log (last {lines} lines):"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t670" href="#t670">670</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9472;"</span> <span class="op">*</span> <span class="num">80</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t671" href="#t671">671</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t672" href="#t672">672</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t673" href="#t673">673</a></span><span class="t">                <span class="key">with</span> <span class="nam">open</span><span class="op">(</span><span class="nam">path</span><span class="op">,</span> <span class="str">'r'</span><span class="op">,</span> <span class="nam">encoding</span><span class="op">=</span><span class="str">'utf-8'</span><span class="op">)</span> <span class="key">as</span> <span class="nam">f</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t674" href="#t674">674</a></span><span class="t">                    <span class="nam">all_lines</span> <span class="op">=</span> <span class="nam">f</span><span class="op">.</span><span class="nam">readlines</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t675" href="#t675">675</a></span><span class="t">                    <span class="nam">recent_lines</span> <span class="op">=</span> <span class="nam">all_lines</span><span class="op">[</span><span class="op">-</span><span class="nam">lines</span><span class="op">:</span><span class="op">]</span> <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">all_lines</span><span class="op">)</span> <span class="op">></span> <span class="nam">lines</span> <span class="key">else</span> <span class="nam">all_lines</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t676" href="#t676">676</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t677" href="#t677">677</a></span><span class="t">                    <span class="key">for</span> <span class="nam">line</span> <span class="key">in</span> <span class="nam">recent_lines</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t678" href="#t678">678</a></span><span class="t">                        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="nam">line</span><span class="op">.</span><span class="nam">rstrip</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t679" href="#t679">679</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t680" href="#t680">680</a></span><span class="t">            <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t681" href="#t681">681</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]Error reading {name} log: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t682" href="#t682">682</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t683" href="#t683">683</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t684" href="#t684">684</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t685" href="#t685">685</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t686" href="#t686">686</a></span><span class="t"><span class="op">@</span><span class="nam">logs</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t687" href="#t687">687</a></span><span class="t"><span class="key">def</span> <span class="nam">clear</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t688" href="#t688">688</a></span><span class="t">    <span class="str">"""Clear all log files"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t689" href="#t689">689</a></span><span class="t">    <span class="key">from</span> <span class="op">.</span><span class="nam">logging_config</span> <span class="key">import</span> <span class="nam">get_logging_manager</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t690" href="#t690">690</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t691" href="#t691">691</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">click</span><span class="op">.</span><span class="nam">confirm</span><span class="op">(</span><span class="str">"Are you sure you want to clear all log files?"</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t692" href="#t692">692</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"Operation cancelled"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t693" href="#t693">693</a></span><span class="t">        <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t694" href="#t694">694</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t695" href="#t695">695</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t696" href="#t696">696</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">get_logging_manager</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t697" href="#t697">697</a></span><span class="t">        <span class="nam">log_files</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">get_log_files</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t698" href="#t698">698</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t699" href="#t699">699</a></span><span class="t">        <span class="nam">cleared_count</span> <span class="op">=</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t700" href="#t700">700</a></span><span class="t">        <span class="key">for</span> <span class="nam">name</span><span class="op">,</span> <span class="nam">path</span> <span class="key">in</span> <span class="nam">log_files</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t701" href="#t701">701</a></span><span class="t">            <span class="key">if</span> <span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t702" href="#t702">702</a></span><span class="t">                <span class="nam">path</span><span class="op">.</span><span class="nam">unlink</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t703" href="#t703">703</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; Cleared {name} log"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t704" href="#t704">704</a></span><span class="t">                <span class="nam">cleared_count</span> <span class="op">+=</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t705" href="#t705">705</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t706" href="#t706">706</a></span><span class="t">        <span class="key">if</span> <span class="nam">cleared_count</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t707" href="#t707">707</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#128465;&#65039;  Cleared {cleared_count} log files"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t708" href="#t708">708</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t709" href="#t709">709</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"No log files to clear"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t710" href="#t710">710</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t711" href="#t711">711</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t712" href="#t712">712</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t713" href="#t713">713</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t714" href="#t714">714</a></span><span class="t"><span class="com"># Testing commands</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t715" href="#t715">715</a></span><span class="t"><span class="op">@</span><span class="nam">cli</span><span class="op">.</span><span class="nam">group</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t716" href="#t716">716</a></span><span class="t"><span class="key">def</span> <span class="nam">test</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t717" href="#t717">717</a></span><span class="t">    <span class="str">"""Testing and quality assurance commands"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t718" href="#t718">718</a></span><span class="t">    <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t719" href="#t719">719</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t720" href="#t720">720</a></span><span class="t"><span class="op">@</span><span class="nam">test</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t721" href="#t721">721</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--type'</span><span class="op">,</span> <span class="str">'test_type'</span><span class="op">,</span> <span class="nam">type</span><span class="op">=</span><span class="nam">click</span><span class="op">.</span><span class="nam">Choice</span><span class="op">(</span><span class="op">[</span><span class="str">'unit'</span><span class="op">,</span> <span class="str">'integration'</span><span class="op">,</span> <span class="str">'e2e'</span><span class="op">,</span> <span class="str">'all'</span><span class="op">]</span><span class="op">)</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'all'</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Type of tests to run'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t722" href="#t722">722</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--coverage/--no-coverage'</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Generate coverage report'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t723" href="#t723">723</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--verbose'</span><span class="op">,</span> <span class="str">'-v'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Verbose output'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t724" href="#t724">724</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--parallel'</span><span class="op">,</span> <span class="str">'-p'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Run tests in parallel'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t725" href="#t725">725</a></span><span class="t"><span class="key">def</span> <span class="nam">run</span><span class="op">(</span><span class="nam">test_type</span><span class="op">,</span> <span class="nam">coverage</span><span class="op">,</span> <span class="nam">verbose</span><span class="op">,</span> <span class="nam">parallel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t726" href="#t726">726</a></span><span class="t">    <span class="str">"""Run the test suite"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t727" href="#t727">727</a></span><span class="t">    <span class="key">import</span> <span class="nam">subprocess</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t728" href="#t728">728</a></span><span class="t">    <span class="key">import</span> <span class="nam">sys</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t729" href="#t729">729</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t730" href="#t730">730</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#129514; Running {test_type} tests..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t731" href="#t731">731</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t732" href="#t732">732</a></span><span class="t">    <span class="com"># Build pytest command</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t733" href="#t733">733</a></span><span class="t">    <span class="nam">cmd</span> <span class="op">=</span> <span class="op">[</span><span class="str">"python"</span><span class="op">,</span> <span class="str">"-m"</span><span class="op">,</span> <span class="str">"pytest"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t734" href="#t734">734</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t735" href="#t735">735</a></span><span class="t">    <span class="com"># Add test type filter</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t736" href="#t736">736</a></span><span class="t">    <span class="key">if</span> <span class="nam">test_type</span> <span class="op">!=</span> <span class="str">'all'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t737" href="#t737">737</a></span><span class="t">        <span class="nam">cmd</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span><span class="str">"-m"</span><span class="op">,</span> <span class="nam">test_type</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t738" href="#t738">738</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t739" href="#t739">739</a></span><span class="t">    <span class="com"># Add coverage</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t740" href="#t740">740</a></span><span class="t">    <span class="key">if</span> <span class="nam">coverage</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t741" href="#t741">741</a></span><span class="t">        <span class="nam">cmd</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span><span class="str">"--cov=semantic_fs"</span><span class="op">,</span> <span class="str">"--cov-report=term-missing"</span><span class="op">,</span> <span class="str">"--cov-report=html"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t742" href="#t742">742</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t743" href="#t743">743</a></span><span class="t">    <span class="com"># Add verbosity</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t744" href="#t744">744</a></span><span class="t">    <span class="key">if</span> <span class="nam">verbose</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t745" href="#t745">745</a></span><span class="t">        <span class="nam">cmd</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"-v"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t746" href="#t746">746</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t747" href="#t747">747</a></span><span class="t">    <span class="com"># Add parallel execution</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t748" href="#t748">748</a></span><span class="t">    <span class="key">if</span> <span class="nam">parallel</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t749" href="#t749">749</a></span><span class="t">        <span class="nam">cmd</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span><span class="str">"-n"</span><span class="op">,</span> <span class="str">"auto"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t750" href="#t750">750</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t751" href="#t751">751</a></span><span class="t">    <span class="com"># Add test directory</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t752" href="#t752">752</a></span><span class="t">    <span class="nam">cmd</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"tests/"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t753" href="#t753">753</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t754" href="#t754">754</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t755" href="#t755">755</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"Running: {' '.join(cmd)}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t756" href="#t756">756</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">cmd</span><span class="op">,</span> <span class="nam">cwd</span><span class="op">=</span><span class="str">"."</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t757" href="#t757">757</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t758" href="#t758">758</a></span><span class="t">        <span class="key">if</span> <span class="nam">result</span><span class="op">.</span><span class="nam">returncode</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t759" href="#t759">759</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; All tests passed!"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t760" href="#t760">760</a></span><span class="t">            <span class="key">if</span> <span class="nam">coverage</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t761" href="#t761">761</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128202; Coverage report generated in htmlcov/"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t762" href="#t762">762</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t763" href="#t763">763</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#10060; Tests failed with exit code {result.returncode}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t764" href="#t764">764</a></span><span class="t">            <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="nam">result</span><span class="op">.</span><span class="nam">returncode</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t765" href="#t765">765</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t766" href="#t766">766</a></span><span class="t">    <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t767" href="#t767">767</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; pytest not found. Install test dependencies:[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t768" href="#t768">768</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"pip install pytest pytest-cov pytest-asyncio"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t769" href="#t769">769</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t770" href="#t770">770</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error running tests: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t771" href="#t771">771</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t772" href="#t772">772</a></span><span class="t"><span class="op">@</span><span class="nam">test</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t773" href="#t773">773</a></span><span class="t"><span class="key">def</span> <span class="nam">install</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t774" href="#t774">774</a></span><span class="t">    <span class="str">"""Install test dependencies"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t775" href="#t775">775</a></span><span class="t">    <span class="key">import</span> <span class="nam">subprocess</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t776" href="#t776">776</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t777" href="#t777">777</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128230; Installing test dependencies..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t778" href="#t778">778</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t779" href="#t779">779</a></span><span class="t">    <span class="nam">test_deps</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t780" href="#t780">780</a></span><span class="t">        <span class="str">"pytest>=7.4.3"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t781" href="#t781">781</a></span><span class="t">        <span class="str">"pytest-asyncio>=0.21.1"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t782" href="#t782">782</a></span><span class="t">        <span class="str">"pytest-cov>=4.1.0"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t783" href="#t783">783</a></span><span class="t">        <span class="str">"pytest-timeout>=2.2.0"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t784" href="#t784">784</a></span><span class="t">        <span class="str">"pytest-mock>=3.12.0"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t785" href="#t785">785</a></span><span class="t">        <span class="str">"pytest-xdist>=3.5.0"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t786" href="#t786">786</a></span><span class="t">        <span class="str">"coverage>=7.3.2"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t787" href="#t787">787</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t788" href="#t788">788</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t789" href="#t789">789</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t790" href="#t790">790</a></span><span class="t">        <span class="nam">cmd</span> <span class="op">=</span> <span class="op">[</span><span class="str">"pip"</span><span class="op">,</span> <span class="str">"install"</span><span class="op">]</span> <span class="op">+</span> <span class="nam">test_deps</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t791" href="#t791">791</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">cmd</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t792" href="#t792">792</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t793" href="#t793">793</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Test dependencies installed successfully!"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t794" href="#t794">794</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t795" href="#t795">795</a></span><span class="t">    <span class="key">except</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">CalledProcessError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t796" href="#t796">796</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Failed to install dependencies: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t797" href="#t797">797</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t798" href="#t798">798</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t799" href="#t799">799</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t800" href="#t800">800</a></span><span class="t"><span class="op">@</span><span class="nam">test</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t801" href="#t801">801</a></span><span class="t"><span class="key">def</span> <span class="nam">coverage</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t802" href="#t802">802</a></span><span class="t">    <span class="str">"""Generate and display coverage report"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t803" href="#t803">803</a></span><span class="t">    <span class="key">import</span> <span class="nam">subprocess</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t804" href="#t804">804</a></span><span class="t">    <span class="key">from</span> <span class="nam">pathlib</span> <span class="key">import</span> <span class="nam">Path</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t805" href="#t805">805</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t806" href="#t806">806</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128202; Generating coverage report..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t807" href="#t807">807</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t808" href="#t808">808</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t809" href="#t809">809</a></span><span class="t">        <span class="com"># Run tests with coverage</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t810" href="#t810">810</a></span><span class="t">        <span class="nam">cmd</span> <span class="op">=</span> <span class="op">[</span><span class="str">"python"</span><span class="op">,</span> <span class="str">"-m"</span><span class="op">,</span> <span class="str">"pytest"</span><span class="op">,</span> <span class="str">"--cov=semantic_fs"</span><span class="op">,</span> <span class="str">"--cov-report=html"</span><span class="op">,</span> <span class="str">"--cov-report=term"</span><span class="op">,</span> <span class="str">"tests/"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t811" href="#t811">811</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">cmd</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t812" href="#t812">812</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t813" href="#t813">813</a></span><span class="t">        <span class="com"># Check if HTML report was generated</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t814" href="#t814">814</a></span><span class="t">        <span class="nam">html_report</span> <span class="op">=</span> <span class="nam">Path</span><span class="op">(</span><span class="str">"htmlcov/index.html"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t815" href="#t815">815</a></span><span class="t">        <span class="key">if</span> <span class="nam">html_report</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t816" href="#t816">816</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"&#9989; HTML coverage report generated: {html_report.absolute()}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t817" href="#t817">817</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t818" href="#t818">818</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Coverage report generated!"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t819" href="#t819">819</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t820" href="#t820">820</a></span><span class="t">    <span class="key">except</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">CalledProcessError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t821" href="#t821">821</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Failed to generate coverage report: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t822" href="#t822">822</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t823" href="#t823">823</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t824" href="#t824">824</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t825" href="#t825">825</a></span><span class="t"><span class="op">@</span><span class="nam">test</span><span class="op">.</span><span class="nam">command</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t826" href="#t826">826</a></span><span class="t"><span class="op">@</span><span class="nam">click</span><span class="op">.</span><span class="nam">option</span><span class="op">(</span><span class="str">'--fix'</span><span class="op">,</span> <span class="nam">is_flag</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help</span><span class="op">=</span><span class="str">'Automatically fix issues'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t827" href="#t827">827</a></span><span class="t"><span class="key">def</span> <span class="nam">lint</span><span class="op">(</span><span class="nam">fix</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t828" href="#t828">828</a></span><span class="t">    <span class="str">"""Run code quality checks"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t829" href="#t829">829</a></span><span class="t">    <span class="key">import</span> <span class="nam">subprocess</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t830" href="#t830">830</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t831" href="#t831">831</a></span><span class="t">    <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128269; Running code quality checks..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t832" href="#t832">832</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t833" href="#t833">833</a></span><span class="t">    <span class="com"># Check if tools are available</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t834" href="#t834">834</a></span><span class="t">    <span class="nam">tools</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t835" href="#t835">835</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t836" href="#t836">836</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t837" href="#t837">837</a></span><span class="t">        <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="op">[</span><span class="str">"black"</span><span class="op">,</span> <span class="str">"--version"</span><span class="op">]</span><span class="op">,</span> <span class="nam">capture_output</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t838" href="#t838">838</a></span><span class="t">        <span class="nam">tools</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"black"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t839" href="#t839">839</a></span><span class="t">    <span class="key">except</span> <span class="op">(</span><span class="nam">subprocess</span><span class="op">.</span><span class="nam">CalledProcessError</span><span class="op">,</span> <span class="nam">FileNotFoundError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t840" href="#t840">840</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[yellow]&#9888;&#65039;  black not installed[/yellow]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t841" href="#t841">841</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t842" href="#t842">842</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t843" href="#t843">843</a></span><span class="t">        <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="op">[</span><span class="str">"flake8"</span><span class="op">,</span> <span class="str">"--version"</span><span class="op">]</span><span class="op">,</span> <span class="nam">capture_output</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">True</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t844" href="#t844">844</a></span><span class="t">        <span class="nam">tools</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"flake8"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t845" href="#t845">845</a></span><span class="t">    <span class="key">except</span> <span class="op">(</span><span class="nam">subprocess</span><span class="op">.</span><span class="nam">CalledProcessError</span><span class="op">,</span> <span class="nam">FileNotFoundError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t846" href="#t846">846</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[yellow]&#9888;&#65039;  flake8 not installed[/yellow]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t847" href="#t847">847</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t848" href="#t848">848</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">tools</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t849" href="#t849">849</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"[red]&#10060; No linting tools available. Install with:[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t850" href="#t850">850</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"pip install black flake8"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t851" href="#t851">851</a></span><span class="t">        <span class="key">return</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t852" href="#t852">852</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t853" href="#t853">853</a></span><span class="t">    <span class="com"># Run black</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t854" href="#t854">854</a></span><span class="t">    <span class="key">if</span> <span class="str">"black"</span> <span class="key">in</span> <span class="nam">tools</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t855" href="#t855">855</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#127912; Running black..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t856" href="#t856">856</a></span><span class="t">        <span class="nam">cmd</span> <span class="op">=</span> <span class="op">[</span><span class="str">"black"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t857" href="#t857">857</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">fix</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t858" href="#t858">858</a></span><span class="t">            <span class="nam">cmd</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">"--check"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t859" href="#t859">859</a></span><span class="t">        <span class="nam">cmd</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span><span class="str">"semantic_fs/"</span><span class="op">,</span> <span class="str">"tests/"</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t860" href="#t860">860</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t861" href="#t861">861</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t862" href="#t862">862</a></span><span class="t">            <span class="nam">result</span> <span class="op">=</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">cmd</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t863" href="#t863">863</a></span><span class="t">            <span class="key">if</span> <span class="nam">result</span><span class="op">.</span><span class="nam">returncode</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t864" href="#t864">864</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Black formatting check passed"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t865" href="#t865">865</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t866" href="#t866">866</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#10060; Black formatting issues found"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t867" href="#t867">867</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t868" href="#t868">868</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error running black: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t869" href="#t869">869</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t870" href="#t870">870</a></span><span class="t">    <span class="com"># Run flake8</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t871" href="#t871">871</a></span><span class="t">    <span class="key">if</span> <span class="str">"flake8"</span> <span class="key">in</span> <span class="nam">tools</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t872" href="#t872">872</a></span><span class="t">        <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#128269; Running flake8..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t873" href="#t873">873</a></span><span class="t">        <span class="nam">cmd</span> <span class="op">=</span> <span class="op">[</span><span class="str">"flake8"</span><span class="op">,</span> <span class="str">"semantic_fs/"</span><span class="op">,</span> <span class="str">"tests/"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t874" href="#t874">874</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t875" href="#t875">875</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t876" href="#t876">876</a></span><span class="t">            <span class="nam">result</span> <span class="op">=</span> <span class="nam">subprocess</span><span class="op">.</span><span class="nam">run</span><span class="op">(</span><span class="nam">cmd</span><span class="op">,</span> <span class="nam">check</span><span class="op">=</span><span class="key">False</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t877" href="#t877">877</a></span><span class="t">            <span class="key">if</span> <span class="nam">result</span><span class="op">.</span><span class="nam">returncode</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t878" href="#t878">878</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; Flake8 checks passed"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t879" href="#t879">879</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t880" href="#t880">880</a></span><span class="t">                <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">"&#10060; Flake8 issues found"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t881" href="#t881">881</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t882" href="#t882">882</a></span><span class="t">            <span class="nam">console</span><span class="op">.</span><span class="nam">print</span><span class="op">(</span><span class="str">f"[red]&#10060; Error running flake8: {e}[/red]"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t883" href="#t883">883</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t884" href="#t884">884</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">'__main__'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t885" href="#t885">885</a></span><span class="t">    <span class="nam">cli</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_48e1513074be697d_cache_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_48e1513074be697d_config_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
</footer>
</body>
</html>
