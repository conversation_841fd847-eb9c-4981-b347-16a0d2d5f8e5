"""
Database setup and session management with migration support and resource management
"""

import logging
import atexit
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, StaticPool
from contextlib import contextmanager
from typing import Generator

from .config import settings
from .models import Base

logger = logging.getLogger(__name__)

# Database engine with proper connection pooling
def create_database_engine():
    """Create database engine with proper configuration"""
    if "sqlite" in settings.database_url:
        # SQLite configuration
        engine = create_engine(
            settings.database_url,
            connect_args={
                "check_same_thread": False,
                "timeout": 20
            },
            poolclass=StaticPool,
            pool_pre_ping=True,
            echo=False
        )
    else:
        # PostgreSQL, MySQL, etc. configuration
        engine = create_engine(
            settings.database_url,
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections after 1 hour
            echo=False
        )

    # Register cleanup
    atexit.register(cleanup_database_engine, engine)

    return engine

def cleanup_database_engine(engine):
    """Cleanup database engine and connections"""
    try:
        engine.dispose()
        logger.info("Database engine disposed")
    except Exception as e:
        logger.warning(f"Error disposing database engine: {e}")

# Create database engine
engine = create_database_engine()

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Create all database tables (legacy method - use migrations instead)"""
    logger.warning("create_tables() is deprecated - use migration system instead")
    Base.metadata.create_all(bind=engine)

def initialize_database():
    """Initialize database with proper migration support"""
    try:
        # Import here to avoid circular imports
        from .migrations import initialize_database as init_db
        return init_db()
    except ImportError as e:
        logger.warning(f"Migration system not available: {e}")
        logger.info("Falling back to direct table creation")
        create_tables()
        return True

@contextmanager
def get_db() -> Generator[Session, None, None]:
    """Get database session with automatic cleanup and resource management"""
    db = SessionLocal()
    try:
        # Register session for resource tracking
        from .resource_manager import get_resource_manager
        manager = get_resource_manager()
        manager.register_managed_object(db)

        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"Database transaction failed: {e}")
        raise
    finally:
        try:
            db.close()
        except Exception as e:
            logger.warning(f"Error closing database session: {e}")

def get_db_session() -> Session:
    """Get database session for dependency injection"""
    session = SessionLocal()

    # Register for resource management
    try:
        from .resource_manager import get_resource_manager
        manager = get_resource_manager()
        manager.register_managed_object(session)
    except ImportError:
        pass  # Resource manager not available

    return session

def get_connection_stats() -> dict:
    """Get database connection pool statistics"""
    try:
        pool = engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    except Exception as e:
        logger.warning(f"Error getting connection stats: {e}")
        return {}

def cleanup_database_connections():
    """Cleanup database connections and reset pool"""
    try:
        # Dispose of all connections
        engine.dispose()
        logger.info("Database connections cleaned up")
    except Exception as e:
        logger.error(f"Error cleaning up database connections: {e}")

# Register cleanup handler
try:
    from .resource_manager import register_cleanup_handler
    register_cleanup_handler(cleanup_database_connections)
except ImportError:
    pass  # Resource manager not available yet
