"""
Unit tests for the security system
"""

import pytest
import jwt
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from semantic_fs.security import (
    SecurityManager, RateLimiter, SecuritySettings,
    get_current_user, security_manager, rate_limiter, User
)


@pytest.mark.unit
@pytest.mark.security
class TestSecurityManager:
    """Test the SecurityManager class"""
    
    def test_create_access_token(self):
        """Test JWT access token creation"""
        manager = SecurityManager()
        data = {"sub": "test_user", "permissions": ["read", "write"]}
        
        token = manager.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token can be decoded
        decoded = manager.verify_token(token)
        assert decoded is not None
        assert decoded["sub"] == "test_user"
        assert decoded["permissions"] == ["read", "write"]
        assert decoded["type"] == "access"
    
    def test_create_refresh_token(self):
        """Test JWT refresh token creation"""
        manager = SecurityManager()
        data = {"sub": "test_user", "permissions": ["read"]}
        
        token = manager.create_refresh_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token can be decoded
        decoded = manager.verify_token(token)
        assert decoded is not None
        assert decoded["sub"] == "test_user"
        assert decoded["type"] == "refresh"
    
    def test_verify_valid_token(self):
        """Test verifying a valid token"""
        manager = SecurityManager()
        data = {"sub": "test_user", "permissions": ["read"]}
        token = manager.create_access_token(data)
        
        result = manager.verify_token(token)
        
        assert result is not None
        assert result["sub"] == "test_user"
        assert result["permissions"] == ["read"]
    
    def test_verify_invalid_token(self):
        """Test verifying an invalid token"""
        manager = SecurityManager()
        
        result = manager.verify_token("invalid.token.here")
        
        assert result is None
    
    def test_verify_expired_token(self):
        """Test verifying an expired token"""
        manager = SecurityManager()
        
        # Create a token that's already expired
        expired_data = {
            "sub": "test_user",
            "exp": datetime.utcnow() - timedelta(minutes=1)
        }
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.jwt_secret_key = "test_secret"
            mock_settings.jwt_algorithm = "HS256"
            
            expired_token = jwt.encode(
                expired_data,
                "test_secret",
                algorithm="HS256"
            )
            
            result = manager.verify_token(expired_token)
            assert result is None
    
    def test_verify_api_key_valid(self):
        """Test verifying a valid API key"""
        manager = SecurityManager()
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.api_keys = {
                "test_key": {
                    "name": "test_user",
                    "permissions": ["read", "write"],
                    "created_at": datetime.utcnow()
                }
            }
            
            result = manager.verify_api_key("test_key")
            
            assert result is not None
            assert result["name"] == "test_user"
            assert result["permissions"] == ["read", "write"]
    
    def test_verify_api_key_invalid(self):
        """Test verifying an invalid API key"""
        manager = SecurityManager()
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.api_keys = {}
            
            result = manager.verify_api_key("invalid_key")
            
            assert result is None
    
    def test_get_client_id_with_api_key(self):
        """Test getting client ID with API key"""
        manager = SecurityManager()
        
        mock_request = Mock()
        mock_request.headers.get.return_value = "test_api_key"
        
        client_id = manager.get_client_id(mock_request)
        
        assert client_id.startswith("api_key:")
        assert len(client_id) > 10
    
    def test_get_client_id_with_ip(self):
        """Test getting client ID with IP address"""
        manager = SecurityManager()
        
        mock_request = Mock()
        mock_request.headers.get.return_value = None
        mock_request.client.host = "***********"
        
        client_id = manager.get_client_id(mock_request)
        
        assert client_id == "ip:***********"


@pytest.mark.unit
@pytest.mark.security
class TestRateLimiter:
    """Test the RateLimiter class"""
    
    def test_rate_limiter_allows_requests(self):
        """Test that rate limiter allows requests under limit"""
        limiter = RateLimiter(max_requests=5, window_seconds=60)
        
        # Should allow first 5 requests
        for i in range(5):
            assert limiter.is_allowed("test_client") is True
    
    def test_rate_limiter_blocks_excess_requests(self):
        """Test that rate limiter blocks requests over limit"""
        limiter = RateLimiter(max_requests=3, window_seconds=60)
        
        # Allow first 3 requests
        for i in range(3):
            assert limiter.is_allowed("test_client") is True
        
        # Block 4th request
        assert limiter.is_allowed("test_client") is False
    
    def test_rate_limiter_different_clients(self):
        """Test that rate limiter tracks different clients separately"""
        limiter = RateLimiter(max_requests=2, window_seconds=60)
        
        # Client 1 makes 2 requests
        assert limiter.is_allowed("client1") is True
        assert limiter.is_allowed("client1") is True
        assert limiter.is_allowed("client1") is False
        
        # Client 2 should still be allowed
        assert limiter.is_allowed("client2") is True
        assert limiter.is_allowed("client2") is True
        assert limiter.is_allowed("client2") is False
    
    def test_rate_limiter_window_reset(self):
        """Test that rate limiter resets after window expires"""
        limiter = RateLimiter(max_requests=2, window_seconds=1)
        
        # Make 2 requests
        assert limiter.is_allowed("test_client") is True
        assert limiter.is_allowed("test_client") is True
        assert limiter.is_allowed("test_client") is False
        
        # Wait for window to expire
        time.sleep(1.1)
        
        # Should be allowed again
        assert limiter.is_allowed("test_client") is True
    
    def test_rate_limiter_stats(self):
        """Test rate limiter statistics"""
        limiter = RateLimiter(max_requests=5, window_seconds=60)
        
        # Make some requests
        limiter.is_allowed("test_client")
        limiter.is_allowed("test_client")
        
        stats = limiter.get_stats("test_client")
        
        assert stats["requests_made"] == 2
        assert stats["requests_remaining"] == 3
        assert stats["window_seconds"] == 60
        assert isinstance(stats["reset_time"], float)


@pytest.mark.unit
@pytest.mark.security
class TestSecuritySettings:
    """Test the SecuritySettings class"""
    
    def test_default_settings(self):
        """Test default security settings"""
        with patch.dict('os.environ', {}, clear=True):
            settings = SecuritySettings()
            
            assert settings.auth_enabled is False
            assert settings.rate_limit_enabled is True
            assert settings.security_headers_enabled is True
            assert settings.rate_limit_requests == 100
            assert settings.rate_limit_window == 60
    
    def test_environment_override(self):
        """Test that environment variables override defaults"""
        env_vars = {
            "AUTH_ENABLED": "true",
            "RATE_LIMIT_ENABLED": "false",
            "RATE_LIMIT_REQUESTS": "50",
            "RATE_LIMIT_WINDOW": "30"
        }
        
        with patch.dict('os.environ', env_vars):
            settings = SecuritySettings()
            
            assert settings.auth_enabled is True
            assert settings.rate_limit_enabled is False
            assert settings.rate_limit_requests == 50
            assert settings.rate_limit_window == 30
    
    def test_api_keys_loading(self):
        """Test loading API keys from environment"""
        env_vars = {
            "API_KEYS": "key1:user1,key2:user2"
        }
        
        with patch.dict('os.environ', env_vars):
            settings = SecuritySettings()
            
            assert "key1" in settings.api_keys
            assert "key2" in settings.api_keys
            assert settings.api_keys["key1"]["name"] == "user1"
            assert settings.api_keys["key2"]["name"] == "user2"
    
    def test_secret_key_generation(self):
        """Test automatic secret key generation"""
        with patch.dict('os.environ', {}, clear=True):
            settings = SecuritySettings()
            
            assert len(settings.jwt_secret_key) > 0
            assert isinstance(settings.jwt_secret_key, str)


@pytest.mark.unit
@pytest.mark.security
class TestAuthenticationHelpers:
    """Test authentication helper functions"""
    
    @pytest.mark.asyncio
    async def test_get_current_user_no_auth(self):
        """Test getting current user when auth is disabled"""
        mock_request = Mock()
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = False
            
            user = await get_current_user(mock_request, None)
            
            assert user is not None
            assert user.username == "anonymous"
            assert "read" in user.permissions
            assert "write" in user.permissions
    
    @pytest.mark.asyncio
    async def test_get_current_user_with_api_key(self):
        """Test getting current user with valid API key"""
        mock_request = Mock()
        mock_request.headers.get.return_value = "valid_key"
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = True
            
        with patch('semantic_fs.security.security_manager') as mock_manager:
            mock_manager.verify_api_key.return_value = {
                "name": "api_user",
                "permissions": ["read"],
                "created_at": datetime.utcnow()
            }
            
            user = await get_current_user(mock_request, None)
            
            assert user is not None
            assert user.username == "api_user"
            assert user.permissions == ["read"]
    
    @pytest.mark.asyncio
    async def test_get_current_user_with_jwt(self):
        """Test getting current user with valid JWT token"""
        mock_request = Mock()
        mock_request.headers.get.return_value = None
        
        mock_credentials = Mock()
        mock_credentials.credentials = "valid_jwt_token"
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = True
            
        with patch('semantic_fs.security.security_manager') as mock_manager:
            mock_manager.verify_api_key.return_value = None
            mock_manager.verify_token.return_value = {
                "sub": "jwt_user",
                "permissions": ["read", "write"]
            }
            
            user = await get_current_user(mock_request, mock_credentials)
            
            assert user is not None
            assert user.username == "jwt_user"
            assert user.permissions == ["read", "write"]
    
    @pytest.mark.asyncio
    async def test_get_current_user_no_credentials(self):
        """Test getting current user with no credentials"""
        mock_request = Mock()
        mock_request.headers.get.return_value = None
        
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = True
            
        user = await get_current_user(mock_request, None)
        
        assert user is None
