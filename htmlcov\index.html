<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">22%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d___init___py.html">semantic_fs\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html">semantic_fs\api.py</a></td>
                <td>184</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="43 184">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html">semantic_fs\cache.py</a></td>
                <td>128</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="53 128">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html">semantic_fs\cli.py</a></td>
                <td>566</td>
                <td>566</td>
                <td>0</td>
                <td class="right" data-ratio="0 566">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html">semantic_fs\config.py</a></td>
                <td>110</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="89 110">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html">semantic_fs\content_extractor.py</a></td>
                <td>193</td>
                <td>157</td>
                <td>0</td>
                <td class="right" data-ratio="36 193">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html">semantic_fs\core.py</a></td>
                <td>167</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="30 167">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html">semantic_fs\database.py</a></td>
                <td>34</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="15 34">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html">semantic_fs\embedding_engine.py</a></td>
                <td>62</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="18 62">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html">semantic_fs\exceptions.py</a></td>
                <td>127</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="49 127">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html">semantic_fs\file_monitor.py</a></td>
                <td>224</td>
                <td>181</td>
                <td>0</td>
                <td class="right" data-ratio="43 224">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html">semantic_fs\llm_processor.py</a></td>
                <td>205</td>
                <td>181</td>
                <td>0</td>
                <td class="right" data-ratio="24 205">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html">semantic_fs\logging_config.py</a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html">semantic_fs\migrations.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html">semantic_fs\models.py</a></td>
                <td>61</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="61 61">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html">semantic_fs\security.py</a></td>
                <td>161</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="73 161">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html">semantic_fs\signal_handler.py</a></td>
                <td>111</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="32 111">29%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>2605</td>
                <td>2033</td>
                <td>0</td>
                <td class="right" data-ratio="572 2605">22%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_48e1513074be697d_signal_handler_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_48e1513074be697d___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
