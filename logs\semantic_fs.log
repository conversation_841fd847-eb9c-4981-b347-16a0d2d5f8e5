2025-08-06 17:48:38 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 17:48:55 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 17:48:55 DEBUG    [semantic_fs.logging] setup_logging:270 - Log level: DEBUG
2025-08-06 17:48:55 DEBUG    [semantic_fs.logging] setup_logging:271 - File logging: enabled
2025-08-06 17:48:55 DEBUG    [semantic_fs.logging] setup_logging:272 - JSON logging: disabled
2025-08-06 17:48:55 DEBUG    [semantic_fs.logging] setup_logging:273 - Log rotation: enabled
2025-08-06 17:48:55 INFO     [semantic_fs.core] __init__:48 - Initializing semantic filesystem...
2025-08-06 17:48:55 INFO     [semantic_fs.embedding_engine] __init__:23 - Loading embedding model: all-MiniLM-L6-v2
2025-08-06 17:48:58 INFO     [semantic_fs.embedding_engine] __init__:27 - Initializing ChromaDB at: C:\Users\<USER>\Desktop\Vaultarica\vector_db
2025-08-06 17:48:58 INFO     [semantic_fs.embedding_engine] __init__:38 - Embedding engine initialized successfully
2025-08-06 17:48:58 WARNING  [semantic_fs.llm_processor] __init__:47 - No LLM provider available - using fallback methods
2025-08-06 17:48:59 INFO     [semantic_fs.migrations] initialize_database:191 - Initializing database...
2025-08-06 17:48:59 INFO     [alembic.runtime.migration] __init__:207 - Context impl SQLiteImpl.
2025-08-06 17:48:59 INFO     [alembic.runtime.migration] __init__:210 - Will assume non-transactional DDL.
2025-08-06 17:48:59 INFO     [semantic_fs.migrations] initialize_database:223 - Database migration tracking already initialized
2025-08-06 17:48:59 INFO     [semantic_fs.migrations] initialize_database:225 - Database initialization completed
2025-08-06 17:48:59 INFO     [semantic_fs.core] __init__:59 - Semantic filesystem initialized
2025-08-06 17:49:16 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 17:53:08 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 17:53:09 WARNING  [semantic_fs.security] _load_api_keys:84 - Generated default API key: XnH-EoOjQa2lhoXhJWT_jWrWoH4uHG5A_QwZmI1b3DY
2025-08-06 17:53:09 INFO     [semantic_fs.api] startup_event:118 - Starting Semantic Filesystem API
2025-08-06 17:53:09 INFO     [semantic_fs.api] startup_event:119 - API Host: localhost:8000
2025-08-06 17:53:09 INFO     [semantic_fs.api] startup_event:120 - Database: sqlite:///./semantic_fs.db
2025-08-06 17:53:09 INFO     [semantic_fs.api] startup_event:121 - Vector DB: C:\Users\<USER>\Desktop\Vaultarica\vector_db
2025-08-06 17:53:09 INFO     [semantic_fs.core] __init__:48 - Initializing semantic filesystem...
2025-08-06 17:53:09 INFO     [semantic_fs.embedding_engine] __init__:23 - Loading embedding model: all-MiniLM-L6-v2
2025-08-06 17:53:12 INFO     [semantic_fs.embedding_engine] __init__:27 - Initializing ChromaDB at: C:\Users\<USER>\Desktop\Vaultarica\vector_db
2025-08-06 17:53:12 INFO     [semantic_fs.embedding_engine] __init__:38 - Embedding engine initialized successfully
2025-08-06 17:53:12 WARNING  [semantic_fs.llm_processor] __init__:47 - No LLM provider available - using fallback methods
2025-08-06 17:53:12 INFO     [semantic_fs.migrations] initialize_database:191 - Initializing database...
2025-08-06 17:53:12 INFO     [alembic.runtime.migration] __init__:207 - Context impl SQLiteImpl.
2025-08-06 17:53:12 INFO     [alembic.runtime.migration] __init__:210 - Will assume non-transactional DDL.
2025-08-06 17:53:12 INFO     [semantic_fs.migrations] initialize_database:223 - Database migration tracking already initialized
2025-08-06 17:53:12 INFO     [semantic_fs.migrations] initialize_database:225 - Database initialization completed
2025-08-06 17:53:12 INFO     [semantic_fs.core] __init__:59 - Semantic filesystem initialized
2025-08-06 17:53:12 INFO     [semantic_fs.api] startup_event:125 - Semantic Filesystem API ready
2025-08-06 17:53:12 INFO     [semantic_fs.api] startup_event:128 - Authentication: disabled
2025-08-06 17:53:12 INFO     [semantic_fs.api] startup_event:129 - Rate limiting: enabled
2025-08-06 17:53:12 INFO     [semantic_fs.api] startup_event:130 - Security headers: enabled
2025-08-06 17:54:58 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 17:54:58 WARNING  [semantic_fs.security] _load_api_keys:84 - Generated default API key: zVh3_x9cImS1p7aZW4cPJcBB3cJMLpSg8Ck-09a7kcU
2025-08-06 17:54:58 INFO     [semantic_fs.api] startup_event:118 - Starting Semantic Filesystem API
2025-08-06 17:54:58 INFO     [semantic_fs.api] startup_event:119 - API Host: localhost:8000
2025-08-06 17:54:58 INFO     [semantic_fs.api] startup_event:120 - Database: sqlite:///./semantic_fs.db
2025-08-06 17:54:58 INFO     [semantic_fs.api] startup_event:121 - Vector DB: C:\Users\<USER>\Desktop\Vaultarica\vector_db
2025-08-06 17:54:58 INFO     [semantic_fs.core] __init__:48 - Initializing semantic filesystem...
2025-08-06 17:54:58 INFO     [semantic_fs.embedding_engine] __init__:23 - Loading embedding model: all-MiniLM-L6-v2
2025-08-06 17:55:01 INFO     [semantic_fs.embedding_engine] __init__:27 - Initializing ChromaDB at: C:\Users\<USER>\Desktop\Vaultarica\vector_db
2025-08-06 17:55:01 INFO     [semantic_fs.embedding_engine] __init__:38 - Embedding engine initialized successfully
2025-08-06 17:55:01 WARNING  [semantic_fs.llm_processor] __init__:47 - No LLM provider available - using fallback methods
2025-08-06 17:55:01 INFO     [semantic_fs.migrations] initialize_database:191 - Initializing database...
2025-08-06 17:55:01 INFO     [alembic.runtime.migration] __init__:207 - Context impl SQLiteImpl.
2025-08-06 17:55:01 INFO     [alembic.runtime.migration] __init__:210 - Will assume non-transactional DDL.
2025-08-06 17:55:01 INFO     [semantic_fs.migrations] initialize_database:223 - Database migration tracking already initialized
2025-08-06 17:55:01 INFO     [semantic_fs.migrations] initialize_database:225 - Database initialization completed
2025-08-06 17:55:01 INFO     [semantic_fs.core] __init__:59 - Semantic filesystem initialized
2025-08-06 17:55:01 INFO     [semantic_fs.api] startup_event:125 - Semantic Filesystem API ready
2025-08-06 17:55:01 INFO     [semantic_fs.api] startup_event:128 - Authentication: disabled
2025-08-06 17:55:01 INFO     [semantic_fs.api] startup_event:129 - Rate limiting: enabled
2025-08-06 17:55:01 INFO     [semantic_fs.api] startup_event:130 - Security headers: enabled
2025-08-06 18:02:28 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:08:19 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:08:19 INFO     [semantic_fs.database] cleanup_database_engine:57 - Database engine disposed
2025-08-06 18:08:35 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:08:35 INFO     [semantic_fs.database] cleanup_database_engine:54 - Database engine disposed
2025-08-06 18:12:41 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:12:41 INFO     [semantic_fs.config_manager] reload_config:132 - Reloading configuration...
2025-08-06 18:12:41 ERROR    [semantic_fs.config_manager] reload_config:164 - Error reloading configuration: 72 validation errors for Settings
USE_OLLAMA
  Extra inputs are not permitted [type=extra_forbidden, input_value='false', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_BASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://localhost:11434', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-oss:20b', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OPENAI_API_KEY
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LLM_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-3.5-turbo', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DATABASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='sqlite:///./semantic_fs.db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VECTOR_DB_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='./vector_db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
EMBEDDING_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='all-MiniLM-L6-v2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
AUTO_TAG_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CONTENT_SUMMARY_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
RELATIONSHIP_TRACKING_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_HOST
  Extra inputs are not permitted [type=extra_forbidden, input_value='localhost', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_PORT
  Extra inputs are not permitted [type=extra_forbidden, input_value='8000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ALLUSERSPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
APPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Roaming', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYINSTALL
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData\\chocolatey', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYLASTPATHUPDATE
  Extra inputs are not permitted [type=extra_forbidden, input_value='133645995987675342', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHROME_CRASHPAD_PIPE_NAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\crashpad_5328_MKXLPCIYFOKKVWLC', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMPUTERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMSPEC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\WINDOWS\\system32\\cmd.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DRIVERDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Windows\\System32\\Drivers\\DriverData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\Users\\Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOCALAPPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOGONSERVER
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
NUMBER_OF_PROCESSORS
  Extra inputs are not permitted [type=extra_forbidden, input_value='4', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVECONSUMER
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ORIGINAL_XDG_CURRENT_DESKTOP
  Extra inputs are not permitted [type=extra_forbidden, input_value='undefined', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OS
  Extra inputs are not permitted [type=extra_forbidden, input_value='Windows_NT', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\...ilot-chat\\debugCommand', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATHEXT
  Extra inputs are not permitted [type=extra_forbidden, input_value='.COM;.EXE;.BAT;.CMD;.VBS....WSH;.MSC;.RB;.RBW;.CPL', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_ARCHITECTURE
  Extra inputs are not permitted [type=extra_forbidden, input_value='AMD64', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_IDENTIFIER
  Extra inputs are not permitted [type=extra_forbidden, input_value='Intel64 Family 6 Model 1...tepping 9, GenuineIntel', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_LEVEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='6', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_REVISION
  Extra inputs are not permitted [type=extra_forbidden, input_value='9e09', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PSMODULEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\Doc...\Program Files\\Intel\\', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PUBLIC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TEMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN_ROAMINGPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ZES_ENABLE_SYSMAN
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_PAGER
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM
  Extra inputs are not permitted [type=extra_forbidden, input_value='vscode', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM_VERSION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1.102.3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LANG
  Extra inputs are not permitted [type=extra_forbidden, input_value='en_US.UTF-8', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COLORTERM
  Extra inputs are not permitted [type=extra_forbidden, input_value='truecolor', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_ASKPASS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\git\\dist\\askpass.sh', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_NODE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\App...osoft VS Code\\Code.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_EXTRA_ARGS
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_MAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\dist\\askpass-main.js', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_IPC_HANDLE
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\vscode-git-08700968f9-sock', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PYDEVD_DISABLE_FILE_VALIDATION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...nt-2c3054fe09d561c9.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
BUNDLED_DEBUGPY_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...\bundled\\libs\\debugpy', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_INJECTION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_DUPLICATE_LIB_OK
  Extra inputs are not permitted [type=extra_forbidden, input_value='True', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_INIT_AT_FORK
  Extra inputs are not permitted [type=extra_forbidden, input_value='FALSE', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-08-06 18:12:41 INFO     [semantic_fs.config_manager] reload_config:132 - Reloading configuration...
2025-08-06 18:12:41 ERROR    [semantic_fs.config_manager] reload_config:164 - Error reloading configuration: 72 validation errors for Settings
USE_OLLAMA
  Extra inputs are not permitted [type=extra_forbidden, input_value='false', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_BASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://localhost:11434', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OLLAMA_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-oss:20b', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OPENAI_API_KEY
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LLM_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='gpt-3.5-turbo', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DATABASE_URL
  Extra inputs are not permitted [type=extra_forbidden, input_value='sqlite:///./semantic_fs.db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VECTOR_DB_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='./vector_db', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
EMBEDDING_MODEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='all-MiniLM-L6-v2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
AUTO_TAG_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CONTENT_SUMMARY_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
RELATIONSHIP_TRACKING_ENABLED
  Extra inputs are not permitted [type=extra_forbidden, input_value='true', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_HOST
  Extra inputs are not permitted [type=extra_forbidden, input_value='localhost', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
API_PORT
  Extra inputs are not permitted [type=extra_forbidden, input_value='8000', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ALLUSERSPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
APPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Roaming', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYINSTALL
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData\\chocolatey', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHOCOLATEYLASTPATHUPDATE
  Extra inputs are not permitted [type=extra_forbidden, input_value='133645995987675342', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
CHROME_CRASHPAD_PIPE_NAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\crashpad_5328_MKXLPCIYFOKKVWLC', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMMONPROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files\\Common Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMPUTERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COMSPEC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\WINDOWS\\system32\\cmd.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
DRIVERDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Windows\\System32\\Drivers\\DriverData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
HOMEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\Users\\Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOCALAPPDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LOGONSERVER
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
NUMBER_OF_PROCESSORS
  Extra inputs are not permitted [type=extra_forbidden, input_value='4', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ONEDRIVECONSUMER
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\OneDrive', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ORIGINAL_XDG_CURRENT_DESKTOP
  Extra inputs are not permitted [type=extra_forbidden, input_value='undefined', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
OS
  Extra inputs are not permitted [type=extra_forbidden, input_value='Windows_NT', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)\...ilot-chat\\debugCommand', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PATHEXT
  Extra inputs are not permitted [type=extra_forbidden, input_value='.COM;.EXE;.BAT;.CMD;.VBS....WSH;.MSC;.RB;.RBW;.CPL', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_ARCHITECTURE
  Extra inputs are not permitted [type=extra_forbidden, input_value='AMD64', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_IDENTIFIER
  Extra inputs are not permitted [type=extra_forbidden, input_value='Intel64 Family 6 Model 1...tepping 9, GenuineIntel', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_LEVEL
  Extra inputs are not permitted [type=extra_forbidden, input_value='6', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROCESSOR_REVISION
  Extra inputs are not permitted [type=extra_forbidden, input_value='9e09', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMDATA
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\ProgramData', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMFILES(X86)
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files (x86)', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PROGRAMW6432
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Program Files', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PSMODULEPATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\Doc...\Program Files\\Intel\\', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PUBLIC
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TEMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TMP
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\AppData\\Local\\Temp', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERDOMAIN_ROAMINGPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='DESKTOP-QEHIGG0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERNAME
  Extra inputs are not permitted [type=extra_forbidden, input_value='Thinkpad', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
USERPROFILE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\WINDOWS', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
ZES_ENABLE_SYSMAN
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_PAGER
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM
  Extra inputs are not permitted [type=extra_forbidden, input_value='vscode', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
TERM_PROGRAM_VERSION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1.102.3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
LANG
  Extra inputs are not permitted [type=extra_forbidden, input_value='en_US.UTF-8', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
COLORTERM
  Extra inputs are not permitted [type=extra_forbidden, input_value='truecolor', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
GIT_ASKPASS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\git\\dist\\askpass.sh', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_NODE
  Extra inputs are not permitted [type=extra_forbidden, input_value='C:\\Users\\<USER>\\App...osoft VS Code\\Code.exe', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_EXTRA_ARGS
  Extra inputs are not permitted [type=extra_forbidden, input_value='', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_ASKPASS_MAIN
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\App...\\dist\\askpass-main.js', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_GIT_IPC_HANDLE
  Extra inputs are not permitted [type=extra_forbidden, input_value='\\\\.\\pipe\\vscode-git-08700968f9-sock', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
PYDEVD_DISABLE_FILE_VALIDATION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...nt-2c3054fe09d561c9.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
BUNDLED_DEBUGPY_PATH
  Extra inputs are not permitted [type=extra_forbidden, input_value='c:\\Users\\<USER>\\.vs...\bundled\\libs\\debugpy', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
VSCODE_INJECTION
  Extra inputs are not permitted [type=extra_forbidden, input_value='1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_DUPLICATE_LIB_OK
  Extra inputs are not permitted [type=extra_forbidden, input_value='True', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
KMP_INIT_AT_FORK
  Extra inputs are not permitted [type=extra_forbidden, input_value='FALSE', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-08-06 18:12:41 INFO     [semantic_fs.database] cleanup_database_engine:54 - Database engine disposed
2025-08-06 18:13:25 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:13:25 INFO     [semantic_fs.config_manager] reload_config:132 - Reloading configuration...
2025-08-06 18:13:25 INFO     [semantic_fs.config_manager] reload_config:157 - Configuration reloaded successfully
2025-08-06 18:13:25 INFO     [semantic_fs.config_manager] reload_config:132 - Reloading configuration...
2025-08-06 18:13:25 INFO     [semantic_fs.config_manager] reload_config:157 - Configuration reloaded successfully
2025-08-06 18:13:25 INFO     [semantic_fs.database] cleanup_database_engine:54 - Database engine disposed
2025-08-06 18:14:04 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:14:04 INFO     [semantic_fs.config_manager] reload_config:132 - Reloading configuration...
2025-08-06 18:14:04 INFO     [semantic_fs.config_manager] reload_config:157 - Configuration reloaded successfully
2025-08-06 18:14:04 INFO     [semantic_fs.database] cleanup_database_engine:54 - Database engine disposed
2025-08-06 18:14:21 INFO     [semantic_fs.logging] setup_logging:269 - Logging system initialized successfully
2025-08-06 18:14:21 INFO     [semantic_fs.config_manager] reload_config:132 - Reloading configuration...
2025-08-06 18:14:21 INFO     [semantic_fs.config_manager] reload_config:157 - Configuration reloaded successfully
2025-08-06 18:14:21 INFO     [semantic_fs.database] cleanup_database_engine:54 - Database engine disposed
