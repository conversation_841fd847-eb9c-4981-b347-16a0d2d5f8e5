"""
Comprehensive logging configuration for the semantic filesystem
"""

import os
import sys
import json
import logging
import logging.config
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional, Union
from datetime import datetime
import platform
import traceback

from .config import settings


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging"""
    
    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra
        self.hostname = platform.node()
        self.process_id = os.getpid()
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON"""
        # Base log structure
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "thread_name": record.threadName,
            "process": self.process_id,
            "hostname": self.hostname,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'getMessage', 'exc_info',
                    'exc_text', 'stack_info'
                }:
                    try:
                        # Only include JSON-serializable values
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredConsoleFormatter(logging.Formatter):
    """Colored console formatter for better readability"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors"""
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, '')
        reset_color = self.COLORS['RESET']
        
        # Create colored level name
        colored_level = f"{level_color}{record.levelname:<8}{reset_color}"
        
        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # Format logger name (truncate if too long)
        logger_name = record.name
        if len(logger_name) > 20:
            logger_name = f"...{logger_name[-17:]}"
        
        # Build the formatted message
        formatted = f"{timestamp} {colored_level} [{logger_name:<20}] {record.getMessage()}"
        
        # Add exception information if present
        if record.exc_info:
            formatted += f"\n{self.formatException(record.exc_info)}"
        
        return formatted


class LoggingManager:
    """Manages logging configuration for the semantic filesystem"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        self.config_applied = False
    
    def get_logging_config(
        self,
        log_level: str = "INFO",
        enable_file_logging: bool = True,
        enable_json_logging: bool = False,
        enable_rotation: bool = True,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ) -> Dict[str, Any]:
        """Get comprehensive logging configuration"""
        
        config = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "console": {
                    "()": ColoredConsoleFormatter,
                    "format": "%(asctime)s %(levelname)-8s [%(name)-20s] %(message)s",
                    "datefmt": "%H:%M:%S"
                },
                "file": {
                    "format": "%(asctime)s %(levelname)-8s [%(name)s] %(funcName)s:%(lineno)d - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                },
                "json": {
                    "()": StructuredFormatter,
                    "include_extra": True
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": log_level,
                    "formatter": "console",
                    "stream": "ext://sys.stdout"
                }
            },
            "loggers": {
                "semantic_fs": {
                    "level": log_level,
                    "handlers": ["console"],
                    "propagate": False
                },
                "alembic": {
                    "level": "INFO",
                    "handlers": ["console"],
                    "propagate": False
                },
                "sqlalchemy.engine": {
                    "level": "WARNING",
                    "handlers": ["console"],
                    "propagate": False
                },
                "uvicorn": {
                    "level": "INFO",
                    "handlers": ["console"],
                    "propagate": False
                },
                "watchdog": {
                    "level": "WARNING",
                    "handlers": ["console"],
                    "propagate": False
                }
            },
            "root": {
                "level": "WARNING",
                "handlers": ["console"]
            }
        }
        
        # Add file logging if enabled
        if enable_file_logging:
            handlers = ["console"]
            
            # Main application log
            if enable_rotation:
                config["handlers"]["file"] = {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": log_level,
                    "formatter": "json" if enable_json_logging else "file",
                    "filename": str(self.log_dir / "semantic_fs.log"),
                    "maxBytes": max_file_size,
                    "backupCount": backup_count,
                    "encoding": "utf-8"
                }
            else:
                config["handlers"]["file"] = {
                    "class": "logging.FileHandler",
                    "level": log_level,
                    "formatter": "json" if enable_json_logging else "file",
                    "filename": str(self.log_dir / "semantic_fs.log"),
                    "encoding": "utf-8"
                }
            
            handlers.append("file")
            
            # Error log (errors and above only)
            config["handlers"]["error_file"] = {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "json" if enable_json_logging else "file",
                "filename": str(self.log_dir / "errors.log"),
                "maxBytes": max_file_size,
                "backupCount": backup_count,
                "encoding": "utf-8"
            }
            handlers.append("error_file")
            
            # Update logger handlers
            for logger_name in ["semantic_fs", "alembic", "uvicorn"]:
                config["loggers"][logger_name]["handlers"] = handlers
            
            config["root"]["handlers"] = handlers
        
        return config
    
    def setup_logging(
        self,
        log_level: str = "INFO",
        enable_file_logging: bool = True,
        enable_json_logging: bool = False,
        enable_rotation: bool = True,
        force_reconfigure: bool = False
    ) -> bool:
        """Setup comprehensive logging configuration"""
        
        if self.config_applied and not force_reconfigure:
            return True
        
        try:
            # Get configuration
            config = self.get_logging_config(
                log_level=log_level.upper(),
                enable_file_logging=enable_file_logging,
                enable_json_logging=enable_json_logging,
                enable_rotation=enable_rotation
            )
            
            # Apply configuration
            logging.config.dictConfig(config)
            
            # Test logging
            logger = logging.getLogger("semantic_fs.logging")
            logger.info("Logging system initialized successfully")
            logger.debug(f"Log level: {log_level}")
            logger.debug(f"File logging: {'enabled' if enable_file_logging else 'disabled'}")
            logger.debug(f"JSON logging: {'enabled' if enable_json_logging else 'disabled'}")
            logger.debug(f"Log rotation: {'enabled' if enable_rotation else 'disabled'}")
            
            self.config_applied = True
            return True
            
        except Exception as e:
            print(f"Failed to setup logging: {e}", file=sys.stderr)
            return False
    
    def get_log_files(self) -> Dict[str, Path]:
        """Get paths to log files"""
        return {
            "main": self.log_dir / "semantic_fs.log",
            "errors": self.log_dir / "errors.log"
        }
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Get logging statistics"""
        stats = {
            "log_directory": str(self.log_dir),
            "files": {}
        }
        
        for name, path in self.get_log_files().items():
            if path.exists():
                stat = path.stat()
                stats["files"][name] = {
                    "path": str(path),
                    "size": stat.st_size,
                    "size_mb": round(stat.st_size / (1024 * 1024), 2),
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
            else:
                stats["files"][name] = {
                    "path": str(path),
                    "exists": False
                }
        
        return stats


# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None


def get_logging_manager() -> LoggingManager:
    """Get the global logging manager instance"""
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def setup_logging(
    log_level: str = "INFO",
    enable_file_logging: bool = True,
    enable_json_logging: bool = False,
    enable_rotation: bool = True,
    force_reconfigure: bool = False
) -> bool:
    """Setup comprehensive logging (convenience function)"""
    return get_logging_manager().setup_logging(
        log_level=log_level,
        enable_file_logging=enable_file_logging,
        enable_json_logging=enable_json_logging,
        enable_rotation=enable_rotation,
        force_reconfigure=force_reconfigure
    )


def get_log_stats() -> Dict[str, Any]:
    """Get logging statistics (convenience function)"""
    return get_logging_manager().get_log_stats()
