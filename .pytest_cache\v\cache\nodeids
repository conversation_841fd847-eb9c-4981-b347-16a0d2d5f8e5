["tests/unit/test_core.py::TestErrorHandling::test_content_extraction_error", "tests/unit/test_core.py::TestErrorHandling::test_database_error_handling", "tests/unit/test_core.py::TestErrorHandling::test_embedding_generation_error", "tests/unit/test_core.py::TestSemanticFilesystem::test_file_permissions", "tests/unit/test_core.py::TestSemanticFilesystem::test_index_file_nonexistent", "tests/unit/test_core.py::TestSemanticFilesystem::test_index_file_success", "tests/unit/test_core.py::TestSemanticFilesystem::test_index_file_too_large", "tests/unit/test_core.py::TestSemanticFilesystem::test_initialization", "tests/unit/test_core.py::TestSemanticFilesystem::test_query_empty_string", "tests/unit/test_core.py::TestSemanticFilesystem::test_query_success", "tests/unit/test_core.py::TestSemanticFilesystem::test_query_with_context", "tests/unit/test_core.py::TestSemanticFilesystem::test_singleton_pattern", "tests/unit/test_core.py::TestSemanticFilesystem::test_update_existing_file", "tests/unit/test_core.py::TestSemanticQuery::test_default_values", "tests/unit/test_core.py::TestSemanticQuery::test_query_validation", "tests/unit/test_core.py::TestSemanticQuery::test_query_with_filters", "tests/unit/test_core.py::TestSemanticQuery::test_valid_query"]