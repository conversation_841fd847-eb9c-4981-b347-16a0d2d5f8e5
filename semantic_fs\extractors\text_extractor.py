"""
Text Content Extractor
Handles all text-based files including code, markup, and configuration files
"""

from pathlib import Path
from typing import Optional, Dict, Set
import logging
import json
import csv
from xml.etree import ElementTree as ET

from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class TextExtractor(BaseExtractor):
    """Extractor for text-based files"""
    
    def __init__(self):
        super().__init__()
    
    def get_supported_extensions(self) -> Set[str]:
        """Get all supported text file extensions"""
        return {
            # Documents & Text
            '.txt', '.md', '.markdown', '.mdown', '.mkd', '.mdx', '.rst', '.asciidoc', '.adoc', '.tex', '.latex',
            
            # Programming Languages
            '.py', '.pyw', '.pyi', '.pyx',  # Python
            '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs', '.map',  # JavaScript/TypeScript
            '.html', '.htm', '.xhtml', '.shtml',  # HTML
            '.css', '.scss', '.sass', '.less', '.styl',  # CSS
            '.java', '.class', '.jar',  # Java
            '.cpp', '.cxx', '.cc', '.c', '.h', '.hpp', '.hxx',  # C/C++
            '.cs', '.vb', '.fs',  # .NET languages
            '.php', '.php3', '.php4', '.php5', '.phtml',  # PHP
            '.rb', '.rbw', '.rake', '.gemspec',  # Ruby
            '.go', '.mod', '.sum',  # Go
            '.rs', '.rlib',  # Rust
            '.swift',  # Swift
            '.kt', '.kts',  # Kotlin
            '.scala', '.sc',  # Scala
            '.clj', '.cljs', '.cljc', '.edn',  # Clojure
            '.hs', '.lhs',  # Haskell
            '.ml', '.mli', '.mll', '.mly',  # OCaml
            '.r', '.R', '.rmd', '.rnw',  # R
            '.m', '.mm',  # Objective-C
            '.pl', '.pm', '.t', '.pod',  # Perl
            '.lua',  # Lua
            '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh',  # Shell scripts
            '.ps1', '.psm1', '.psd1',  # PowerShell
            '.bat', '.cmd',  # Windows batch
            '.asm', '.s',  # Assembly
            '.f', '.f90', '.f95', '.f03', '.f08',  # Fortran
            '.pas', '.pp', '.inc',  # Pascal
            '.d',  # D
            '.nim', '.nims',  # Nim
            '.cr',  # Crystal
            '.ex', '.exs',  # Elixir
            '.erl', '.hrl',  # Erlang
            '.dart',  # Dart
            '.v', '.vh', '.sv', '.svh',  # Verilog/SystemVerilog
            '.vhd', '.vhdl',  # VHDL
            
            # Data & Configuration
            '.json', '.jsonl', '.ndjson', '.json5',
            '.xml', '.xsd', '.xsl', '.xslt', '.dtd',
            '.yaml', '.yml',
            '.toml',
            '.ini', '.cfg', '.conf', '.config',
            '.properties',
            '.env', '.envrc',
            '.gitignore', '.gitattributes', '.gitmodules',
            '.dockerignore', '.dockerfile',
            '.makefile', '.mk',
            '.cmake', '.cmakelist',
            '.gradle', '.gradlew',
            '.maven', '.pom',
            '.sbt',
            '.cabal',
            '.cargo',
            '.package', '.lock',
            '.requirements',
            '.pipfile', '.poetry',
            '.gemfile',
            '.composer',
            '.npm', '.yarn',
            
            # Database & Query
            '.sql', '.mysql', '.pgsql', '.sqlite', '.db',
            '.cql', '.cypher',
            '.sparql',
            '.graphql', '.gql',
            
            # Web & API
            '.api', '.rest',
            '.wsdl', '.wadl',
            '.raml', '.swagger',
            '.openapi',
            '.postman',
            
            # Log & Debug files
            '.log', '.logs',
            '.out', '.err',
            '.trace', '.debug',
            '.dump', '.dmp',
            
            # Specialized text formats
            '.man', '.info', '.help', '.1', '.2', '.3', '.4', '.5', '.6', '.7', '.8', '.9',
            '.patch', '.diff', '.rej', '.orig', '.bak', '.swp', '.tmp',
            
            # No extension files (common names)
            '.readme', '.license', '.changelog', '.authors', '.contributors',
            '.copying', '.install', '.news', '.todo', '.fixme',
            '.version', '.release', '.history'
        }
    
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get MIME type mappings for text files"""
        return {
            # Text files
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.markdown': 'text/markdown',
            '.mdown': 'text/markdown',
            '.mkd': 'text/markdown',
            '.mdx': 'text/markdown',
            '.rst': 'text/x-rst',
            '.rest': 'text/x-rst',
            '.asciidoc': 'text/asciidoc',
            '.adoc': 'text/asciidoc',
            '.tex': 'text/x-tex',
            '.latex': 'text/x-tex',
            
            # Programming languages
            '.py': 'text/x-python',
            '.pyw': 'text/x-python',
            '.pyi': 'text/x-python',
            '.js': 'application/javascript',
            '.jsx': 'application/javascript',
            '.ts': 'application/typescript',
            '.tsx': 'application/typescript',
            '.mjs': 'application/javascript',
            '.cjs': 'application/javascript',
            '.map': 'application/json',
            '.html': 'text/html',
            '.htm': 'text/html',
            '.xhtml': 'application/xhtml+xml',
            '.css': 'text/css',
            '.scss': 'text/x-scss',
            '.sass': 'text/x-sass',
            '.less': 'text/x-less',
            '.java': 'text/x-java-source',
            '.cpp': 'text/x-c++src',
            '.cxx': 'text/x-c++src',
            '.cc': 'text/x-c++src',
            '.c': 'text/x-csrc',
            '.h': 'text/x-chdr',
            '.hpp': 'text/x-c++hdr',
            '.cs': 'text/x-csharp',
            '.php': 'application/x-php',
            '.rb': 'application/x-ruby',
            '.go': 'text/x-go',
            '.rs': 'text/x-rust',
            '.swift': 'text/x-swift',
            '.kt': 'text/x-kotlin',
            '.scala': 'text/x-scala',
            '.clj': 'text/x-clojure',
            '.hs': 'text/x-haskell',
            '.ml': 'text/x-ocaml',
            '.r': 'text/x-r',
            '.lua': 'text/x-lua',
            '.sh': 'application/x-sh',
            '.bash': 'application/x-sh',
            '.ps1': 'application/x-powershell',
            '.bat': 'application/x-msdos-program',
            '.sql': 'application/sql',
            
            # Data formats
            '.json': 'application/json',
            '.jsonl': 'application/jsonlines',
            '.json5': 'application/json5',
            '.xml': 'application/xml',
            '.yaml': 'application/x-yaml',
            '.yml': 'application/x-yaml',
            '.toml': 'application/toml',
            '.ini': 'text/plain',
            '.cfg': 'text/plain',
            '.conf': 'text/plain',
            '.config': 'text/plain',
            '.properties': 'text/plain',
            '.env': 'text/plain',
            '.csv': 'text/csv',
            '.tsv': 'text/tab-separated-values',
            '.log': 'text/plain',
            
            # Special files
            '.readme': 'text/plain',
            '.license': 'text/plain',
            '.changelog': 'text/plain',
            '.makefile': 'text/plain',
            '.dockerfile': 'text/plain',
            '.man': 'text/x-troff-man',
            '.patch': 'text/x-patch',
            '.diff': 'text/x-diff',
        }
    
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract content from text files"""
        try:
            extension = file_path.suffix.lower()
            
            if not self.supports_extension(extension):
                return None
            
            # Get MIME type and choose appropriate extraction method
            mime_type = self.get_mime_type(extension)
            
            if mime_type == 'application/json':
                return self._extract_json(file_path)
            elif mime_type == 'text/csv':
                return self._extract_csv(file_path)
            elif mime_type in ['application/xml', 'text/xml']:
                return self._extract_xml(file_path)
            elif mime_type == 'text/html':
                return self._extract_html(file_path)
            else:
                return self._extract_text(file_path)
                
        except Exception as e:
            logger.error(f"Error extracting text content from {file_path}: {e}")
            return None
    
    def _extract_text(self, file_path: Path) -> Optional[str]:
        """Extract content from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Add file context
            file_info = self._get_file_info(file_path)
            context = self._analyze_filename_context(file_path)
            
            content_parts = []
            if context:
                content_parts.append(f"File Context: {context}")
            content_parts.append(f"File Type: {file_info['extension']} ({file_info['size_mb']} MB)")
            content_parts.append(f"Content:\n{content}")
            
            return self._clean_content("\n\n".join(content_parts))
            
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return None
    
    def _extract_json(self, file_path: Path) -> Optional[str]:
        """Extract content from JSON files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convert JSON to readable text
            json_content = json.dumps(data, indent=2, ensure_ascii=False)
            
            file_info = self._get_file_info(file_path)
            context = self._analyze_filename_context(file_path)
            
            content_parts = []
            if context:
                content_parts.append(f"File Context: {context}")
            content_parts.append(f"JSON File: {file_info['name']} ({file_info['size_mb']} MB)")
            content_parts.append(f"Structure: {type(data).__name__} with {len(str(data))} characters")
            content_parts.append(f"Content:\n{json_content}")
            
            return self._clean_content("\n\n".join(content_parts))
            
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            return None
    
    def _extract_csv(self, file_path: Path) -> Optional[str]:
        """Extract content from CSV files"""
        try:
            content_lines = []
            row_count = 0
            
            with open(file_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.reader(f)
                for i, row in enumerate(reader):
                    if i < 100:  # Limit to first 100 rows
                        content_lines.append(' | '.join(row))
                        row_count += 1
                    else:
                        break
            
            file_info = self._get_file_info(file_path)
            context = self._analyze_filename_context(file_path)
            
            content_parts = []
            if context:
                content_parts.append(f"File Context: {context}")
            content_parts.append(f"CSV File: {file_info['name']} ({file_info['size_mb']} MB)")
            content_parts.append(f"Rows shown: {row_count} (first 100)")
            content_parts.append(f"Content:\n" + '\n'.join(content_lines))
            
            return self._clean_content("\n\n".join(content_parts))
            
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return None
    
    def _extract_xml(self, file_path: Path) -> Optional[str]:
        """Extract content from XML files"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Extract all text content
            text_content = []
            for elem in root.iter():
                if elem.text and elem.text.strip():
                    text_content.append(elem.text.strip())
            
            file_info = self._get_file_info(file_path)
            context = self._analyze_filename_context(file_path)
            
            content_parts = []
            if context:
                content_parts.append(f"File Context: {context}")
            content_parts.append(f"XML File: {file_info['name']} ({file_info['size_mb']} MB)")
            content_parts.append(f"Root Element: {root.tag}")
            content_parts.append(f"Text Content:\n" + '\n'.join(text_content))
            
            return self._clean_content("\n\n".join(content_parts))
            
        except Exception as e:
            logger.error(f"Error reading XML file {file_path}: {e}")
            return None
    
    def _extract_html(self, file_path: Path) -> Optional[str]:
        """Extract content from HTML files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Simple HTML tag removal (for basic extraction)
            import re
            # Remove script and style elements
            content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
            content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL | re.IGNORECASE)
            # Remove HTML tags
            content = re.sub(r'<[^>]+>', '', content)
            # Clean up whitespace
            content = re.sub(r'\s+', ' ', content)
            
            file_info = self._get_file_info(file_path)
            context = self._analyze_filename_context(file_path)
            
            content_parts = []
            if context:
                content_parts.append(f"File Context: {context}")
            content_parts.append(f"HTML File: {file_info['name']} ({file_info['size_mb']} MB)")
            content_parts.append(f"Extracted Text:\n{content.strip()}")
            
            return self._clean_content("\n\n".join(content_parts))
            
        except Exception as e:
            logger.error(f"Error reading HTML file {file_path}: {e}")
            return None
