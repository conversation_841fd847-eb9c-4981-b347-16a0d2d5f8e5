#!/usr/bin/env python3
"""
Create a sample DOCX file for testing
"""

from docx import Document
from docx.shared import Inches

# Create a new document
doc = Document()

# Add title
title = doc.add_heading('QUARTERLY BUSINESS REPORT', 0)

# Add subtitle
doc.add_heading('Q1 2024 Performance Analysis', level=1)

# Add content
doc.add_paragraph('Executive Summary:')
doc.add_paragraph(
    'This report provides a comprehensive analysis of our business performance '
    'during the first quarter of 2024. Our company has achieved significant '
    'milestones in revenue growth, customer acquisition, and product development.'
)

doc.add_heading('Key Metrics', level=2)
doc.add_paragraph('• Revenue: $1.2M (25% increase from Q4 2023)')
doc.add_paragraph('• New Customers: 450 (18% growth)')
doc.add_paragraph('• Product Launches: 3 major releases')
doc.add_paragraph('• Employee Count: 85 (15 new hires)')

doc.add_heading('Market Analysis', level=2)
doc.add_paragraph(
    'The market conditions in Q1 2024 have been favorable for our industry. '
    'We have successfully captured additional market share through strategic '
    'partnerships and innovative product offerings. Our competitive position '
    'has strengthened significantly.'
)

doc.add_heading('Future Outlook', level=2)
doc.add_paragraph(
    'Looking ahead to Q2 2024, we anticipate continued growth driven by '
    'our new product pipeline and expanding customer base. We are well-positioned '
    'to achieve our annual targets and maintain our leadership position in the market.'
)

doc.add_paragraph('')
doc.add_paragraph('Report prepared: April 2024')
doc.add_paragraph('Confidential - Internal Use Only')

# Save the document
doc.save('test_documents/quarterly_report.docx')
print("DOCX file created successfully!")
