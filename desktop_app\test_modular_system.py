"""
Test the new modular content extraction system
"""

import sys
import os
from pathlib import Path

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from semantic_fs.content_extractor import ContentExtractor


def test_modular_system():
    """Test the modular content extraction system"""
    print("🧪 TESTING MODULAR CONTENT EXTRACTION SYSTEM")
    print("=" * 60)
    
    try:
        # Initialize the content extractor
        extractor = ContentExtractor()
        print("✅ Content extractor initialized successfully")
        
        # Test extractor info
        print("\n📊 EXTRACTOR INFORMATION:")
        extractor_info = extractor.get_extractor_info()
        for name, info in extractor_info.items():
            print(f"   {name}:")
            print(f"      Extensions: {info['extension_count']}")
            print(f"      MIME types: {info['mime_types']}")
            print(f"      Description: {info['description']}")
        
        # Test extraction stats
        print("\n📈 EXTRACTION STATISTICS:")
        stats = extractor.get_extraction_stats()
        for name, stat in stats.items():
            if isinstance(stat, dict):
                if 'extensions' in stat:
                    print(f"   {name}: {stat['extensions']} extensions, {stat['mime_types']} MIME types")
                else:
                    print(f"   {name}: {stat}")
        
        # Test extractor functionality
        print("\n🔧 EXTRACTOR FUNCTIONALITY TEST:")
        test_results = extractor.test_extractors()
        for name, result in test_results.items():
            status = "✅" if result else "❌"
            print(f"   {status} {name}: {'PASS' if result else 'FAIL'}")
        
        # Test supported extensions
        print("\n📁 SUPPORTED EXTENSIONS:")
        extensions = extractor.get_supported_extensions()
        print(f"   Total supported extensions: {len(extensions)}")
        
        # Group extensions by category
        extension_categories = {
            'Images': [ext for ext in extensions if ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp', '.svg']],
            'Documents': [ext for ext in extensions if ext in ['.pdf', '.docx', '.doc', '.txt', '.md', '.rtf']],
            'Code': [ext for ext in extensions if ext in ['.py', '.js', '.ts', '.html', '.css', '.java', '.cpp', '.c', '.h']],
            'Data': [ext for ext in extensions if ext in ['.json', '.xml', '.csv', '.yaml', '.yml', '.sql']],
            'Archives': [ext for ext in extensions if ext in ['.zip', '.rar', '.7z', '.tar', '.gz']],
            'Binary': [ext for ext in extensions if ext in ['.exe', '.dll', '.so', '.bin', '.dat']],
            'Specialized': [ext for ext in extensions if ext in ['.pkl', '.h5', '.parquet', '.proto', '.flt', '.venc']]
        }
        
        for category, exts in extension_categories.items():
            if exts:
                print(f"   {category}: {len(exts)} extensions")
                print(f"      Sample: {', '.join(sorted(exts)[:10])}")
        
        print("\n🎉 MODULAR SYSTEM TEST COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing modular system: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_extraction():
    """Test actual file extraction if sample files exist"""
    print("\n🔍 TESTING FILE EXTRACTION:")
    print("-" * 40)
    
    try:
        extractor = ContentExtractor()
        
        # Test files to look for
        test_files = [
            "README.md",
            "requirements.txt", 
            "package.json",
            "desktop_app/test_modular_system.py"
        ]
        
        for test_file in test_files:
            file_path = Path(test_file)
            if file_path.exists():
                print(f"\n📄 Testing: {test_file}")
                
                # Check if supported
                is_supported = extractor.supports_file(str(file_path))
                print(f"   Supported: {'✅' if is_supported else '❌'}")
                
                if is_supported:
                    # Extract content
                    content = extractor.extract_content(str(file_path))
                    if content:
                        preview = content[:200] + "..." if len(content) > 200 else content
                        print(f"   Content preview: {preview}")
                    else:
                        print("   No content extracted")
                else:
                    print("   File type not supported")
            else:
                print(f"   📄 {test_file}: File not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing file extraction: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 MODULAR CONTENT EXTRACTION SYSTEM TEST")
    print("=" * 60)
    
    # Test 1: Modular system
    success1 = test_modular_system()
    
    # Test 2: File extraction
    success2 = test_file_extraction()
    
    # Final result
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED! Modular system is working perfectly!")
        print("✅ Ready for production deployment!")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    return success1 and success2


if __name__ == "__main__":
    main()
