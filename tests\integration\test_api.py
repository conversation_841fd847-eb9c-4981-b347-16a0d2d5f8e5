"""
Integration tests for the API endpoints
"""

import pytest
import json
from unittest.mock import patch, Mock
from fastapi.testclient import Test<PERSON>lient

from semantic_fs.api import app
from semantic_fs.models import SemanticQuery


@pytest.mark.integration
@pytest.mark.api
class TestAPIEndpoints:
    """Test API endpoints integration"""
    
    def test_root_endpoint(self, api_client):
        """Test the root endpoint returns HTML"""
        response = api_client.get("/")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        assert "Semantic Filesystem" in response.text
    
    def test_health_endpoint(self, api_client):
        """Test the health check endpoint"""
        response = api_client.get("/api/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_search_endpoint_valid_query(self, api_client):
        """Test search endpoint with valid query"""
        query_data = {
            "query": "test document",
            "limit": 5
        }
        
        with patch('semantic_fs.api.get_semantic_fs') as mock_get_fs:
            mock_fs = Mock()
            mock_result = Mock()
            mock_result.query = "test document"
            mock_result.total_found = 1
            mock_result.results = []
            mock_result.execution_time = 0.1
            mock_result.interpretation = "Looking for test documents"
            
            mock_fs.query.return_value = mock_result
            mock_get_fs.return_value = mock_fs
            
            response = api_client.post("/api/search", json=query_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["query"] == "test document"
            assert data["total_found"] == 1
            assert "execution_time" in data
    
    def test_search_endpoint_empty_query(self, api_client):
        """Test search endpoint with empty query"""
        query_data = {
            "query": "",
            "limit": 5
        }
        
        response = api_client.post("/api/search", json=query_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_search_endpoint_invalid_limit(self, api_client):
        """Test search endpoint with invalid limit"""
        query_data = {
            "query": "test",
            "limit": 101  # Exceeds maximum
        }
        
        response = api_client.post("/api/search", json=query_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_index_file_endpoint(self, api_client, test_files):
        """Test file indexing endpoint"""
        test_file = test_files["test_document.txt"]
        
        with patch('semantic_fs.api.get_semantic_fs') as mock_get_fs:
            mock_fs = Mock()
            mock_fs.index_file.return_value = Mock(id=1)
            mock_get_fs.return_value = mock_fs
            
            response = api_client.post(
                "/api/index",
                json={"file_path": str(test_file)}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "message" in data
    
    def test_index_file_nonexistent(self, api_client):
        """Test indexing non-existent file"""
        response = api_client.post(
            "/api/index",
            json={"file_path": "/nonexistent/file.txt"}
        )
        
        assert response.status_code == 404
    
    def test_files_endpoint(self, api_client):
        """Test files listing endpoint"""
        with patch('semantic_fs.api.get_semantic_fs') as mock_get_fs:
            mock_fs = Mock()
            mock_fs.get_all_files.return_value = []
            mock_get_fs.return_value = mock_fs
            
            response = api_client.get("/api/files")
            
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
    
    def test_file_detail_endpoint(self, api_client):
        """Test file detail endpoint"""
        file_id = 1
        
        with patch('semantic_fs.api.get_semantic_fs') as mock_get_fs:
            mock_fs = Mock()
            mock_file = Mock()
            mock_file.id = file_id
            mock_file.filename = "test.txt"
            mock_file.path = "/test/test.txt"
            mock_file.file_type = "text/plain"
            mock_file.size = 1000
            mock_file.content_summary = "Test file"
            mock_file.auto_tags = ["test"]
            mock_file.user_tags = []
            
            mock_fs.get_file_by_id.return_value = mock_file
            mock_get_fs.return_value = mock_fs
            
            response = api_client.get(f"/api/files/{file_id}")
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == file_id
            assert data["filename"] == "test.txt"
    
    def test_file_detail_not_found(self, api_client):
        """Test file detail endpoint with non-existent file"""
        file_id = 999
        
        with patch('semantic_fs.api.get_semantic_fs') as mock_get_fs:
            mock_fs = Mock()
            mock_fs.get_file_by_id.return_value = None
            mock_get_fs.return_value = mock_fs
            
            response = api_client.get(f"/api/files/{file_id}")
            
            assert response.status_code == 404
    
    def test_security_status_endpoint(self, api_client):
        """Test security status endpoint"""
        response = api_client.get("/api/security/status")
        
        assert response.status_code == 200
        data = response.json()
        assert "auth_enabled" in data
        assert "rate_limit_enabled" in data
        assert "security_headers_enabled" in data
    
    def test_cors_headers(self, api_client):
        """Test CORS headers are present"""
        response = api_client.options("/api/health")
        
        assert response.status_code == 200
        assert "access-control-allow-origin" in response.headers
    
    def test_security_headers(self, api_client):
        """Test security headers are present"""
        response = api_client.get("/api/health")
        
        assert response.status_code == 200
        assert "x-content-type-options" in response.headers
        assert "x-frame-options" in response.headers
        assert "x-xss-protection" in response.headers
    
    def test_rate_limiting_headers(self, api_client):
        """Test rate limiting headers are present"""
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.rate_limit_enabled = True
            mock_settings.rate_limit_requests = 100
            mock_settings.rate_limit_window = 60
            
            response = api_client.get("/api/health")
            
            assert response.status_code == 200
            # Rate limiting headers should be present
            # Note: Actual headers depend on middleware implementation


@pytest.mark.integration
@pytest.mark.api
class TestAPIAuthentication:
    """Test API authentication integration"""
    
    def test_auth_disabled_allows_access(self, api_client):
        """Test that requests are allowed when auth is disabled"""
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = False
            
            response = api_client.get("/api/health")
            
            assert response.status_code == 200
    
    def test_api_key_authentication(self, api_client):
        """Test API key authentication"""
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = True
            mock_settings.api_keys = {
                "test_key": {
                    "name": "test_user",
                    "permissions": ["read", "write"],
                    "created_at": "2023-01-01T00:00:00"
                }
            }
            
            # Request with valid API key
            headers = {"X-API-Key": "test_key"}
            response = api_client.get("/api/health", headers=headers)
            
            assert response.status_code == 200
    
    def test_invalid_api_key(self, api_client):
        """Test invalid API key handling"""
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.auth_enabled = True
            mock_settings.require_auth_for_read = True
            mock_settings.api_keys = {}
            
            # Request with invalid API key
            headers = {"X-API-Key": "invalid_key"}
            response = api_client.post(
                "/api/search",
                json={"query": "test", "limit": 5},
                headers=headers
            )
            
            # Should be unauthorized
            assert response.status_code == 401
    
    def test_jwt_token_creation(self, api_client):
        """Test JWT token creation endpoint"""
        with patch('semantic_fs.security.security_settings') as mock_settings:
            mock_settings.api_keys = {
                "test_key": {
                    "name": "test_user",
                    "permissions": ["read", "write"],
                    "created_at": "2023-01-01T00:00:00"
                }
            }
            
            response = api_client.post(
                "/api/auth/token",
                json={"api_key": "test_key"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data
            assert "refresh_token" in data
            assert data["token_type"] == "bearer"
    
    def test_current_user_endpoint(self, api_client):
        """Test current user information endpoint"""
        with patch('semantic_fs.security.get_current_user') as mock_get_user:
            from semantic_fs.security import User
            from datetime import datetime
            
            mock_user = User(
                username="test_user",
                permissions=["read", "write"],
                created_at=datetime.utcnow()
            )
            mock_get_user.return_value = mock_user
            
            response = api_client.get("/api/auth/me")
            
            assert response.status_code == 200
            data = response.json()
            assert data["username"] == "test_user"
            assert data["permissions"] == ["read", "write"]


@pytest.mark.integration
@pytest.mark.api
class TestAPIErrorHandling:
    """Test API error handling integration"""
    
    def test_validation_error_response(self, api_client):
        """Test validation error response format"""
        # Send invalid data
        response = api_client.post("/api/search", json={"invalid": "data"})
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_internal_error_handling(self, api_client):
        """Test internal error handling"""
        with patch('semantic_fs.api.get_semantic_fs') as mock_get_fs:
            mock_fs = Mock()
            mock_fs.query.side_effect = Exception("Internal error")
            mock_get_fs.return_value = mock_fs
            
            response = api_client.post(
                "/api/search",
                json={"query": "test", "limit": 5}
            )
            
            assert response.status_code == 500
    
    def test_not_found_error(self, api_client):
        """Test 404 error handling"""
        response = api_client.get("/api/nonexistent")
        
        assert response.status_code == 404
    
    def test_method_not_allowed(self, api_client):
        """Test 405 error handling"""
        response = api_client.put("/api/health")
        
        assert response.status_code == 405
