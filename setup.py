"""
Setup script for Semantic Filesystem
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="semantic-filesystem",
    version="0.1.0",
    author="Semantic FS Team",
    description="LLM-powered semantic file organization and search",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=[
        "fastapi>=0.104.1",
        "uvicorn>=0.24.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "sqlalchemy>=2.0.23",
        "chromadb>=0.4.18",
        "sentence-transformers>=2.2.2",
        "openai>=1.3.7",
        "PyPDF2>=3.0.1",
        "python-docx>=1.1.0",
        "openpyxl>=3.1.2",
        "Pillow>=10.1.0",
        "click>=8.1.7",
        "rich>=13.7.0",
        "python-dotenv>=1.0.0",
    ],
    extras_require={
        "ocr": ["pytesseract>=0.3.10"],
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "black",
            "flake8",
            "mypy",
        ],
    },
    entry_points={
        "console_scripts": [
            "semantic-fs=semantic_fs.cli:cli",
        ],
    },
)
