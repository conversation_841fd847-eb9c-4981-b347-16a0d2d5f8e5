"""
Specialized Content Extractor
Extractor for proprietary and very specialized file formats
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Set
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class SpecializedExtractor(BaseExtractor):
    """Extractor for proprietary and specialized file formats"""
    
    def get_supported_extensions(self) -> Set[str]:
        """Get set of supported specialized extensions"""
        return {
            # Proprietary formats
            '.flt', '.venc', '.idx', '.meta', '.desc', '.spec', '.def', '.ref',
            
            # System metadata
            '.cache', '.index', '.catalog', '.manifest', '.registry',
            
            # Database auxiliary
            '.db-journal', '.sqlite-wal', '.sqlite-shm', '.lck', '.lock', '.pid',
            
            # Identifiers
            '.guid', '.uuid', '.hash', '.checksum', '.crc', '.md5', '.sha1', '.sha256',
            
            # Security files
            '.sig', '.signature', '.cert', '.key', '.pem', '.crt', '.p12',
            '.jks', '.keystore', '.truststore', '.pfx', '.der', '.csr',
            
            # Licensing
            '.license-key', '.activation', '.serial', '.token', '.session',
            
            # Scientific data
            '.mat', '.h5', '.hdf5', '.npy', '.npz', '.pkl', '.pickle', '.joblib',
            
            # Big data formats
            '.parquet', '.avro', '.orc', '.feather', '.arrow', '.msgpack',
            
            # Protocol buffers
            '.protobuf', '.pb', '.proto', '.thrift', '.capnp',
            
            # Web resources
            '.webmanifest', '.htaccess', '.htpasswd', '.robots', '.sitemap',
            
            # Version control
            '.patch', '.diff', '.rej', '.orig', '.bak', '.swp', '.tmp'
        }
    
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get mapping of specialized extensions to MIME types"""
        return {
            # Proprietary formats
            '.flt': 'application/octet-stream',
            '.venc': 'application/octet-stream',
            '.idx': 'application/octet-stream',
            '.meta': 'text/plain',
            '.desc': 'text/plain',
            '.spec': 'text/plain',
            '.def': 'text/plain',
            '.ref': 'text/plain',
            
            # System metadata
            '.cache': 'application/octet-stream',
            '.index': 'text/plain',
            '.catalog': 'text/plain',
            '.manifest': 'text/plain',
            '.registry': 'text/plain',
            
            # Database auxiliary
            '.db-journal': 'application/octet-stream',
            '.sqlite-wal': 'application/octet-stream',
            '.sqlite-shm': 'application/octet-stream',
            '.lck': 'text/plain',
            '.lock': 'text/plain',
            '.pid': 'text/plain',
            
            # Identifiers
            '.guid': 'text/plain',
            '.uuid': 'text/plain',
            '.hash': 'text/plain',
            '.checksum': 'text/plain',
            '.crc': 'text/plain',
            '.md5': 'text/plain',
            '.sha1': 'text/plain',
            '.sha256': 'text/plain',
            
            # Security files
            '.sig': 'application/pgp-signature',
            '.signature': 'application/pgp-signature',
            '.cert': 'application/x-x509-ca-cert',
            '.key': 'application/pkcs8',
            '.pem': 'application/x-pem-file',
            '.crt': 'application/x-x509-ca-cert',
            '.p12': 'application/x-pkcs12',
            '.jks': 'application/x-java-keystore',
            '.keystore': 'application/x-java-keystore',
            '.truststore': 'application/x-java-keystore',
            '.pfx': 'application/x-pkcs12',
            '.der': 'application/x-x509-ca-cert',
            '.csr': 'application/pkcs10',
            
            # Licensing
            '.license-key': 'text/plain',
            '.activation': 'text/plain',
            '.serial': 'text/plain',
            '.token': 'text/plain',
            '.session': 'text/plain',
            
            # Scientific data
            '.mat': 'application/x-matlab-data',
            '.h5': 'application/x-hdf5',
            '.hdf5': 'application/x-hdf5',
            '.npy': 'application/octet-stream',
            '.npz': 'application/zip',
            '.pkl': 'application/octet-stream',
            '.pickle': 'application/octet-stream',
            '.joblib': 'application/octet-stream',
            
            # Big data formats
            '.parquet': 'application/vnd.apache.parquet',
            '.avro': 'application/avro',
            '.orc': 'application/orc',
            '.feather': 'application/vnd.apache.arrow.file',
            '.arrow': 'application/vnd.apache.arrow.file',
            '.msgpack': 'application/msgpack',
            
            # Protocol buffers
            '.protobuf': 'application/x-protobuf',
            '.pb': 'application/x-protobuf',
            '.proto': 'text/x-protobuf',
            '.thrift': 'application/x-thrift',
            '.capnp': 'application/x-capnp',
            
            # Web resources
            '.webmanifest': 'application/manifest+json',
            '.htaccess': 'text/plain',
            '.htpasswd': 'text/plain',
            '.robots': 'text/plain',
            '.sitemap': 'application/xml',
            
            # Version control
            '.patch': 'text/x-patch',
            '.diff': 'text/x-diff',
            '.rej': 'text/x-reject',
            '.orig': 'text/plain',
            '.bak': 'text/plain',
            '.swp': 'application/octet-stream',
            '.tmp': 'text/plain'
        }
    
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract content from specialized files"""
        try:
            extension = file_path.suffix.lower()
            if extension not in self.get_supported_extensions():
                return None
            
            content_parts = []
            
            # 1. Extract basic file info
            file_info = self._get_file_info(file_path)
            content_parts.append(f"Specialized File: {file_info['name']} ({file_info['size_mb']} MB)")
            
            # 2. Analyze file category
            category = self._analyze_file_category(extension)
            if category:
                content_parts.append(f"Category: {category}")
            
            # 3. Extract specialized content
            specialized_content = self._extract_specialized_content(file_path, extension)
            if specialized_content:
                content_parts.append(specialized_content)
            
            # 4. Analyze file purpose
            purpose_analysis = self._analyze_file_purpose(file_path, extension)
            if purpose_analysis:
                content_parts.append(f"Purpose: {purpose_analysis}")
            
            # 5. Extract filename context
            filename_context = self._analyze_filename_context(file_path)
            if filename_context:
                content_parts.append(f"Context: {filename_context}")
            
            return "\n\n".join(content_parts) if content_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting specialized content from {file_path}: {e}")
            return f"Specialized file: {file_path.name} (extraction error)"
    
    def _analyze_file_category(self, extension: str) -> Optional[str]:
        """Analyze the category of specialized file"""
        categories = {
            'proprietary': ['.flt', '.venc'],
            'metadata': ['.idx', '.meta', '.desc', '.spec', '.def', '.ref'],
            'system': ['.cache', '.index', '.catalog', '.manifest', '.registry'],
            'database': ['.db-journal', '.sqlite-wal', '.sqlite-shm'],
            'locking': ['.lck', '.lock', '.pid'],
            'identity': ['.guid', '.uuid'],
            'checksum': ['.hash', '.checksum', '.crc', '.md5', '.sha1', '.sha256'],
            'security': ['.sig', '.signature', '.cert', '.key', '.pem', '.crt', '.p12', '.jks', '.keystore', '.truststore', '.pfx', '.der', '.csr'],
            'licensing': ['.license-key', '.activation', '.serial', '.token', '.session'],
            'scientific': ['.mat', '.h5', '.hdf5', '.npy', '.npz', '.pkl', '.pickle', '.joblib'],
            'bigdata': ['.parquet', '.avro', '.orc', '.feather', '.arrow', '.msgpack'],
            'protocol': ['.protobuf', '.pb', '.proto', '.thrift', '.capnp'],
            'web': ['.webmanifest', '.htaccess', '.htpasswd', '.robots', '.sitemap'],
            'version_control': ['.patch', '.diff', '.rej', '.orig', '.bak', '.swp', '.tmp']
        }
        
        for category, extensions in categories.items():
            if extension in extensions:
                return category.replace('_', ' ').title()
        
        return None
    
    def _extract_specialized_content(self, file_path: Path, extension: str) -> Optional[str]:
        """Extract content specific to the file type"""
        try:
            content_parts = []
            
            # Text-based files that can be read directly
            text_extensions = {'.meta', '.desc', '.spec', '.def', '.ref', '.index', '.catalog', 
                             '.manifest', '.registry', '.lck', '.lock', '.pid', '.guid', '.uuid',
                             '.hash', '.checksum', '.crc', '.md5', '.sha1', '.sha256', 
                             '.license-key', '.activation', '.serial', '.token', '.session',
                             '.htaccess', '.htpasswd', '.robots', '.sitemap', '.patch', '.diff',
                             '.rej', '.orig', '.bak', '.tmp'}
            
            if extension in text_extensions:
                text_content = self._extract_text_content(file_path)
                if text_content:
                    content_parts.append(f"Content: {text_content[:500]}{'...' if len(text_content) > 500 else ''}")
            
            # Security files
            elif extension in ['.sig', '.signature', '.cert', '.key', '.pem', '.crt', '.p12', '.jks', '.keystore', '.truststore', '.pfx', '.der', '.csr']:
                security_info = self._analyze_security_file(file_path, extension)
                if security_info:
                    content_parts.append(security_info)
            
            # Scientific data files
            elif extension in ['.mat', '.h5', '.hdf5', '.npy', '.npz', '.pkl', '.pickle', '.joblib']:
                scientific_info = self._analyze_scientific_file(file_path, extension)
                if scientific_info:
                    content_parts.append(scientific_info)
            
            return "\n".join(content_parts) if content_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting specialized content: {e}")
            return None
    
    def _extract_text_content(self, file_path: Path) -> Optional[str]:
        """Extract text content from readable files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(1000)  # Read first 1000 characters
                return content.strip() if content else None
        except Exception as e:
            logger.debug(f"Error reading text content: {e}")
            return None
    
    def _analyze_security_file(self, file_path: Path, extension: str) -> Optional[str]:
        """Analyze security-related files"""
        try:
            filename = file_path.stem.lower()
            analysis_parts = []
            
            # Certificate type analysis
            if extension in ['.cert', '.crt', '.pem']:
                if any(keyword in filename for keyword in ['ca', 'root', 'authority']):
                    analysis_parts.append("Certificate Authority certificate")
                elif any(keyword in filename for keyword in ['server', 'ssl', 'tls']):
                    analysis_parts.append("Server certificate")
                elif any(keyword in filename for keyword in ['client', 'user']):
                    analysis_parts.append("Client certificate")
            
            # Key type analysis
            elif extension in ['.key', '.pem']:
                if 'private' in filename:
                    analysis_parts.append("Private key")
                elif 'public' in filename:
                    analysis_parts.append("Public key")
            
            # Signature analysis
            elif extension in ['.sig', '.signature']:
                if 'pgp' in filename or 'gpg' in filename:
                    analysis_parts.append("PGP/GPG signature")
                elif 'detached' in filename:
                    analysis_parts.append("Detached signature")
            
            return " | ".join(analysis_parts) if analysis_parts else None
            
        except Exception as e:
            logger.debug(f"Error analyzing security file: {e}")
            return None
    
    def _analyze_scientific_file(self, file_path: Path, extension: str) -> Optional[str]:
        """Analyze scientific data files"""
        try:
            filename = file_path.stem.lower()
            analysis_parts = []
            
            # Data type analysis
            if extension == '.mat':
                analysis_parts.append("MATLAB data file")
            elif extension in ['.h5', '.hdf5']:
                analysis_parts.append("HDF5 hierarchical data")
            elif extension in ['.npy', '.npz']:
                analysis_parts.append("NumPy array data")
            elif extension in ['.pkl', '.pickle']:
                analysis_parts.append("Python pickle data")
            elif extension == '.joblib':
                analysis_parts.append("Joblib serialized data")
            
            # Purpose analysis from filename
            if any(keyword in filename for keyword in ['model', 'trained', 'weights']):
                analysis_parts.append("likely machine learning model")
            elif any(keyword in filename for keyword in ['data', 'dataset', 'samples']):
                analysis_parts.append("likely dataset")
            elif any(keyword in filename for keyword in ['results', 'output', 'predictions']):
                analysis_parts.append("likely analysis results")
            
            return " | ".join(analysis_parts) if analysis_parts else None
            
        except Exception as e:
            logger.debug(f"Error analyzing scientific file: {e}")
            return None
    
    def _analyze_file_purpose(self, file_path: Path, extension: str) -> Optional[str]:
        """Analyze the likely purpose of the specialized file"""
        try:
            filename = file_path.stem.lower()
            purpose_parts = []
            
            # General purpose patterns
            if any(keyword in filename for keyword in ['config', 'settings', 'prefs']):
                purpose_parts.append("configuration")
            elif any(keyword in filename for keyword in ['cache', 'temp', 'tmp']):
                purpose_parts.append("temporary data")
            elif any(keyword in filename for keyword in ['backup', 'bak', 'archive']):
                purpose_parts.append("backup")
            elif any(keyword in filename for keyword in ['log', 'debug', 'trace']):
                purpose_parts.append("logging")
            elif any(keyword in filename for keyword in ['index', 'catalog', 'manifest']):
                purpose_parts.append("indexing/cataloging")
            elif any(keyword in filename for keyword in ['lock', 'pid', 'session']):
                purpose_parts.append("process management")
            
            return ", ".join(purpose_parts) if purpose_parts else None
            
        except Exception as e:
            logger.debug(f"Error analyzing file purpose: {e}")
            return None
