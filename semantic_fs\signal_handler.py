"""
Cross-platform signal handling utilities for graceful shutdown
"""

import os
import sys
import signal
import logging
import platform
import threading
from typing import Callable, List, Optional
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# Windows-specific imports
if platform.system() == "Windows":
    try:
        import win32api
        import win32con
        WINDOWS_SIGNALS_AVAILABLE = True
    except ImportError:
        WINDOWS_SIGNALS_AVAILABLE = False
        logger.warning("pywin32 not available - Windows signal handling will be limited")
else:
    WINDOWS_SIGNALS_AVAILABLE = False


class SignalManager:
    """Cross-platform signal manager for graceful shutdown"""
    
    def __init__(self):
        self.shutdown_handlers: List[Callable] = []
        self.shutdown_lock = threading.Lock()
        self.shutdown_initiated = False
        self._original_handlers = {}
        
    def add_shutdown_handler(self, handler: Callable):
        """Add a shutdown handler function
        
        Args:
            handler: Function to call during shutdown
        """
        with self.shutdown_lock:
            if handler not in self.shutdown_handlers:
                self.shutdown_handlers.append(handler)
                logger.debug(f"Added shutdown handler: {handler.__name__}")
    
    def remove_shutdown_handler(self, handler: Callable):
        """Remove a shutdown handler function
        
        Args:
            handler: Function to remove from shutdown handlers
        """
        with self.shutdown_lock:
            if handler in self.shutdown_handlers:
                self.shutdown_handlers.remove(handler)
                logger.debug(f"Removed shutdown handler: {handler.__name__}")
    
    def register_signal_handlers(self):
        """Register signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name if hasattr(signal, 'Signals') else str(signum)
            logger.info(f"Received signal {signal_name} ({signum}), initiating shutdown...")
            self.shutdown()
        
        # Register standard POSIX signals
        signals_to_handle = [signal.SIGINT, signal.SIGTERM]
        
        # Add SIGBREAK on Windows
        if platform.system() == "Windows" and hasattr(signal, 'SIGBREAK'):
            signals_to_handle.append(signal.SIGBREAK)
        
        for sig in signals_to_handle:
            try:
                self._original_handlers[sig] = signal.signal(sig, signal_handler)
                logger.debug(f"Registered handler for signal {sig}")
            except (OSError, ValueError) as e:
                logger.warning(f"Could not register handler for signal {sig}: {e}")
        
        # Windows-specific console event handling
        if platform.system() == "Windows" and WINDOWS_SIGNALS_AVAILABLE:
            self._register_windows_handlers()
    
    def _register_windows_handlers(self):
        """Register Windows-specific console event handlers"""
        def windows_console_handler(ctrl_type):
            """Handle Windows console control events"""
            event_names = {
                win32con.CTRL_C_EVENT: "CTRL_C_EVENT",
                win32con.CTRL_BREAK_EVENT: "CTRL_BREAK_EVENT",
                win32con.CTRL_CLOSE_EVENT: "CTRL_CLOSE_EVENT",
                win32con.CTRL_LOGOFF_EVENT: "CTRL_LOGOFF_EVENT",
                win32con.CTRL_SHUTDOWN_EVENT: "CTRL_SHUTDOWN_EVENT"
            }
            
            event_name = event_names.get(ctrl_type, f"UNKNOWN_EVENT_{ctrl_type}")
            logger.info(f"Windows console event: {event_name}")
            
            if ctrl_type in (win32con.CTRL_C_EVENT, win32con.CTRL_BREAK_EVENT, 
                           win32con.CTRL_CLOSE_EVENT, win32con.CTRL_SHUTDOWN_EVENT):
                self.shutdown()
                return True  # Indicate we handled the event
            
            return False  # Let Windows handle other events
        
        try:
            win32api.SetConsoleCtrlHandler(windows_console_handler, True)
            logger.info("Registered Windows console control handler")
        except Exception as e:
            logger.warning(f"Could not register Windows console handler: {e}")
    
    def shutdown(self):
        """Execute all shutdown handlers"""
        with self.shutdown_lock:
            if self.shutdown_initiated:
                logger.debug("Shutdown already initiated, skipping")
                return
            
            self.shutdown_initiated = True
            logger.info("Initiating graceful shutdown...")
        
        # Execute shutdown handlers in reverse order (LIFO)
        for handler in reversed(self.shutdown_handlers):
            try:
                logger.debug(f"Executing shutdown handler: {handler.__name__}")
                handler()
            except Exception as e:
                logger.error(f"Error in shutdown handler {handler.__name__}: {e}")
        
        logger.info("Graceful shutdown completed")
    
    def restore_signal_handlers(self):
        """Restore original signal handlers"""
        for sig, original_handler in self._original_handlers.items():
            try:
                signal.signal(sig, original_handler)
                logger.debug(f"Restored original handler for signal {sig}")
            except (OSError, ValueError) as e:
                logger.warning(f"Could not restore handler for signal {sig}: {e}")
        
        self._original_handlers.clear()
    
    @contextmanager
    def managed_shutdown(self):
        """Context manager for automatic signal handler management"""
        try:
            self.register_signal_handlers()
            yield self
        finally:
            self.restore_signal_handlers()


# Global signal manager instance
_signal_manager: Optional[SignalManager] = None


def get_signal_manager() -> SignalManager:
    """Get the global signal manager instance"""
    global _signal_manager
    if _signal_manager is None:
        _signal_manager = SignalManager()
    return _signal_manager


def register_shutdown_handler(handler: Callable):
    """Register a shutdown handler with the global signal manager
    
    Args:
        handler: Function to call during shutdown
    """
    get_signal_manager().add_shutdown_handler(handler)


def setup_signal_handling():
    """Setup signal handling for the current process"""
    manager = get_signal_manager()
    manager.register_signal_handlers()
    return manager


@contextmanager
def signal_handling():
    """Context manager for automatic signal handling setup and cleanup"""
    with get_signal_manager().managed_shutdown() as manager:
        yield manager


def is_shutdown_initiated() -> bool:
    """Check if shutdown has been initiated"""
    return get_signal_manager().shutdown_initiated


def initiate_shutdown():
    """Manually initiate shutdown"""
    get_signal_manager().shutdown()
