"""
Subscription management system for desktop application
Handles Pro tier licensing, validation, and feature gating
"""

import os
import json
import time
import hashlib
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from cryptography.fernet import Fernet
import jwt

logger = logging.getLogger(__name__)


@dataclass
class SubscriptionInfo:
    """Subscription information"""
    tier: str  # 'free', 'pro', 'premium', 'ultimate'
    status: str  # 'active', 'expired', 'trial', 'cancelled'
    expires_at: datetime
    features: List[str]
    max_indexed_files: int
    max_indexed_size_gb: int
    license_key: str = ""
    user_email: str = ""
    created_at: Optional[datetime] = None
    
    @property
    def is_active(self) -> bool:
        return self.status == 'active' and self.expires_at > datetime.utcnow()
    
    @property
    def is_trial(self) -> bool:
        return self.status == 'trial' and self.expires_at > datetime.utcnow()
    
    @property
    def days_remaining(self) -> int:
        if self.expires_at <= datetime.utcnow():
            return 0
        return (self.expires_at - datetime.utcnow()).days


class SubscriptionManager:
    """Manages subscription licensing and feature access"""
    
    def __init__(self, app_data_dir: Path):
        """Initialize subscription manager"""
        self.app_data_dir = app_data_dir
        self.license_file = app_data_dir / "license.dat"
        self.config_file = app_data_dir / "subscription.json"
        
        # Encryption key for license storage
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # JWT secret for license validation
        self.jwt_secret = "semantic_fs_desktop_2024_secret_key"  # In production, use proper key management
        
        # Subscription tiers configuration
        self.tiers = {
            'free': {
                'name': 'Free Tier',
                'max_indexed_files': 1000,
                'max_indexed_size_gb': 1,
                'features': ['basic_search', 'document_indexing'],
                'price': 0
            },
            'pro': {
                'name': 'Pro Tier',
                'max_indexed_files': 1000000,  # 1M files
                'max_indexed_size_gb': 1000,   # 1TB
                'features': ['unlimited_indexing', 'semantic_search', 'advanced_filters', 'priority_support'],
                'price': 9.99
            },
            'premium': {
                'name': 'Premium Tier (Future)',
                'max_indexed_files': 1000000,
                'max_indexed_size_gb': 1000,
                'features': ['unlimited_indexing', 'semantic_search', 'audio_indexing', 'advanced_ai'],
                'price': 19.99
            },
            'ultimate': {
                'name': 'Ultimate Tier (Future)',
                'max_indexed_files': 1000000,
                'max_indexed_size_gb': 1000,
                'features': ['unlimited_indexing', 'semantic_search', 'audio_indexing', 'video_indexing', 'ai_insights'],
                'price': 29.99
            }
        }
        
        # Load current subscription
        self.current_subscription = self._load_subscription()
        
        logger.info(f"Subscription manager initialized - Current tier: {self.current_subscription.tier}")
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for license storage"""
        key_file = self.app_data_dir / ".key"
        
        if key_file.exists():
            return key_file.read_bytes()
        else:
            key = Fernet.generate_key()
            key_file.write_bytes(key)
            key_file.chmod(0o600)  # Restrict permissions
            return key
    
    def _load_subscription(self) -> SubscriptionInfo:
        """Load subscription information from storage"""
        try:
            if self.license_file.exists():
                # Try to load from encrypted license file
                encrypted_data = self.license_file.read_bytes()
                decrypted_data = self.cipher.decrypt(encrypted_data)
                license_data = json.loads(decrypted_data.decode())
                
                # Validate license
                if self._validate_license(license_data):
                    return self._create_subscription_from_license(license_data)
            
            # Check for trial
            if self.config_file.exists():
                config_data = json.loads(self.config_file.read_text())
                if 'trial_started' in config_data:
                    trial_start = datetime.fromisoformat(config_data['trial_started'])
                    trial_end = trial_start + timedelta(days=7)  # 7-day trial
                    
                    if datetime.utcnow() < trial_end:
                        return SubscriptionInfo(
                            tier='pro',
                            status='trial',
                            expires_at=trial_end,
                            features=self.tiers['pro']['features'],
                            max_indexed_files=self.tiers['pro']['max_indexed_files'],
                            max_indexed_size_gb=self.tiers['pro']['max_indexed_size_gb'],
                            created_at=trial_start
                        )
        
        except Exception as e:
            logger.warning(f"Error loading subscription: {e}")
        
        # Default to free tier
        return self._create_free_subscription()
    
    def _create_free_subscription(self) -> SubscriptionInfo:
        """Create free tier subscription"""
        return SubscriptionInfo(
            tier='free',
            status='active',
            expires_at=datetime.utcnow() + timedelta(days=365),  # Free tier doesn't expire
            features=self.tiers['free']['features'],
            max_indexed_files=self.tiers['free']['max_indexed_files'],
            max_indexed_size_gb=self.tiers['free']['max_indexed_size_gb']
        )
    
    def _validate_license(self, license_data: Dict[str, Any]) -> bool:
        """Validate license data"""
        try:
            # Decode JWT token
            token = license_data.get('token', '')
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            
            # Check expiration
            if payload.get('exp', 0) < time.time():
                return False
            
            # Check required fields
            required_fields = ['tier', 'email', 'expires_at']
            if not all(field in payload for field in required_fields):
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"License validation failed: {e}")
            return False
    
    def _create_subscription_from_license(self, license_data: Dict[str, Any]) -> SubscriptionInfo:
        """Create subscription info from valid license"""
        try:
            payload = jwt.decode(license_data['token'], self.jwt_secret, algorithms=['HS256'])
            tier = payload['tier']
            
            return SubscriptionInfo(
                tier=tier,
                status='active',
                expires_at=datetime.fromtimestamp(payload['expires_at']),
                features=self.tiers[tier]['features'],
                max_indexed_files=self.tiers[tier]['max_indexed_files'],
                max_indexed_size_gb=self.tiers[tier]['max_indexed_size_gb'],
                license_key=license_data.get('key', ''),
                user_email=payload['email'],
                created_at=datetime.fromtimestamp(payload.get('created_at', time.time()))
            )
        except Exception as e:
            logger.error(f"Error creating subscription from license: {e}")
            return self._create_free_subscription()
    
    def start_trial(self) -> bool:
        """Start 7-day free trial"""
        try:
            if self.current_subscription.tier != 'free':
                logger.warning("Trial already used or subscription active")
                return False
            
            # Check if trial was already used
            if self.config_file.exists():
                config_data = json.loads(self.config_file.read_text())
                if 'trial_started' in config_data:
                    logger.warning("Trial already used")
                    return False
            
            # Start trial
            trial_start = datetime.utcnow()
            config_data = {
                'trial_started': trial_start.isoformat(),
                'machine_id': self._get_machine_id()
            }
            
            self.config_file.write_text(json.dumps(config_data, indent=2))
            
            # Update current subscription
            self.current_subscription = SubscriptionInfo(
                tier='pro',
                status='trial',
                expires_at=trial_start + timedelta(days=7),
                features=self.tiers['pro']['features'],
                max_indexed_files=self.tiers['pro']['max_indexed_files'],
                max_indexed_size_gb=self.tiers['pro']['max_indexed_size_gb'],
                created_at=trial_start
            )
            
            logger.info("7-day trial started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting trial: {e}")
            return False
    
    def activate_license(self, license_key: str) -> bool:
        """Activate a license key"""
        try:
            # In a real implementation, this would validate with a license server
            # For now, we'll create a simple validation
            
            if not license_key or len(license_key) < 20:
                return False
            
            # Create JWT token for the license
            payload = {
                'tier': 'pro',
                'email': '<EMAIL>',  # Would come from license server
                'expires_at': (datetime.utcnow() + timedelta(days=30)).timestamp(),
                'created_at': time.time(),
                'machine_id': self._get_machine_id()
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
            
            # Store encrypted license
            license_data = {
                'key': license_key,
                'token': token,
                'activated_at': datetime.utcnow().isoformat()
            }
            
            encrypted_data = self.cipher.encrypt(json.dumps(license_data).encode())
            self.license_file.write_bytes(encrypted_data)
            
            # Update current subscription
            self.current_subscription = self._create_subscription_from_license(license_data)
            
            logger.info(f"License activated: {license_key[:8]}...")
            return True
            
        except Exception as e:
            logger.error(f"Error activating license: {e}")
            return False
    
    def _get_machine_id(self) -> str:
        """Get unique machine identifier"""
        try:
            import platform
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            return hashlib.sha256(machine_info.encode()).hexdigest()[:16]
        except:
            return "unknown"
    
    def check_feature_access(self, feature: str) -> bool:
        """Check if current subscription has access to a feature"""
        return feature in self.current_subscription.features
    
    def check_indexing_limits(self, current_files: int, current_size_gb: float) -> Dict[str, Any]:
        """Check if indexing is within subscription limits"""
        return {
            'files_allowed': current_files <= self.current_subscription.max_indexed_files,
            'size_allowed': current_size_gb <= self.current_subscription.max_indexed_size_gb,
            'files_limit': self.current_subscription.max_indexed_files,
            'size_limit_gb': self.current_subscription.max_indexed_size_gb,
            'files_usage_percent': (current_files / self.current_subscription.max_indexed_files) * 100,
            'size_usage_percent': (current_size_gb / self.current_subscription.max_indexed_size_gb) * 100
        }
    
    def get_subscription_info(self) -> Dict[str, Any]:
        """Get current subscription information"""
        return {
            'tier': self.current_subscription.tier,
            'tier_name': self.tiers[self.current_subscription.tier]['name'],
            'status': self.current_subscription.status,
            'is_active': self.current_subscription.is_active,
            'is_trial': self.current_subscription.is_trial,
            'expires_at': self.current_subscription.expires_at.isoformat(),
            'days_remaining': self.current_subscription.days_remaining,
            'features': self.current_subscription.features,
            'limits': {
                'max_files': self.current_subscription.max_indexed_files,
                'max_size_gb': self.current_subscription.max_indexed_size_gb
            },
            'user_email': self.current_subscription.user_email
        }
    
    def get_available_tiers(self) -> Dict[str, Any]:
        """Get information about all available tiers"""
        return {
            tier: {
                'name': info['name'],
                'price': info['price'],
                'features': info['features'],
                'limits': {
                    'max_files': info['max_indexed_files'],
                    'max_size_gb': info['max_indexed_size_gb']
                }
            }
            for tier, info in self.tiers.items()
        }
    
    def refresh_subscription(self) -> bool:
        """Refresh subscription status (check for updates)"""
        try:
            # Reload subscription from storage
            old_subscription = self.current_subscription
            self.current_subscription = self._load_subscription()
            
            # Check if subscription changed
            if old_subscription.tier != self.current_subscription.tier:
                logger.info(f"Subscription changed: {old_subscription.tier} -> {self.current_subscription.tier}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error refreshing subscription: {e}")
            return False
