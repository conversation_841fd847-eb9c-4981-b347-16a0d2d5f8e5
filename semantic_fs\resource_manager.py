"""
Resource management system for preventing memory leaks and managing cleanup
"""

import gc
import os
import sys
import time
import psutil
import logging
import threading
import weakref
from typing import Dict, List, Any, Optional, Callable, Set
from contextlib import contextmanager
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class ResourceStats:
    """Resource usage statistics"""
    memory_mb: float
    cpu_percent: float
    open_files: int
    threads: int
    connections: int
    cache_size: int
    timestamp: datetime


class ResourceTracker:
    """Tracks resource usage and detects potential leaks"""
    
    def __init__(self, check_interval: int = 60):
        """Initialize resource tracker
        
        Args:
            check_interval: Seconds between resource checks
        """
        self.check_interval = check_interval
        self.stats_history: List[ResourceStats] = []
        self.max_history = 100
        self.running = False
        self.thread: Optional[threading.Thread] = None
        self.process = psutil.Process()
        
        # Resource limits
        self.memory_limit_mb = 1024  # 1GB default
        self.file_limit = 1000
        self.thread_limit = 50
        self.connection_limit = 100
        
        # Leak detection
        self.leak_detection_enabled = True
        self.memory_growth_threshold = 100  # MB
        self.consecutive_growth_limit = 5
    
    def start(self):
        """Start resource monitoring"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.name = "ResourceTracker"
        self.thread.start()
        logger.info("Resource tracker started")
    
    def stop(self):
        """Stop resource monitoring"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("Resource tracker stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                stats = self._collect_stats()
                self._add_stats(stats)
                self._check_limits(stats)
                
                if self.leak_detection_enabled:
                    self._detect_leaks()
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in resource monitoring: {e}")
                time.sleep(self.check_interval)
    
    def _collect_stats(self) -> ResourceStats:
        """Collect current resource statistics"""
        try:
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            cpu_percent = self.process.cpu_percent()
            
            # Count open files
            try:
                open_files = len(self.process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            # Count threads
            try:
                threads = self.process.num_threads()
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                threads = 0
            
            # Estimate connections (network connections)
            try:
                connections = len(self.process.connections())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                connections = 0
            
            # Get cache size
            cache_size = self._get_cache_size()
            
            return ResourceStats(
                memory_mb=memory_mb,
                cpu_percent=cpu_percent,
                open_files=open_files,
                threads=threads,
                connections=connections,
                cache_size=cache_size,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.warning(f"Error collecting resource stats: {e}")
            return ResourceStats(0, 0, 0, 0, 0, 0, datetime.utcnow())
    
    def _get_cache_size(self) -> int:
        """Get approximate cache size"""
        try:
            from .cache import get_memory_cache
            cache = get_memory_cache()
            return len(cache.cache)
        except Exception:
            return 0
    
    def _add_stats(self, stats: ResourceStats):
        """Add stats to history"""
        self.stats_history.append(stats)
        
        # Limit history size
        if len(self.stats_history) > self.max_history:
            self.stats_history = self.stats_history[-self.max_history:]
    
    def _check_limits(self, stats: ResourceStats):
        """Check if resource limits are exceeded"""
        if stats.memory_mb > self.memory_limit_mb:
            logger.warning(f"Memory usage high: {stats.memory_mb:.1f}MB (limit: {self.memory_limit_mb}MB)")
        
        if stats.open_files > self.file_limit:
            logger.warning(f"Open files high: {stats.open_files} (limit: {self.file_limit})")
        
        if stats.threads > self.thread_limit:
            logger.warning(f"Thread count high: {stats.threads} (limit: {self.thread_limit})")
        
        if stats.connections > self.connection_limit:
            logger.warning(f"Connection count high: {stats.connections} (limit: {self.connection_limit})")
    
    def _detect_leaks(self):
        """Detect potential memory leaks"""
        if len(self.stats_history) < self.consecutive_growth_limit:
            return
        
        recent_stats = self.stats_history[-self.consecutive_growth_limit:]
        
        # Check for consistent memory growth
        memory_values = [s.memory_mb for s in recent_stats]
        is_growing = all(memory_values[i] < memory_values[i+1] 
                        for i in range(len(memory_values)-1))
        
        if is_growing:
            growth = memory_values[-1] - memory_values[0]
            if growth > self.memory_growth_threshold:
                logger.warning(f"Potential memory leak detected: {growth:.1f}MB growth over {len(recent_stats)} checks")
    
    def get_current_stats(self) -> Optional[ResourceStats]:
        """Get current resource statistics"""
        if self.stats_history:
            return self.stats_history[-1]
        return self._collect_stats()
    
    def get_stats_summary(self) -> Dict[str, Any]:
        """Get resource statistics summary"""
        if not self.stats_history:
            return {}
        
        current = self.stats_history[-1]
        
        # Calculate averages over last 10 entries
        recent = self.stats_history[-10:]
        avg_memory = sum(s.memory_mb for s in recent) / len(recent)
        avg_cpu = sum(s.cpu_percent for s in recent) / len(recent)
        
        return {
            "current": {
                "memory_mb": current.memory_mb,
                "cpu_percent": current.cpu_percent,
                "open_files": current.open_files,
                "threads": current.threads,
                "connections": current.connections,
                "cache_size": current.cache_size
            },
            "averages": {
                "memory_mb": avg_memory,
                "cpu_percent": avg_cpu
            },
            "limits": {
                "memory_mb": self.memory_limit_mb,
                "open_files": self.file_limit,
                "threads": self.thread_limit,
                "connections": self.connection_limit
            }
        }


class ResourceManager:
    """Manages resources and prevents leaks"""
    
    def __init__(self):
        """Initialize resource manager"""
        self.tracker = ResourceTracker()
        self.cleanup_handlers: List[Callable] = []
        self.managed_objects: Set[weakref.ref] = set()
        self.connection_pools: Dict[str, Any] = {}
        self.file_handles: Dict[str, Any] = {}
        self.cleanup_lock = threading.Lock()
        
        # Auto-cleanup settings
        self.auto_cleanup_enabled = True
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def start(self):
        """Start resource management"""
        self.tracker.start()
        logger.info("Resource manager started")
    
    def stop(self):
        """Stop resource management and cleanup"""
        logger.info("Stopping resource manager...")
        
        # Stop tracker
        self.tracker.stop()
        
        # Run cleanup
        self.cleanup_all()
        
        logger.info("Resource manager stopped")
    
    def register_cleanup_handler(self, handler: Callable):
        """Register a cleanup handler function"""
        with self.cleanup_lock:
            if handler not in self.cleanup_handlers:
                self.cleanup_handlers.append(handler)
                logger.debug(f"Registered cleanup handler: {handler.__name__}")
    
    def unregister_cleanup_handler(self, handler: Callable):
        """Unregister a cleanup handler function"""
        with self.cleanup_lock:
            if handler in self.cleanup_handlers:
                self.cleanup_handlers.remove(handler)
                logger.debug(f"Unregistered cleanup handler: {handler.__name__}")
    
    def register_managed_object(self, obj: Any):
        """Register an object for automatic cleanup"""
        if hasattr(obj, '__del__') or hasattr(obj, 'close') or hasattr(obj, 'cleanup'):
            weak_ref = weakref.ref(obj, self._object_cleanup_callback)
            self.managed_objects.add(weak_ref)
    
    def _object_cleanup_callback(self, weak_ref):
        """Callback when managed object is garbage collected"""
        self.managed_objects.discard(weak_ref)
    
    def cleanup_all(self):
        """Run all cleanup handlers"""
        with self.cleanup_lock:
            logger.info("Running resource cleanup...")
            
            # Run registered cleanup handlers
            for handler in self.cleanup_handlers:
                try:
                    handler()
                    logger.debug(f"Executed cleanup handler: {handler.__name__}")
                except Exception as e:
                    logger.error(f"Error in cleanup handler {handler.__name__}: {e}")
            
            # Cleanup connection pools
            self._cleanup_connection_pools()
            
            # Cleanup file handles
            self._cleanup_file_handles()
            
            # Force garbage collection
            self._force_garbage_collection()
            
            self.last_cleanup = time.time()
            logger.info("Resource cleanup completed")
    
    def _cleanup_connection_pools(self):
        """Cleanup database connection pools"""
        for name, pool in list(self.connection_pools.items()):
            try:
                if hasattr(pool, 'dispose'):
                    pool.dispose()
                elif hasattr(pool, 'close'):
                    pool.close()
                logger.debug(f"Cleaned up connection pool: {name}")
            except Exception as e:
                logger.warning(f"Error cleaning up connection pool {name}: {e}")
        
        self.connection_pools.clear()
    
    def _cleanup_file_handles(self):
        """Cleanup open file handles"""
        for name, handle in list(self.file_handles.items()):
            try:
                if hasattr(handle, 'close') and not handle.closed:
                    handle.close()
                logger.debug(f"Closed file handle: {name}")
            except Exception as e:
                logger.warning(f"Error closing file handle {name}: {e}")
        
        self.file_handles.clear()
    
    def _force_garbage_collection(self):
        """Force garbage collection"""
        try:
            # Run garbage collection multiple times
            for i in range(3):
                collected = gc.collect()
                if collected > 0:
                    logger.debug(f"Garbage collection pass {i+1}: collected {collected} objects")
            
            # Clear weak references to dead objects
            dead_refs = [ref for ref in self.managed_objects if ref() is None]
            for ref in dead_refs:
                self.managed_objects.discard(ref)
            
            if dead_refs:
                logger.debug(f"Cleaned up {len(dead_refs)} dead object references")
                
        except Exception as e:
            logger.warning(f"Error during garbage collection: {e}")
    
    def check_and_cleanup(self):
        """Check if cleanup is needed and run it"""
        if not self.auto_cleanup_enabled:
            return
        
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self.cleanup_all()
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """Get current resource statistics"""
        stats = self.tracker.get_stats_summary()
        
        # Add manager-specific stats
        stats["managed_objects"] = len(self.managed_objects)
        stats["cleanup_handlers"] = len(self.cleanup_handlers)
        stats["connection_pools"] = len(self.connection_pools)
        stats["file_handles"] = len(self.file_handles)
        
        return stats


# Global resource manager instance
_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """Get the global resource manager instance"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager


@contextmanager
def managed_resource(resource, cleanup_func: Optional[Callable] = None):
    """Context manager for automatic resource cleanup"""
    manager = get_resource_manager()
    
    try:
        # Register for cleanup if needed
        if cleanup_func:
            manager.register_cleanup_handler(cleanup_func)
        else:
            manager.register_managed_object(resource)
        
        yield resource
        
    finally:
        # Cleanup
        if cleanup_func:
            try:
                cleanup_func()
            except Exception as e:
                logger.warning(f"Error in resource cleanup: {e}")
            finally:
                manager.unregister_cleanup_handler(cleanup_func)


def register_cleanup_handler(handler: Callable):
    """Register a cleanup handler with the global resource manager"""
    get_resource_manager().register_cleanup_handler(handler)


def cleanup_resources():
    """Manually trigger resource cleanup"""
    get_resource_manager().cleanup_all()


def get_resource_stats() -> Dict[str, Any]:
    """Get current resource statistics"""
    return get_resource_manager().get_resource_stats()


def start_resource_management():
    """Start the global resource management system"""
    get_resource_manager().start()


def stop_resource_management():
    """Stop the global resource management system"""
    get_resource_manager().stop()
