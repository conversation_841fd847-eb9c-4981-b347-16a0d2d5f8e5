# Development Environment Configuration
# This file contains development-specific settings

# Database settings
database_url: "sqlite:///./semantic_fs_dev.db"
vector_db_path: "./vector_db_dev"

# Logging settings
log_level: "DEBUG"
enable_file_logging: true
enable_json_logging: false
enable_log_rotation: true

# Security settings (relaxed for development)
auth_enabled: false
require_auth_for_read: false
require_auth_for_write: false
rate_limit_enabled: false
security_headers_enabled: true

# API settings
api_host: "localhost"
api_port: 8000
cors_origins: ["*"]

# LLM settings
use_ollama: true
ollama_base_url: "http://localhost:11434"
ollama_model: "phi"

# File processing
max_file_size: 52428800  # 50MB for development
auto_tag_enabled: true
content_summary_enabled: true
relationship_tracking_enabled: true

# Performance settings
supported_extensions:
  - ".txt"
  - ".md"
  - ".pdf"
  - ".docx"
  - ".json"
  - ".py"
  - ".js"
  - ".html"
  - ".css"
