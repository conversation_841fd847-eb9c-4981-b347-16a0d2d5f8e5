"""
Binary Content Extractor
Specialized extractor for binary files with metadata analysis
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Set
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class BinaryExtractor(BaseExtractor):
    """Specialized extractor for binary files"""
    
    def get_supported_extensions(self) -> Set[str]:
        """Get set of supported binary extensions"""
        return {
            # Executables
            '.exe', '.dll', '.so', '.dylib', '.app', '.bin',
            
            # System files
            '.sys', '.drv', '.ocx', '.ax', '.cpl',
            
            # Libraries
            '.lib', '.a', '.o', '.obj', '.pdb',
            
            # Data files
            '.dat', '.data', '.db', '.sqlite', '.sqlite3',
            '.dump', '.core', '.mem',
            
            # Cache files
            '.cache', '.tmp', '.temp', '.swap', '.swp',
            
            # Database files
            '.mdb', '.accdb', '.fdb', '.gdb', '.nsf',
            
            # Compiled files
            '.pyc', '.pyo', '.class', '.dex',
            
            # Font files
            '.ttf', '.otf', '.woff', '.woff2', '.eot'
        }
    
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get mapping of binary extensions to MIME types"""
        return {
            '.exe': 'application/x-msdownload',
            '.dll': 'application/x-msdownload',
            '.so': 'application/x-sharedlib',
            '.dylib': 'application/x-mach-binary',
            '.app': 'application/x-mach-binary',
            '.bin': 'application/octet-stream',
            '.sys': 'application/octet-stream',
            '.drv': 'application/octet-stream',
            '.ocx': 'application/x-msdownload',
            '.ax': 'application/x-msdownload',
            '.cpl': 'application/x-msdownload',
            '.lib': 'application/x-archive',
            '.a': 'application/x-archive',
            '.o': 'application/x-object',
            '.obj': 'application/x-object',
            '.pdb': 'application/x-ms-pdb',
            '.dat': 'application/octet-stream',
            '.data': 'application/octet-stream',
            '.db': 'application/x-sqlite3',
            '.sqlite': 'application/x-sqlite3',
            '.sqlite3': 'application/x-sqlite3',
            '.dump': 'application/octet-stream',
            '.core': 'application/x-core',
            '.mem': 'application/octet-stream',
            '.cache': 'application/octet-stream',
            '.tmp': 'application/octet-stream',
            '.temp': 'application/octet-stream',
            '.swap': 'application/octet-stream',
            '.swp': 'application/octet-stream',
            '.mdb': 'application/x-msaccess',
            '.accdb': 'application/x-msaccess',
            '.fdb': 'application/octet-stream',
            '.gdb': 'application/octet-stream',
            '.nsf': 'application/vnd.lotus-notes',
            '.pyc': 'application/x-python-code',
            '.pyo': 'application/x-python-code',
            '.class': 'application/java-vm',
            '.dex': 'application/octet-stream',
            '.ttf': 'font/ttf',
            '.otf': 'font/otf',
            '.woff': 'font/woff',
            '.woff2': 'font/woff2',
            '.eot': 'application/vnd.ms-fontobject'
        }
    
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract metadata from binary files"""
        try:
            extension = file_path.suffix.lower()
            if extension not in self.get_supported_extensions():
                return None
            
            content_parts = []
            
            # 1. Extract basic file info
            file_info = self._get_file_info(file_path)
            content_parts.append(f"Binary File: {file_info['name']} ({file_info['size_mb']} MB)")
            
            # 2. Analyze binary type
            binary_type = self._analyze_binary_type(file_path, extension)
            if binary_type:
                content_parts.append(f"Type: {binary_type}")
            
            # 3. Extract binary metadata
            binary_metadata = self._extract_binary_metadata(file_path, extension)
            if binary_metadata:
                content_parts.append(binary_metadata)
            
            # 4. Analyze binary purpose
            purpose_analysis = self._analyze_binary_purpose(file_path)
            if purpose_analysis:
                content_parts.append(f"Purpose: {purpose_analysis}")
            
            # 5. Extract filename context
            filename_context = self._analyze_filename_context(file_path)
            if filename_context:
                content_parts.append(f"Context: {filename_context}")
            
            return "\n\n".join(content_parts) if content_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting binary content from {file_path}: {e}")
            return f"Binary file: {file_path.name} (extraction error)"
    
    def _analyze_binary_type(self, file_path: Path, extension: str) -> Optional[str]:
        """Analyze the type of binary file"""
        try:
            type_mappings = {
                '.exe': 'Windows executable',
                '.dll': 'Windows dynamic library',
                '.so': 'Linux shared library',
                '.dylib': 'macOS dynamic library',
                '.app': 'macOS application bundle',
                '.sys': 'System driver',
                '.drv': 'Device driver',
                '.ocx': 'ActiveX control',
                '.ax': 'DirectShow filter',
                '.cpl': 'Control panel applet',
                '.lib': 'Static library',
                '.a': 'Archive library',
                '.o': 'Object file',
                '.obj': 'Object file',
                '.pdb': 'Program database',
                '.db': 'Database file',
                '.sqlite': 'SQLite database',
                '.sqlite3': 'SQLite database',
                '.pyc': 'Python bytecode',
                '.pyo': 'Python optimized bytecode',
                '.class': 'Java bytecode',
                '.dex': 'Android bytecode',
                '.ttf': 'TrueType font',
                '.otf': 'OpenType font',
                '.woff': 'Web font',
                '.woff2': 'Web font v2'
            }
            
            return type_mappings.get(extension)
            
        except Exception as e:
            logger.debug(f"Error analyzing binary type: {e}")
            return None
    
    def _extract_binary_metadata(self, file_path: Path, extension: str) -> Optional[str]:
        """Extract metadata specific to binary type"""
        try:
            metadata_parts = []
            
            # Try to read file header for additional info
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(64)  # Read first 64 bytes
                    
                    # Analyze common binary signatures
                    if header.startswith(b'MZ'):
                        metadata_parts.append("PE/COFF executable format")
                    elif header.startswith(b'\x7fELF'):
                        metadata_parts.append("ELF executable format")
                    elif header.startswith(b'\xfe\xed\xfa'):
                        metadata_parts.append("Mach-O binary format")
                    elif header.startswith(b'SQLite format 3'):
                        metadata_parts.append("SQLite database format")
                    elif header.startswith(b'\x89PNG'):
                        metadata_parts.append("PNG image embedded")
                    elif header.startswith(b'RIFF'):
                        metadata_parts.append("RIFF container format")
            except:
                pass
            
            # Font-specific metadata
            if extension in ['.ttf', '.otf', '.woff', '.woff2']:
                font_metadata = self._extract_font_metadata(file_path)
                if font_metadata:
                    metadata_parts.append(font_metadata)
            
            # Database-specific metadata
            elif extension in ['.db', '.sqlite', '.sqlite3']:
                db_metadata = self._extract_database_metadata(file_path)
                if db_metadata:
                    metadata_parts.append(db_metadata)
            
            return "\n".join(metadata_parts) if metadata_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting binary metadata: {e}")
            return None
    
    def _extract_font_metadata(self, file_path: Path) -> Optional[str]:
        """Extract font-specific metadata"""
        try:
            filename = file_path.stem.lower()
            metadata_parts = []
            
            # Font weight detection
            weights = ['thin', 'light', 'regular', 'medium', 'bold', 'black', 'heavy']
            for weight in weights:
                if weight in filename:
                    metadata_parts.append(f"Weight: {weight.title()}")
                    break
            
            # Font style detection
            if 'italic' in filename:
                metadata_parts.append("Style: Italic")
            elif 'oblique' in filename:
                metadata_parts.append("Style: Oblique")
            
            # Font family detection
            families = ['arial', 'helvetica', 'times', 'courier', 'georgia', 'verdana', 'trebuchet']
            for family in families:
                if family in filename:
                    metadata_parts.append(f"Family: {family.title()}")
                    break
            
            return " | ".join(metadata_parts) if metadata_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting font metadata: {e}")
            return None
    
    def _extract_database_metadata(self, file_path: Path) -> Optional[str]:
        """Extract database-specific metadata"""
        try:
            metadata_parts = []
            
            # Try to connect to SQLite database
            if file_path.suffix.lower() in ['.db', '.sqlite', '.sqlite3']:
                try:
                    import sqlite3
                    conn = sqlite3.connect(str(file_path))
                    cursor = conn.cursor()
                    
                    # Get table count
                    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                    table_count = cursor.fetchone()[0]
                    metadata_parts.append(f"Tables: {table_count}")
                    
                    # Get table names
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5")
                    tables = [row[0] for row in cursor.fetchall()]
                    if tables:
                        metadata_parts.append(f"Sample tables: {', '.join(tables)}")
                    
                    conn.close()
                except:
                    pass
            
            return " | ".join(metadata_parts) if metadata_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting database metadata: {e}")
            return None
    
    def _analyze_binary_purpose(self, file_path: Path) -> Optional[str]:
        """Analyze the likely purpose of the binary file"""
        try:
            filename = file_path.stem.lower()
            extension = file_path.suffix.lower()
            
            purpose_parts = []
            
            # Analyze by filename patterns
            if any(keyword in filename for keyword in ['setup', 'install', 'installer']):
                purpose_parts.append("installer")
            elif any(keyword in filename for keyword in ['update', 'patch', 'hotfix']):
                purpose_parts.append("update/patch")
            elif any(keyword in filename for keyword in ['driver', 'drv']):
                purpose_parts.append("device driver")
            elif any(keyword in filename for keyword in ['service', 'daemon', 'svc']):
                purpose_parts.append("system service")
            elif any(keyword in filename for keyword in ['plugin', 'addon', 'extension']):
                purpose_parts.append("plugin/extension")
            elif any(keyword in filename for keyword in ['lib', 'library']):
                purpose_parts.append("code library")
            elif any(keyword in filename for keyword in ['cache', 'temp', 'tmp']):
                purpose_parts.append("temporary/cache file")
            elif any(keyword in filename for keyword in ['config', 'settings', 'prefs']):
                purpose_parts.append("configuration data")
            elif any(keyword in filename for keyword in ['backup', 'bak']):
                purpose_parts.append("backup file")
            
            return ", ".join(purpose_parts) if purpose_parts else None
            
        except Exception as e:
            logger.debug(f"Error analyzing binary purpose: {e}")
            return None
