<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">22%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d___init___py.html">semantic_fs\__init__.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t47">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t47"><data value='security_middleware'>security_middleware</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t99">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t99"><data value='get_semantic_fs'>get_semantic_fs</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t107">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t107"><data value='startup_event'>startup_event</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t134">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t134"><data value='create_token'>create_token</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t162">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t162"><data value='get_current_user_info'>get_current_user_info</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t174">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t174"><data value='get_security_status'>get_security_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t187">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t187"><data value='root'>root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t681">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t681"><data value='get_stats'>get_stats</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t721">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t721"><data value='search_files'>search_files</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t781">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t781"><data value='index_file'>index_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t796">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t796"><data value='index_directory'>index_directory</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t803">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t803"><data value='index_dir'>index_directory.index_dir</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t829">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t829"><data value='list_files'>list_files</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t895">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html#t895"><data value='delete_file'>delete_file</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_api_py.html">semantic_fs\api.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="43 45">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t19">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t19"><data value='init__'>MemoryCache.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t28">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t28"><data value='is_expired'>MemoryCache._is_expired</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t32">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t32"><data value='get'>MemoryCache.get</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t53">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t53"><data value='set'>MemoryCache.set</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t71">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t71"><data value='delete'>MemoryCache.delete</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t85">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t85"><data value='clear'>MemoryCache.clear</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t89">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t89"><data value='cleanup_expired'>MemoryCache.cleanup_expired</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t107">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t107"><data value='get_stats'>MemoryCache.get_stats</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t127">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t127"><data value='init__'>QueryCache.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t136">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t136"><data value='make_query_key'>QueryCache._make_query_key</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t157">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t157"><data value='get_query_result'>QueryCache.get_query_result</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t176">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t176"><data value='set_query_result'>QueryCache.set_query_result</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t191">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t191"><data value='invalidate_all_queries'>QueryCache.invalidate_all_queries</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t209">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t209"><data value='init__'>EmbeddingCache.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t218">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t218"><data value='make_embedding_key'>EmbeddingCache._make_embedding_key</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t231">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t231"><data value='get_embedding'>EmbeddingCache.get_embedding</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t243">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t243"><data value='set_embedding'>EmbeddingCache.set_embedding</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t259">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t259"><data value='get_memory_cache'>get_memory_cache</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t263">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t263"><data value='get_query_cache'>get_query_cache</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t267">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t267"><data value='get_embedding_cache'>get_embedding_cache</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t271">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t271"><data value='cache_query_result'>cache_query_result</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t277">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t277"><data value='decorator'>cache_query_result.decorator</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t279">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t279"><data value='wrapper'>cache_query_result.decorator.wrapper</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t302">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t302"><data value='cache_embedding'>cache_embedding</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t308">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t308"><data value='decorator'>cache_embedding.decorator</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t310">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html#t310"><data value='wrapper'>cache_embedding.decorator.wrapper</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html">semantic_fs\cache.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cache_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t30">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t30"><data value='cli'>cli</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t54">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t54"><data value='search'>search</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t134">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t134"><data value='index'>index</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t200">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t200"><data value='list'>list</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t264">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t264"><data value='cache'>cache</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t290">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t290"><data value='stats'>stats</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t333">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t333"><data value='serve'>serve</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t356">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t356"><data value='watch'>watch</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t423">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t423"><data value='open'>open</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t472">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t472"><data value='delete'>delete</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t502">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t502"><data value='config'>config</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t522">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t522"><data value='db'>db</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t527">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t527"><data value='init'>init</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t548">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t548"><data value='status'>status</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t575">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t575"><data value='migrate'>migrate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t592">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t592"><data value='upgrade'>upgrade</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t609">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t609"><data value='logs'>logs</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t614">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t614"><data value='status'>status</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t650">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t650"><data value='tail'>tail</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t687">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t687"><data value='clear'>clear</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t716">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t716"><data value='test'>test</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t725">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t725"><data value='run'>run</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t773">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t773"><data value='install'>install</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t801">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t801"><data value='coverage'>coverage</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t827">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html#t827"><data value='lint'>lint</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html">semantic_fs\cli.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>93</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t75">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t75"><data value='validate_vector_db_path'>Settings.validate_vector_db_path</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t87">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t87"><data value='validate_extensions'>Settings.validate_extensions</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t97">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t97"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t108">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html#t108"><data value='validate_dependencies'>validate_dependencies</data></a></td>
                <td>35</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="22 35">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_config_py.html">semantic_fs\config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>57</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="54 57">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t49">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t49"><data value='init__'>ContentExtractor.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t80">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t80"><data value='extract_content'>ContentExtractor.extract_content</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t122">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t122"><data value='get_mime_from_extension'>ContentExtractor._get_mime_from_extension</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t144">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t144"><data value='extract_text'>ContentExtractor._extract_text</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t153">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t153"><data value='extract_json'>ContentExtractor._extract_json</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t164">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t164"><data value='extract_csv'>ContentExtractor._extract_csv</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t180">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t180"><data value='extract_xml'>ContentExtractor._extract_xml</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t197">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t197"><data value='extract_html'>ContentExtractor._extract_html</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t218">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t218"><data value='extract_pdf'>ContentExtractor._extract_pdf</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t238">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t238"><data value='extract_docx'>ContentExtractor._extract_docx</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t257">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t257"><data value='extract_excel'>ContentExtractor._extract_excel</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t281">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t281"><data value='extract_image_ocr'>ContentExtractor._extract_image_ocr</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t295">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html#t295"><data value='clean_content'>ContentExtractor._clean_content</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html">semantic_fs\content_extractor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_content_extractor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="36 44">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t37">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t37"><data value='new__'>SemanticFilesystem.__new__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t43">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t43"><data value='init__'>SemanticFilesystem.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t67">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t67"><data value='index_file'>SemanticFilesystem.index_file</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t202">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t202"><data value='query'>SemanticFilesystem.query</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t319">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t319"><data value='add_context_relationships'>SemanticFilesystem._add_context_relationships</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t332">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t332"><data value='combine_and_rank_results'>SemanticFilesystem._combine_and_rank_results</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t393">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html#t393"><data value='calculate_context_score'>SemanticFilesystem._calculate_context_score</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_core_py.html">semantic_fs\core.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t25">semantic_fs\database.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t25"><data value='create_tables'>create_tables</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t30">semantic_fs\database.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t30"><data value='initialize_database'>initialize_database</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t43">semantic_fs\database.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t43"><data value='get_db'>get_db</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t55">semantic_fs\database.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_database_py.html#t55"><data value='get_db_session'>get_db_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_database_py.html">semantic_fs\database.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t20">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t20"><data value='init__'>EmbeddingEngine.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t45">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t45"><data value='generate_embedding'>EmbeddingEngine.generate_embedding</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t50">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t50"><data value='generate_embedding_id'>EmbeddingEngine.generate_embedding_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t55">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t55"><data value='store_embedding'>EmbeddingEngine.store_embedding</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t86">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t86"><data value='search_similar'>EmbeddingEngine.search_similar</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t125">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t125"><data value='delete_embedding'>EmbeddingEngine.delete_embedding</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t133">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html#t133"><data value='update_embedding'>EmbeddingEngine.update_embedding</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html">semantic_fs\embedding_engine.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_embedding_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t7">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t7"><data value='init__'>SemanticFilesystemError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t15">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t15"><data value='init__'>FileProcessingError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t29">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t29"><data value='init__'>ContentExtractionError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t40">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t40"><data value='init__'>EmbeddingError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t54">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t54"><data value='init__'>SearchError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t68">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t68"><data value='init__'>DatabaseError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t82">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t82"><data value='init__'>ConfigurationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t96">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t96"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t110">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t110"><data value='init__'>DependencyError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t124">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t124"><data value='init__'>FileSystemError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t140">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t140"><data value='init__'>LLMError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t156">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t156"><data value='init__'>CacheError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t194">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t194"><data value='handle_errors'>handle_errors</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t212">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t212"><data value='decorator'>handle_errors.decorator</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t214">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t214"><data value='wrapper'>handle_errors.decorator.wrapper</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t253">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t253"><data value='log_error'>log_error</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t293">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html#t293"><data value='create_user_friendly_error'>create_user_friendly_error</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html">semantic_fs\exceptions.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t41">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t41"><data value='init__'>SemanticFileHandler.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t67">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t67"><data value='stop'>SemanticFileHandler.stop</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t80">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t80"><data value='is_supported_file'>SemanticFileHandler._is_supported_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t95">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t95"><data value='schedule_file_processing'>SemanticFileHandler._schedule_file_processing</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t102">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t102"><data value='process_pending_files'>SemanticFileHandler._process_pending_files</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t140">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t140"><data value='process_file'>SemanticFileHandler._process_file</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t168">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t168"><data value='remove_file_from_index'>SemanticFileHandler._remove_file_from_index</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t195">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t195"><data value='on_created'>SemanticFileHandler.on_created</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t200">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t200"><data value='on_modified'>SemanticFileHandler.on_modified</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t205">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t205"><data value='on_deleted'>SemanticFileHandler.on_deleted</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t213">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t213"><data value='init__'>FileMonitor.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t231">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t231"><data value='safe_operation'>FileMonitor._safe_operation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t241">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t241"><data value='add_watch_path'>FileMonitor.add_watch_path</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t279">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t279"><data value='remove_watch_path'>FileMonitor.remove_watch_path</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t310">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t310"><data value='start'>FileMonitor.start</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t337">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t337"><data value='stop'>FileMonitor.stop</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t370">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html#t370"><data value='get_status'>FileMonitor.get_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html">semantic_fs\file_monitor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_file_monitor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="43 47">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t25">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t25"><data value='init__'>LLMProcessor.__init__</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t50">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t50"><data value='check_ollama_availability'>LLMProcessor._check_ollama_availability</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t79">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t79"><data value='call_ollama'>LLMProcessor._call_ollama</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t107">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t107"><data value='call_llm'>LLMProcessor._call_llm</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t143">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t143"><data value='interpret_query'>LLMProcessor.interpret_query</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t201">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t201"><data value='generate_summary'>LLMProcessor.generate_summary</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t230">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t230"><data value='generate_tags'>LLMProcessor.generate_tags</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t271">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t271"><data value='process_temporal_filter'>LLMProcessor._process_temporal_filter</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t299">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t299"><data value='fallback_query_interpretation'>LLMProcessor._fallback_query_interpretation</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t360">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t360"><data value='fallback_summary'>LLMProcessor._fallback_summary</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t383">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html#t383"><data value='fallback_tags'>LLMProcessor._fallback_tags</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html">semantic_fs\llm_processor.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_llm_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="24 25">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t23">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t23"><data value='init__'>StructuredFormatter.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t29">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t29"><data value='format'>StructuredFormatter.format</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t91">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t91"><data value='format'>ColoredConsoleFormatter.format</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t121">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t121"><data value='init__'>LoggingManager.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t126">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t126"><data value='get_logging_config'>LoggingManager.get_logging_config</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t242">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t242"><data value='setup_logging'>LoggingManager.setup_logging</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t282">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t282"><data value='get_log_files'>LoggingManager.get_log_files</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t289">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t289"><data value='get_log_stats'>LoggingManager.get_log_stats</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t318">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t318"><data value='get_logging_manager'>get_logging_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t326">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t326"><data value='setup_logging'>setup_logging</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t343">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html#t343"><data value='get_log_stats'>get_log_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html">semantic_fs\logging_config.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t29">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t29"><data value='init__'>MigrationManager.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t44">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t44"><data value='get_current_revision'>MigrationManager.get_current_revision</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t54">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t54"><data value='get_head_revision'>MigrationManager.get_head_revision</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t63">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t63"><data value='is_database_up_to_date'>MigrationManager.is_database_up_to_date</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t74">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t74"><data value='get_pending_migrations'>MigrationManager.get_pending_migrations</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t97">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t97"><data value='create_initial_migration'>MigrationManager.create_initial_migration</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t122">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t122"><data value='create_migration'>MigrationManager.create_migration</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t140">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t140"><data value='upgrade_database'>MigrationManager.upgrade_database</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t154">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t154"><data value='downgrade_database'>MigrationManager.downgrade_database</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t168">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t168"><data value='get_migration_history'>MigrationManager.get_migration_history</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t188">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t188"><data value='initialize_database'>MigrationManager.initialize_database</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t232">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t232"><data value='auto_upgrade'>MigrationManager.auto_upgrade</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t246">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t246"><data value='get_status'>MigrationManager.get_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t261">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t261"><data value='get_migration_manager'>get_migration_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t269">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t269"><data value='initialize_database'>initialize_database</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t274">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t274"><data value='auto_upgrade_database'>auto_upgrade_database</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t279">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t279"><data value='create_migration'>create_migration</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t284">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t284"><data value='upgrade_database'>upgrade_database</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t289">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html#t289"><data value='get_migration_status'>get_migration_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html">semantic_fs\migrations.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_migrations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_models_py.html">semantic_fs\models.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="61 61">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t33">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t33"><data value='init__'>SecuritySettings.__init__</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t56">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t56"><data value='generate_secret_key'>SecuritySettings._generate_secret_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t60">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t60"><data value='load_api_keys'>SecuritySettings._load_api_keys</data></a></td>
                <td>12</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="8 12">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t110">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t110"><data value='init__'>RateLimiter.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t115">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t115"><data value='is_allowed'>RateLimiter.is_allowed</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t132">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t132"><data value='get_stats'>RateLimiter.get_stats</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t159">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t159"><data value='init__'>SecurityManager.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t162">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t162"><data value='create_access_token'>SecurityManager.create_access_token</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t174">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t174"><data value='create_refresh_token'>SecurityManager.create_refresh_token</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t186">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t186"><data value='verify_token'>SecurityManager.verify_token</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t202">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t202"><data value='verify_api_key'>SecurityManager.verify_api_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t208">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t208"><data value='get_client_id'>SecurityManager.get_client_id</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t224">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t224"><data value='get_current_user'>get_current_user</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t262">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t262"><data value='require_auth'>require_auth</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t264">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t264"><data value='decorator'>require_auth.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t266">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t266"><data value='wrapper'>require_auth.decorator.wrapper</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t291">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t291"><data value='rate_limit_middleware'>rate_limit_middleware</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t321">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html#t321"><data value='add_security_headers'>add_security_headers</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_security_py.html">semantic_fs\security.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t32">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t32"><data value='init__'>SignalManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t38">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t38"><data value='add_shutdown_handler'>SignalManager.add_shutdown_handler</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t49">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t49"><data value='remove_shutdown_handler'>SignalManager.remove_shutdown_handler</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t60">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t60"><data value='register_signal_handlers'>SignalManager.register_signal_handlers</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t62">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t62"><data value='signal_handler'>SignalManager.register_signal_handlers.signal_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t85">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t85"><data value='register_windows_handlers'>SignalManager._register_windows_handlers</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t87">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t87"><data value='windows_console_handler'>SignalManager._register_windows_handlers.windows_console_handler</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t113">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t113"><data value='shutdown'>SignalManager.shutdown</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t133">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t133"><data value='restore_signal_handlers'>SignalManager.restore_signal_handlers</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t145">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t145"><data value='managed_shutdown'>SignalManager.managed_shutdown</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t158">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t158"><data value='get_signal_manager'>get_signal_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t166">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t166"><data value='register_shutdown_handler'>register_shutdown_handler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t175">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t175"><data value='setup_signal_handling'>setup_signal_handling</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t183">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t183"><data value='signal_handling'>signal_handling</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t189">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t189"><data value='is_shutdown_initiated'>is_shutdown_initiated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t194">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html#t194"><data value='initiate_shutdown'>initiate_shutdown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html">semantic_fs\signal_handler.py</a></td>
                <td class="name left"><a href="z_48e1513074be697d_signal_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="32 36">89%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2605</td>
                <td>2033</td>
                <td>0</td>
                <td class="right" data-ratio="572 2605">22%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-08-06 18:02 -0500
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
