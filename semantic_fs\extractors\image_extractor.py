"""
Image Content Extractor
Specialized extractor for image files with OCR and metadata analysis
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Set
from .base_extractor import BaseExtractor

# Image processing
try:
    from PIL import Image, ExifTags
    from PIL.ExifTags import TAGS
    HAS_PIL = True
except ImportError:
    HAS_PIL = False

# OCR processing
try:
    import pytesseract
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

# Advanced image analysis
try:
    import cv2
    import numpy as np
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False

logger = logging.getLogger(__name__)


class ImageExtractor(BaseExtractor):
    """Specialized extractor for image files with OCR and contextual analysis"""
    
    def __init__(self):
        """Initialize the image extractor"""
        super().__init__()
        
        # OCR configuration for better accuracy
        self.ocr_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?@#$%^&*()_+-=[]{}|;:,.<>?/~`'
        
        # Image analysis thresholds
        self.min_text_confidence = 30  # Minimum OCR confidence
        self.min_text_length = 3       # Minimum text length to consider
    
    def get_supported_extensions(self) -> Set[str]:
        """Get set of supported image extensions"""
        return {
            # Common image formats
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', 
            '.webp', '.svg', '.ico',
            
            # Professional formats
            '.psd', '.ai', '.eps', '.pdf',
            
            # Raw camera formats
            '.raw', '.cr2', '.nef', '.arw', '.dng', '.orf', '.rw2', '.pef', '.srw'
        }
    
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get mapping of image extensions to MIME types"""
        return {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff',
            '.webp': 'image/webp',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
            '.psd': 'image/vnd.adobe.photoshop',
            '.ai': 'application/postscript',
            '.eps': 'application/postscript',
            '.raw': 'image/x-panasonic-raw',
            '.cr2': 'image/x-canon-cr2',
            '.nef': 'image/x-nikon-nef',
            '.arw': 'image/x-sony-arw',
            '.dng': 'image/x-adobe-dng',
            '.orf': 'image/x-olympus-orf',
            '.rw2': 'image/x-panasonic-rw2',
            '.pef': 'image/x-pentax-pef',
            '.srw': 'image/x-samsung-srw'
        }
    
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract comprehensive content from image file"""
        if not HAS_PIL:
            return f"Image file: {file_path.name} (PIL not available for processing)"
        
        try:
            extension = file_path.suffix.lower()
            if extension not in self.get_supported_extensions():
                return None
            
            content_parts = []
            
            # 1. Extract basic file info
            file_info = self._get_file_info(file_path)
            content_parts.append(f"Image File: {file_info['name']} ({file_info['size_mb']} MB)")
            
            # 2. Extract EXIF metadata
            metadata = self._extract_metadata(file_path)
            if metadata:
                content_parts.append(f"Metadata: {metadata}")
            
            # 3. Extract OCR text
            if HAS_OCR:
                ocr_text = self._extract_ocr_text(file_path)
                if ocr_text:
                    content_parts.append(f"Text Content: {ocr_text}")
            
            # 4. Extract visual context
            visual_context = self._analyze_visual_context(file_path)
            if visual_context:
                content_parts.append(f"Visual Analysis: {visual_context}")
            
            # 5. Extract filename context
            filename_context = self._analyze_filename_context(file_path)
            if filename_context:
                content_parts.append(f"Context: {filename_context}")
            
            if content_parts:
                return "\n\n".join(content_parts)
            else:
                return f"Image file: {file_path.name} (no extractable content)"
                
        except Exception as e:
            logger.error(f"Error extracting content from image {file_path}: {e}")
            return f"Image file: {file_path.name} (extraction error)"
    
    def _extract_metadata(self, file_path: Path) -> Optional[str]:
        """Extract EXIF and other metadata from image"""
        try:
            with Image.open(file_path) as image:
                metadata_parts = []
                
                # Basic image info
                metadata_parts.append(f"Format: {image.format}")
                metadata_parts.append(f"Size: {image.size[0]}x{image.size[1]}")
                metadata_parts.append(f"Mode: {image.mode}")
                
                # EXIF data
                try:
                    exif_data = image._getexif()
                    if exif_data:
                        exif_info = []
                        for tag_id, value in exif_data.items():
                            tag = TAGS.get(tag_id, tag_id)
                            if isinstance(tag, str) and isinstance(value, (str, int, float)):
                                # Filter useful metadata
                                if tag in ['DateTime', 'DateTimeOriginal', 'Make', 'Model', 'Software', 
                                         'Artist', 'Copyright', 'ImageDescription', 'UserComment']:
                                    exif_info.append(f"{tag}: {value}")
                        
                        if exif_info:
                            metadata_parts.extend(exif_info)
                except:
                    pass  # EXIF not available or corrupted
                
                return " | ".join(metadata_parts) if metadata_parts else None
                
        except Exception as e:
            logger.debug(f"Error extracting metadata from {file_path}: {e}")
            return None
    
    def _extract_ocr_text(self, file_path: Path) -> Optional[str]:
        """Extract text using OCR with preprocessing"""
        try:
            # Open and preprocess image for better OCR
            with Image.open(file_path) as image:
                # Convert to RGB if necessary
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # Enhance image for OCR if OpenCV is available
                if HAS_CV2:
                    image = self._preprocess_for_ocr(image)
                
                # Perform OCR with configuration
                text = pytesseract.image_to_string(image, config=self.ocr_config)
                
                # Clean and validate text
                if text:
                    text = self._clean_ocr_text(text)
                    if len(text) >= self.min_text_length:
                        return text
                
                return None
                
        except Exception as e:
            logger.debug(f"Error performing OCR on {file_path}: {e}")
            return None
    
    def _preprocess_for_ocr(self, pil_image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR accuracy using OpenCV"""
        try:
            # Convert PIL to OpenCV format
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Convert back to PIL
            return Image.fromarray(thresh)
            
        except Exception as e:
            logger.debug(f"Error preprocessing image: {e}")
            return pil_image
    
    def _clean_ocr_text(self, text: str) -> str:
        """Clean and normalize OCR text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # Remove very short "words" that are likely OCR errors
        words = text.split()
        cleaned_words = [word for word in words if len(word) >= 2 or word.isdigit()]
        
        return ' '.join(cleaned_words)
    
    def _analyze_visual_context(self, file_path: Path) -> Optional[str]:
        """Analyze visual context and characteristics"""
        try:
            with Image.open(file_path) as image:
                context_parts = []
                
                # Analyze image characteristics
                width, height = image.size
                aspect_ratio = width / height
                
                # Determine image type based on characteristics
                if aspect_ratio > 2.0:
                    context_parts.append("panoramic or banner image")
                elif aspect_ratio < 0.5:
                    context_parts.append("tall or portrait image")
                elif 0.9 <= aspect_ratio <= 1.1:
                    context_parts.append("square image")
                
                # Analyze size category
                total_pixels = width * height
                if total_pixels > 2000000:  # > 2MP
                    context_parts.append("high resolution")
                elif total_pixels < 100000:  # < 0.1MP
                    context_parts.append("thumbnail or icon")
                
                # Analyze color information
                if image.mode == 'L':
                    context_parts.append("grayscale")
                elif image.mode == 'RGBA':
                    context_parts.append("with transparency")
                
                return ", ".join(context_parts) if context_parts else None
                
        except Exception as e:
            logger.debug(f"Error analyzing visual context of {file_path}: {e}")
            return None
    
    def _analyze_filename_context(self, file_path: Path) -> Optional[str]:
        """Analyze filename for image-specific context"""
        try:
            filename = file_path.stem.lower()
            context_parts = []
            
            # Image-specific naming patterns
            patterns = {
                'screenshot': ['screenshot', 'screen', 'capture', 'snap'],
                'photo': ['photo', 'img', 'pic', 'picture', 'image'],
                'logo': ['logo', 'brand', 'icon', 'symbol'],
                'diagram': ['diagram', 'chart', 'graph', 'flow', 'schema'],
                'document': ['doc', 'page', 'scan', 'pdf', 'text'],
                'ui': ['ui', 'interface', 'mockup', 'wireframe', 'design'],
                'avatar': ['avatar', 'profile', 'user', 'person', 'face'],
                'banner': ['banner', 'header', 'hero', 'cover', 'background']
            }
            
            for category, keywords in patterns.items():
                if any(keyword in filename for keyword in keywords):
                    context_parts.append(f"likely {category}")
                    break
            
            # Check for date patterns
            import re
            if re.search(r'\d{4}[-_]\d{2}[-_]\d{2}', filename):
                context_parts.append("dated content")
            
            # Check parent directory for context
            parent_dir = file_path.parent.name.lower()
            if parent_dir in ['screenshots', 'photos', 'images', 'pics', 'documents', 'downloads']:
                context_parts.append(f"from {parent_dir} folder")
            
            return ", ".join(context_parts) if context_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting filename context from {file_path}: {e}")
            return None
