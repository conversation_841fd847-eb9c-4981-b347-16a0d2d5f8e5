"""
LLM-Powered Semantic Filesystem

A filesystem that organizes files by concepts and semantic meaning,
allowing natural language queries like:
"show me the pdf of that paper I used in my youtube video on PTX 6 months ago"
"""

__version__ = "0.1.0"
__author__ = "Semantic FS Team"

from .core import SemanticFilesystem
from .models import SemanticQuery, QueryResult, SearchResult, FileMetadata
from .config import settings

__all__ = [
    "SemanticFilesystem",
    "SemanticQuery",
    "QueryResult",
    "SearchResult",
    "FileMetadata",
    "settings"
]
