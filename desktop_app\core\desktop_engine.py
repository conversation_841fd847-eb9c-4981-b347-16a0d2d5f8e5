"""
Desktop-optimized semantic filesystem engine for massive scale file indexing
Designed to handle 1TB+ datasets with 100,000+ files efficiently
"""

import os
import sys
import time
import logging
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Generator, Set
from dataclasses import dataclass
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import psutil

# Add parent directory to path to import from main semantic_fs
sys.path.append(str(Path(__file__).parent.parent.parent))

from semantic_fs.core import SemanticFilesystem
from semantic_fs.models import FileRecord
from semantic_fs.config import Settings
from semantic_fs.database import get_db
from semantic_fs.resource_manager import get_resource_manager

logger = logging.getLogger(__name__)


@dataclass
class IndexingProgress:
    """Progress tracking for massive indexing operations"""
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    current_file: str = ""
    start_time: float = 0
    estimated_completion: float = 0
    bytes_processed: int = 0
    files_per_second: float = 0
    
    @property
    def progress_percentage(self) -> float:
        if self.total_files == 0:
            return 0.0
        return (self.processed_files / self.total_files) * 100
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time if self.start_time > 0 else 0


class DesktopSemanticEngine:
    """Desktop-optimized semantic filesystem engine"""
    
    def __init__(self, config_path: Optional[Path] = None):
        """Initialize desktop engine with massive scale optimizations"""
        self.config_path = config_path or Path.home() / ".semantic_fs"
        self.config_path.mkdir(exist_ok=True)
        
        # Initialize core engine
        self.core_engine = SemanticFilesystem()
        
        # Desktop-specific settings
        self.max_workers = min(32, (os.cpu_count() or 1) * 4)  # Aggressive threading
        self.batch_size = 100  # Process files in batches
        self.memory_limit_gb = 8  # Memory usage limit
        
        # Progress tracking
        self.indexing_progress = IndexingProgress()
        self.is_indexing = False
        self.indexing_thread: Optional[threading.Thread] = None
        
        # Performance monitoring
        self.resource_manager = get_resource_manager()
        
        # Excluded directories (performance optimization)
        self.excluded_dirs = {
            'node_modules', '.git', '.svn', '__pycache__', '.pytest_cache',
            'venv', 'env', '.venv', 'build', 'dist', '.tox',
            'Windows', 'Program Files', 'Program Files (x86)',  # Windows system dirs
            '$Recycle.Bin', 'System Volume Information',
            '.Trash', '.Trashes'  # Trash directories
        }
        
        # Supported file extensions (optimized list)
        self.supported_extensions = {
            # Documents & Text
            '.txt', '.md', '.markdown', '.mdown', '.mkd', '.mdx', '.rst', '.asciidoc', '.adoc', '.tex', '.latex',

            # Office Documents
            '.pdf', '.docx', '.doc', '.rtf', '.odt', '.pages', '.xlsx', '.xls', '.csv', '.ods', '.numbers', '.pptx', '.ppt', '.odp', '.key',

            # Python
            '.py', '.pyw', '.pyi', '.pyx',

            # JavaScript/TypeScript
            '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs', '.map',

            # Web
            '.html', '.htm', '.xhtml', '.shtml', '.css', '.scss', '.sass', '.less', '.styl',

            # Systems Programming
            '.c', '.h', '.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.rs', '.rlib', '.go', '.mod', '.sum',

            # JVM Languages
            '.java', '.class', '.jar', '.scala', '.sc', '.kt', '.kts', '.clj', '.cljs', '.cljc',

            # Other Languages
            '.cs', '.vb', '.fs', '.php', '.rb', '.swift', '.hs', '.ml', '.r', '.lua', '.pl', '.pm',

            # Shell & Scripts
            '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh', '.ps1', '.psm1', '.psd1', '.bat', '.cmd',

            # Data & Config
            '.json', '.jsonl', '.json5', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.config', '.properties', '.env',

            # Database
            '.sql', '.mysql', '.pgsql', '.sqlite', '.db', '.cql', '.cypher', '.sparql', '.graphql', '.gql',

            # Logs & Debug
            '.log', '.logs', '.out', '.err', '.trace', '.debug', '.dump', '.dmp',

            # Special Files
            '.readme', '.license', '.changelog', '.authors', '.makefile', '.dockerfile', '.gitignore',

            # Images (with OCR support)
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg',
            '.ico', '.psd', '.ai', '.eps', '.raw', '.cr2', '.nef', '.arw', '.dng',

            # Specialized & Data formats
            '.pkl', '.pickle', '.joblib', '.npy', '.npz', '.mat', '.h5', '.hdf5',
            '.parquet', '.avro', '.orc', '.feather', '.arrow', '.msgpack',
            '.protobuf', '.pb', '.proto', '.thrift', '.capnp',

            # Web & Font formats
            '.woff', '.woff2', '.ttf', '.otf', '.eot', '.svg', '.webmanifest',
            '.htaccess', '.htpasswd', '.robots', '.sitemap',

            # Archive formats (metadata extraction)
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.lz4', '.zst',

            # Binary data with metadata
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat', '.dump', '.core',

            # Specialized text formats
            '.pod', '.man', '.info', '.help', '.1', '.2', '.3', '.4', '.5', '.6', '.7', '.8', '.9',
            '.patch', '.diff', '.rej', '.orig', '.bak', '.swp', '.tmp',

            # Proprietary & Very Specialized formats
            '.flt', '.venc', '.idx', '.meta', '.desc', '.spec', '.def', '.ref',
            '.cache', '.index', '.catalog', '.manifest', '.registry', '.db-journal',
            '.sqlite-wal', '.sqlite-shm', '.lck', '.lock', '.pid', '.guid',
            '.uuid', '.hash', '.checksum', '.crc', '.md5', '.sha1', '.sha256',
            '.sig', '.signature', '.cert', '.key', '.pem', '.crt', '.p12',
            '.jks', '.keystore', '.truststore', '.pfx', '.der', '.csr',
            '.license-key', '.activation', '.serial', '.token', '.session'

        }
        
        logger.info(f"Desktop engine initialized with {self.max_workers} workers")
    
    def discover_drives(self) -> List[Path]:
        """Discover all available drives for indexing"""
        drives = []
        
        if os.name == 'nt':  # Windows
            import string
            for letter in string.ascii_uppercase:
                drive = Path(f"{letter}:\\")
                if drive.exists():
                    drives.append(drive)
        else:  # Unix-like
            drives.append(Path("/"))
        
        logger.info(f"Discovered drives: {[str(d) for d in drives]}")
        return drives
    
    def estimate_indexing_scope(self, paths: List[Path]) -> Dict[str, Any]:
        """Estimate the scope of indexing operation"""
        total_files = 0
        total_size = 0
        file_types = {}
        
        logger.info("Estimating indexing scope...")
        
        for path in paths:
            if not path.exists():
                continue
                
            try:
                for file_path in self._walk_files(path, estimate_only=True):
                    if self._should_index_file(file_path):
                        total_files += 1
                        try:
                            size = file_path.stat().st_size
                            total_size += size
                            
                            ext = file_path.suffix.lower()
                            file_types[ext] = file_types.get(ext, 0) + 1
                            
                        except (OSError, PermissionError):
                            continue
                            
                        # Progress update every 1000 files
                        if total_files % 1000 == 0:
                            logger.info(f"Estimated {total_files} files so far...")
                            
            except Exception as e:
                logger.warning(f"Error estimating scope for {path}: {e}")
        
        scope = {
            'total_files': total_files,
            'total_size_gb': total_size / (1024**3),
            'file_types': file_types,
            'estimated_time_hours': self._estimate_indexing_time(total_files, total_size)
        }
        
        logger.info(f"Indexing scope: {total_files} files, {scope['total_size_gb']:.2f} GB")
        return scope
    
    def _estimate_indexing_time(self, file_count: int, total_size: int) -> float:
        """Estimate indexing time based on file count and size"""
        # Rough estimates based on performance testing
        files_per_second = 50  # Conservative estimate
        bytes_per_second = 10 * 1024 * 1024  # 10 MB/s
        
        time_by_count = file_count / files_per_second
        time_by_size = total_size / bytes_per_second
        
        # Use the larger estimate (bottleneck)
        estimated_seconds = max(time_by_count, time_by_size)
        return estimated_seconds / 3600  # Convert to hours
    
    def start_full_indexing(self, paths: List[Path], progress_callback=None) -> bool:
        """Start full indexing of specified paths"""
        if self.is_indexing:
            logger.warning("Indexing already in progress")
            return False
        
        self.is_indexing = True
        self.indexing_progress = IndexingProgress()
        self.indexing_progress.start_time = time.time()
        
        # Start indexing in background thread
        self.indexing_thread = threading.Thread(
            target=self._indexing_worker,
            args=(paths, progress_callback),
            daemon=True
        )
        self.indexing_thread.start()
        
        logger.info("Full indexing started")
        return True
    
    def _indexing_worker(self, paths: List[Path], progress_callback=None):
        """Background worker for indexing operation"""
        try:
            # First pass: count total files
            logger.info("Counting files for progress tracking...")
            total_files = 0
            for path in paths:
                for _ in self._walk_files(path, estimate_only=True):
                    if self._should_index_file(_):
                        total_files += 1
            
            self.indexing_progress.total_files = total_files
            logger.info(f"Will index {total_files} files")
            
            # Second pass: actual indexing
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                batch = []
                
                for path in paths:
                    for file_path in self._walk_files(path):
                        if not self._should_index_file(file_path):
                            continue
                        
                        batch.append(file_path)
                        
                        # Process in batches
                        if len(batch) >= self.batch_size:
                            future = executor.submit(self._index_batch, batch.copy())
                            futures.append(future)
                            batch.clear()
                            
                            # Memory management
                            self._check_memory_usage()
                
                # Process remaining files
                if batch:
                    future = executor.submit(self._index_batch, batch)
                    futures.append(future)
                
                # Wait for completion and track progress
                for future in as_completed(futures):
                    try:
                        batch_results = future.result()
                        self.indexing_progress.processed_files += len(batch_results)
                        
                        # Update progress
                        if progress_callback:
                            progress_callback(self.indexing_progress)
                        
                        # Log progress every 1000 files
                        if self.indexing_progress.processed_files % 1000 == 0:
                            logger.info(f"Indexed {self.indexing_progress.processed_files}/{total_files} files")
                            
                    except Exception as e:
                        logger.error(f"Batch indexing failed: {e}")
                        self.indexing_progress.failed_files += 1
        
        except Exception as e:
            logger.error(f"Indexing worker failed: {e}")
        
        finally:
            self.is_indexing = False
            logger.info("Indexing completed")
    
    def _walk_files(self, root_path: Path, estimate_only: bool = False) -> Generator[Path, None, None]:
        """Walk directory tree efficiently, skipping excluded directories"""
        try:
            for dirpath, dirnames, filenames in os.walk(root_path):
                # Skip excluded directories
                dirnames[:] = [d for d in dirnames if d not in self.excluded_dirs]
                
                current_dir = Path(dirpath)
                
                # Skip system directories
                if self._is_system_directory(current_dir):
                    continue
                
                for filename in filenames:
                    file_path = current_dir / filename
                    
                    # Basic checks
                    if not file_path.is_file():
                        continue
                    
                    yield file_path
                    
        except (PermissionError, OSError) as e:
            logger.warning(f"Cannot access {root_path}: {e}")
    
    def _should_index_file(self, file_path: Path) -> bool:
        """Determine if file should be indexed"""
        try:
            # Check extension
            if file_path.suffix.lower() not in self.supported_extensions:
                return False
            
            # Check file size (skip very large files for now)
            stat = file_path.stat()
            if stat.st_size > 100 * 1024 * 1024:  # 100MB limit
                return False
            
            if stat.st_size == 0:  # Skip empty files
                return False
            
            # Check if already indexed (optimization)
            # TODO: Implement incremental indexing
            
            return True
            
        except (OSError, PermissionError):
            return False
    
    def _is_system_directory(self, path: Path) -> bool:
        """Check if directory is a system directory to skip"""
        path_str = str(path).lower()

        # Only skip actual Windows system directories, not temp directories during testing
        system_patterns = [
            'windows\\system32', 'windows\\syswow64', 'windows\\winsxs',
            'program files\\windows', 'programdata\\microsoft',
            'appdata\\local\\microsoft',  # Removed temp to allow testing
            '$recycle.bin', 'system volume information'
        ]

        # Skip temp directories only if they contain Windows system files
        if 'appdata\\local\\temp' in path_str:
            # Allow temp directories unless they contain specific system patterns
            system_temp_patterns = ['microsoft', 'windows', 'system']
            return any(pattern in path_str for pattern in system_temp_patterns)

        return any(pattern in path_str for pattern in system_patterns)
    
    def _index_batch(self, file_paths: List[Path]) -> List[bool]:
        """Index a batch of files"""
        results = []
        
        for file_path in file_paths:
            try:
                # Update current file in progress
                self.indexing_progress.current_file = str(file_path)
                
                # Index the file using core engine
                result = self.core_engine.index_file(str(file_path))
                results.append(result is not None)
                
                # Update bytes processed
                try:
                    self.indexing_progress.bytes_processed += file_path.stat().st_size
                except:
                    pass
                
            except Exception as e:
                logger.warning(f"Failed to index {file_path}: {e}")
                results.append(False)
        
        return results
    
    def _check_memory_usage(self):
        """Check memory usage and trigger cleanup if needed"""
        memory_usage_gb = psutil.Process().memory_info().rss / (1024**3)
        
        if memory_usage_gb > self.memory_limit_gb:
            logger.warning(f"Memory usage high: {memory_usage_gb:.2f}GB, triggering cleanup")
            self.resource_manager.cleanup_all()
    
    def stop_indexing(self):
        """Stop ongoing indexing operation"""
        if self.is_indexing:
            self.is_indexing = False
            logger.info("Indexing stop requested")
    
    def get_indexing_stats(self) -> Dict[str, Any]:
        """Get current indexing statistics"""
        with get_db() as db:
            total_indexed = db.query(FileRecord).count()
        
        return {
            'total_indexed_files': total_indexed,
            'is_indexing': self.is_indexing,
            'current_progress': self.indexing_progress,
            'memory_usage_gb': psutil.Process().memory_info().rss / (1024**3),
            'supported_extensions': len(self.supported_extensions)
        }
    
    def search_files(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search indexed files using semantic search"""
        from semantic_fs.models import SemanticQuery

        # Create SemanticQuery object
        semantic_query = SemanticQuery(
            query=query,
            limit=limit,
            filters=None,
            include_content=False
        )

        # Execute query and return results
        query_result = self.core_engine.query(semantic_query)

        # Convert QueryResult to list of dictionaries for easier use
        results = []
        for result in query_result.results:
            results.append({
                'filename': result.file.filename,
                'path': result.file.path,
                'relevance_score': result.relevance_score,
                'file_type': result.file.file_type,
                'size': result.file.size,
                'modified_at': result.file.modified_at.isoformat() if result.file.modified_at else None,
                'summary': result.file.content_summary,
                'match_reason': result.match_reason,
                'content_snippet': result.content_snippet
            })

        return results
    
    def get_file_stats(self) -> Dict[str, Any]:
        """Get comprehensive file statistics"""
        with get_db() as db:
            # Get file type distribution
            files = db.query(FileRecord).all()
            
            type_stats = {}
            size_stats = {'total_size': 0, 'avg_size': 0}
            
            for file_record in files:
                file_type = Path(file_record.path).suffix.lower()
                type_stats[file_type] = type_stats.get(file_type, 0) + 1
                size_stats['total_size'] += file_record.size
            
            if files:
                size_stats['avg_size'] = size_stats['total_size'] / len(files)
            
            return {
                'total_files': len(files),
                'file_types': type_stats,
                'size_stats': size_stats
            }
