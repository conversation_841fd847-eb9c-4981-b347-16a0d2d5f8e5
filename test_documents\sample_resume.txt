JOHN SMITH
Software Engineer

Contact Information:
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johnsmith
GitHub: github.com/johnsmith

PROFESSIONAL SUMMARY
Experienced software engineer with 5+ years of expertise in full-stack development. 
Proficient in Python, JavaScript, React, and cloud technologies. Strong background 
in building scalable web applications and API development.

TECHNICAL SKILLS
• Programming Languages: Python, JavaScript, TypeScript, Java
• Frontend: React, Vue.js, HTML5, CSS3, Bootstrap
• Backend: Node.js, Django, Flask, Express.js
• Databases: PostgreSQL, MongoDB, Redis
• Cloud: AWS, Docker, Kubernetes
• Tools: Git, Jenkins, JIRA, VS Code

WORK EXPERIENCE

Senior Software Engineer | TechCorp Inc. | 2021 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented CI/CD pipelines reducing deployment time by 60%
• Mentored junior developers and conducted code reviews
• Technologies: Python, <PERSON>act, <PERSON><PERSON>, Docker

Software Engineer | StartupXYZ | 2019 - 2021
• Developed full-stack web applications using React and Node.js
• Designed and implemented RESTful APIs
• Optimized database queries improving performance by 40%
• Technologies: JavaScript, React, Node.js, PostgreSQL

EDUCATION
Bachelor of Science in Computer Science
University of Technology | 2015 - 2019
GPA: 3.8/4.0

PROJECTS
• E-commerce Platform: Built scalable online shopping platform
• Task Management App: Created collaborative project management tool
• Data Analytics Dashboard: Developed real-time analytics visualization

CERTIFICATIONS
• AWS Certified Solutions Architect
• Google Cloud Professional Developer
• Certified Kubernetes Administrator (CKA)

This resume was last updated in April 2024 and used for multiple job applications.
