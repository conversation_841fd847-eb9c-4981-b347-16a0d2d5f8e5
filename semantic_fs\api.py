"""
FastAPI REST API for the semantic filesystem
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, Depends, Query, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from typing import List, Optional, Dict, Any
import logging
import time
from pathlib import Path

from .core import SemanticFilesystem
from .models import SemanticQuery, QueryResult, SearchResult, FileMetadata
from .config import settings
from .exceptions import (
    SemanticFilesystemError, SearchError, FileProcessingError,
    DatabaseError, ValidationError, create_user_friendly_error,
    handle_errors, ErrorSeverity, ErrorRecovery
)
from .security import (
    get_current_user, User, rate_limit_middleware, add_security_headers,
    require_auth, security_settings
)

logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Semantic Filesystem API",
    description="LLM-powered semantic file organization and search",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add security middleware
@app.middleware("http")
async def security_middleware(request: Request, call_next):
    """Security middleware for rate limiting, logging, and headers"""
    start_time = time.time()

    # Log request
    logger.info(
        "HTTP Request",
        extra={
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("user-agent", "unknown")
        }
    )

    # Apply rate limiting
    try:
        response = await rate_limit_middleware(request, call_next)
    except HTTPException as e:
        # Log rate limit violations
        logger.warning(
            "Rate limit exceeded",
            extra={
                "client_ip": request.client.host if request.client else "unknown",
                "method": request.method,
                "url": str(request.url)
            }
        )
        raise

    # Calculate duration
    duration = time.time() - start_time

    # Add security headers
    response = add_security_headers(response)

    # Log response
    logger.info(
        "HTTP Response",
        extra={
            "method": request.method,
            "url": str(request.url),
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2)
        }
    )

    return response

# Global semantic filesystem instance (lazy initialization)
semantic_fs = None

def get_semantic_fs():
    """Get or create the semantic filesystem instance"""
    global semantic_fs
    if semantic_fs is None:
        semantic_fs = SemanticFilesystem()
    return semantic_fs

@app.on_event("startup")
async def startup_event():
    """Initialize the application"""
    # Setup logging for API
    from .logging_config import setup_logging
    setup_logging(
        log_level=settings.log_level,
        enable_file_logging=settings.enable_file_logging,
        enable_json_logging=settings.enable_json_logging,
        enable_rotation=settings.enable_log_rotation
    )

    logger.info("Starting Semantic Filesystem API")
    logger.info(f"API Host: {settings.api_host}:{settings.api_port}")
    logger.info(f"Database: {settings.database_url}")
    logger.info(f"Vector DB: {settings.vector_db_path}")

    # Initialize the semantic filesystem
    get_semantic_fs()
    logger.info("Semantic Filesystem API ready")

    # Log security settings
    logger.info(f"Authentication: {'enabled' if settings.auth_enabled else 'disabled'}")
    logger.info(f"Rate limiting: {'enabled' if settings.rate_limit_enabled else 'disabled'}")
    logger.info(f"Security headers: {'enabled' if settings.security_headers_enabled else 'disabled'}")

# Authentication endpoints
@app.post("/api/auth/token")
async def create_token(api_key: str):
    """Create JWT token from API key"""
    from .security import security_manager

    key_data = security_manager.verify_api_key(api_key)
    if not key_data:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )

    # Create tokens
    token_data = {
        "sub": key_data["name"],
        "permissions": key_data["permissions"]
    }

    access_token = security_manager.create_access_token(token_data)
    refresh_token = security_manager.create_refresh_token(token_data)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": 30 * 60  # 30 minutes in seconds
    }

@app.get("/api/auth/me")
async def get_current_user_info(current_user: Optional[User] = Depends(get_current_user)):
    """Get current user information"""
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    return {
        "username": current_user.username,
        "permissions": current_user.permissions,
        "created_at": current_user.created_at.isoformat()
    }

@app.get("/api/security/status")
async def get_security_status():
    """Get security configuration status"""
    return {
        "auth_enabled": settings.auth_enabled,
        "rate_limit_enabled": settings.rate_limit_enabled,
        "security_headers_enabled": settings.security_headers_enabled,
        "require_auth_for_read": settings.require_auth_for_read,
        "require_auth_for_write": settings.require_auth_for_write,
        "rate_limit_requests": settings.rate_limit_requests,
        "rate_limit_window": settings.rate_limit_window
    }

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main web interface"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🧠 Semantic Filesystem</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            * { box-sizing: border-box; margin: 0; padding: 0; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                text-align: center;
                color: white;
                margin-bottom: 30px;
            }
            .header h1 {
                font-size: 3rem;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .header p {
                font-size: 1.2rem;
                opacity: 0.9;
            }
            .search-container {
                background: white;
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                margin-bottom: 30px;
            }
            .search-box {
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
            }
            .search-input {
                flex: 1;
                padding: 15px 20px;
                border: 2px solid #e1e5e9;
                border-radius: 10px;
                font-size: 16px;
                transition: all 0.3s ease;
            }
            .search-input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
            .search-btn {
                padding: 15px 25px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 120px;
            }
            .search-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            }
            .search-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            .examples {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                margin-top: 20px;
            }
            .example {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                border-left: 4px solid #667eea;
            }
            .example:hover {
                background: #e9ecef;
                transform: translateX(5px);
            }
            .example-text {
                font-style: italic;
                color: #666;
                margin-bottom: 5px;
            }
            .example-desc {
                font-size: 0.9rem;
                color: #888;
            }
            .results-container {
                background: white;
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                display: none;
            }
            .results-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 2px solid #e1e5e9;
            }
            .results-title {
                font-size: 1.5rem;
                color: #333;
            }
            .results-meta {
                color: #666;
                font-size: 0.9rem;
            }
            .result-item {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 15px;
                border-left: 4px solid #667eea;
                transition: all 0.3s ease;
            }
            .result-item:hover {
                background: #e9ecef;
                transform: translateX(5px);
            }
            .result-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 10px;
            }
            .result-title {
                font-size: 1.2rem;
                font-weight: 600;
                color: #333;
                margin-bottom: 5px;
            }
            .result-score {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
            }
            .result-path {
                color: #666;
                font-size: 0.9rem;
                margin-bottom: 10px;
                word-break: break-all;
            }
            .result-summary {
                color: #555;
                line-height: 1.5;
                margin-bottom: 10px;
            }
            .result-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
            }
            .tag {
                background: #e1e5e9;
                color: #666;
                padding: 3px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
            }
            .loading {
                text-align: center;
                padding: 40px;
                color: #666;
            }
            .spinner {
                display: inline-block;
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 15px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .error {
                background: #f8d7da;
                color: #721c24;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                border-left: 4px solid #dc3545;
            }
            .stats-container {
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                margin-bottom: 20px;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }
            .stat-item {
                text-align: center;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            .stat-number {
                font-size: 2rem;
                font-weight: 600;
                color: #667eea;
                margin-bottom: 5px;
            }
            .stat-label {
                color: #666;
                font-size: 0.9rem;
            }
            .footer {
                text-align: center;
                color: white;
                margin-top: 40px;
                opacity: 0.8;
            }
            .footer a {
                color: white;
                text-decoration: none;
            }
            .footer a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🧠 Semantic Filesystem</h1>
                <p>Ask questions about your files in natural language</p>
            </div>

            <div class="stats-container" id="statsContainer">
                <div class="stats-grid" id="statsGrid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalFiles">-</div>
                        <div class="stat-label">Total Files</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalSize">-</div>
                        <div class="stat-label">Total Size</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="fileTypes">-</div>
                        <div class="stat-label">File Types</div>
                    </div>
                </div>
            </div>

            <div class="search-container">
                <div class="search-box">
                    <input type="text" class="search-input" id="searchInput"
                           placeholder="Ask about your files... e.g., 'show me the PDF from my YouTube video 6 months ago'"
                           autocomplete="off">
                    <button class="search-btn" id="searchBtn" onclick="performSearch()">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>

                <div class="examples">
                    <div class="example" onclick="setQuery('python script that processes customer data')">
                        <div class="example-text">"python script that processes customer data"</div>
                        <div class="example-desc">Find code files by functionality</div>
                    </div>
                    <div class="example" onclick="setQuery('budget report from Q3')">
                        <div class="example-text">"budget report from Q3"</div>
                        <div class="example-desc">Search by time period</div>
                    </div>
                    <div class="example" onclick="setQuery('that resume I used in April')">
                        <div class="example-text">"that resume I used in April"</div>
                        <div class="example-desc">Find files by temporal context</div>
                    </div>
                    <div class="example" onclick="setQuery('meeting notes about product launch')">
                        <div class="example-text">"meeting notes about product launch"</div>
                        <div class="example-desc">Search by topic and content</div>
                    </div>
                </div>
            </div>

            <div class="results-container" id="resultsContainer">
                <div class="results-header">
                    <div class="results-title">Search Results</div>
                    <div class="results-meta" id="resultsMeta"></div>
                </div>
                <div id="resultsContent"></div>
            </div>

            <div class="footer">
                <p>
                    <a href="/docs" target="_blank"><i class="fas fa-book"></i> API Documentation</a> |
                    <a href="https://github.com/your-repo" target="_blank"><i class="fab fa-github"></i> GitHub</a>
                </p>
            </div>
        </div>

        <script>
            // Global variables
            let isSearching = false;

            // Initialize the page
            document.addEventListener('DOMContentLoaded', function() {
                loadStats();
                setupEventListeners();
            });

            function setupEventListeners() {
                const searchInput = document.getElementById('searchInput');
                const searchBtn = document.getElementById('searchBtn');

                // Enter key to search
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !isSearching) {
                        performSearch();
                    }
                });

                // Focus on search input
                searchInput.focus();
            }

            async function loadStats() {
                try {
                    const response = await fetch('/api/stats');
                    const stats = await response.json();

                    document.getElementById('totalFiles').textContent = stats.total_files || 0;
                    document.getElementById('totalSize').textContent = formatBytes(stats.total_size || 0);
                    document.getElementById('fileTypes').textContent = stats.file_types || 0;
                } catch (error) {
                    console.error('Error loading stats:', error);
                }
            }

            function setQuery(query) {
                document.getElementById('searchInput').value = query;
                performSearch();
            }

            async function performSearch() {
                const query = document.getElementById('searchInput').value.trim();
                if (!query || isSearching) return;

                isSearching = true;
                const searchBtn = document.getElementById('searchBtn');
                const resultsContainer = document.getElementById('resultsContainer');
                const resultsContent = document.getElementById('resultsContent');
                const resultsMeta = document.getElementById('resultsMeta');

                // Update UI
                searchBtn.disabled = true;
                searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
                resultsContainer.style.display = 'block';
                resultsContent.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>Searching your files...</div>
                    </div>
                `;

                try {
                    const startTime = Date.now();
                    const response = await fetch('/api/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            query: query,
                            limit: 10
                        })
                    });

                    const data = await response.json();
                    const endTime = Date.now();
                    const searchTime = ((endTime - startTime) / 1000).toFixed(2);

                    if (response.ok) {
                        displayResults(data, searchTime);
                    } else {
                        throw new Error(data.detail || 'Search failed');
                    }
                } catch (error) {
                    console.error('Search error:', error);
                    resultsContent.innerHTML = `
                        <div class="error">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error: ${error.message}
                        </div>
                    `;
                } finally {
                    isSearching = false;
                    searchBtn.disabled = false;
                    searchBtn.innerHTML = '<i class="fas fa-search"></i> Search';
                }
            }

            function displayResults(data, searchTime) {
                const resultsContent = document.getElementById('resultsContent');
                const resultsMeta = document.getElementById('resultsMeta');

                resultsMeta.innerHTML = `
                    Found ${data.total_found} results in ${searchTime}s
                    ${data.interpretation ? `<br><em>${data.interpretation}</em>` : ''}
                `;

                if (data.results && data.results.length > 0) {
                    resultsContent.innerHTML = data.results.map((result, index) => `
                        <div class="result-item">
                            <div class="result-header">
                                <div>
                                    <div class="result-title">
                                        <i class="fas ${getFileIcon(result.file.file_type)}"></i>
                                        ${result.file.filename}
                                    </div>
                                    <div class="result-path">${result.file.path}</div>
                                </div>
                                <div class="result-score">${(result.relevance_score * 100).toFixed(1)}%</div>
                            </div>
                            <div class="result-summary">${result.file.content_summary || 'No summary available'}</div>
                            <div class="result-tags">
                                ${(result.file.auto_tags || []).map(tag => `<span class="tag">#${tag}</span>`).join('')}
                            </div>
                        </div>
                    `).join('');
                } else {
                    resultsContent.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <div>No files found matching your query.</div>
                            <div style="margin-top: 10px; font-size: 0.9rem;">Try different keywords or check if files are indexed.</div>
                        </div>
                    `;
                }
            }

            function getFileIcon(fileType) {
                const iconMap = {
                    'application/pdf': 'fa-file-pdf',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel',
                    'text/plain': 'fa-file-alt',
                    'text/markdown': 'fa-file-alt',
                    'application/json': 'fa-file-code',
                    'text/csv': 'fa-file-csv',
                    'text/x-python': 'fa-file-code'
                };
                return iconMap[fileType] || 'fa-file';
            }

            function formatBytes(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }
        </script>
    </body>
    </html>
    """

@app.get("/api/stats")
async def get_stats():
    """Get filesystem statistics"""
    try:
        with get_db() as db:
            from sqlalchemy import func
            from .models import FileRecord

            # Get basic stats
            total_files = db.query(func.count(FileRecord.id)).scalar() or 0
            total_size = db.query(func.sum(FileRecord.file_size)).scalar() or 0

            # Get file types count
            file_types = db.query(func.count(func.distinct(FileRecord.file_type))).scalar() or 0

            # Get recent files (last 7 days)
            from datetime import datetime, timedelta
            week_ago = datetime.now() - timedelta(days=7)
            recent_files = db.query(func.count(FileRecord.id)).filter(
                FileRecord.indexed_at >= week_ago
            ).scalar() or 0

            return {
                "total_files": total_files,
                "total_size": total_size,
                "file_types": file_types,
                "recent_files": recent_files,
                "status": "healthy"
            }
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        return {
            "total_files": 0,
            "total_size": 0,
            "file_types": 0,
            "recent_files": 0,
            "status": "error",
            "error": str(e)
        }

@app.post("/api/search", response_model=QueryResult)
async def search_files(
    query: SemanticQuery,
    request: Request,
    current_user: Optional[User] = Depends(get_current_user)
):
    """Search files using natural language query"""
    try:
        # Check authentication for read operations
        if settings.require_auth_for_read and not current_user:
            raise HTTPException(
                status_code=401,
                detail="Authentication required for search operations"
            )

        # Validate input
        if not query.query or not query.query.strip():
            raise ValidationError("query", query.query, "Query cannot be empty")

        if len(query.query) > 1000:
            raise ValidationError("query", query.query, "Query too long (max 1000 characters)")

        if query.limit and (query.limit < 1 or query.limit > 100):
            raise ValidationError("limit", str(query.limit), "Limit must be between 1 and 100")

        # Log search query
        logger.info(
            "Search query",
            extra={
                "user": current_user.username if current_user else "anonymous",
                "query": query.query[:100] + "..." if len(query.query) > 100 else query.query,
                "limit": query.limit
            }
        )

        # Perform search
        result = get_semantic_fs().query(query)
        return result

    except ValidationError as e:
        logger.warning(f"Validation error in search: {e}")
        raise HTTPException(status_code=400, detail=create_user_friendly_error(e, "search"))

    except SearchError as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=create_user_friendly_error(e, "search"))

    except DatabaseError as e:
        logger.error(f"Database error during search: {e}")
        raise HTTPException(status_code=503, detail=create_user_friendly_error(e, "search"))

    except SemanticFilesystemError as e:
        logger.error(f"Semantic filesystem error during search: {e}")
        raise HTTPException(status_code=500, detail=create_user_friendly_error(e, "search"))

    except Exception as e:
        logger.error(f"Unexpected error during search: {e}")
        error_response = create_user_friendly_error(e, "search")
        raise HTTPException(status_code=500, detail=error_response)

@app.post("/api/index")
async def index_file(
    file_path: str,
    context: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Index a single file"""
    try:
        # Run indexing in background
        background_tasks.add_task(get_semantic_fs().index_file, file_path, context)
        return {"message": f"Indexing started for {file_path}"}
    except Exception as e:
        logger.error(f"Indexing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/index-directory")
async def index_directory(
    directory_path: str,
    recursive: bool = True,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Index all files in a directory"""
    try:
        def index_dir():
            directory = Path(directory_path)
            if not directory.exists():
                raise ValueError(f"Directory does not exist: {directory_path}")
            
            pattern = "**/*" if recursive else "*"
            files_indexed = 0
            
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    try:
                        result = get_semantic_fs().index_file(str(file_path))
                        if result:
                            files_indexed += 1
                    except Exception as e:
                        logger.error(f"Error indexing {file_path}: {e}")
            
            logger.info(f"Indexed {files_indexed} files from {directory_path}")
        
        background_tasks.add_task(index_dir)
        return {"message": f"Directory indexing started for {directory_path}"}
    except Exception as e:
        logger.error(f"Directory indexing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/files", response_model=List[FileMetadata])
async def list_files(
    limit: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0)
):
    """List indexed files"""
    try:
        from .database import get_db
        from .models import FileRecord
        
        with get_db() as db:
            files = db.query(FileRecord).offset(offset).limit(limit).all()
            
            result = []
            for file_record in files:
                file_metadata = FileMetadata(
                    id=file_record.id,
                    path=file_record.path,
                    filename=file_record.filename,
                    file_type=file_record.file_type,
                    size=file_record.size,
                    created_at=file_record.created_at,
                    modified_at=file_record.modified_at,
                    accessed_at=file_record.accessed_at,
                    content_summary=file_record.content_summary,
                    auto_tags=file_record.auto_tags or [],
                    user_tags=file_record.user_tags or [],
                    relationships=[
                        {
                            "type": rel.relationship_type,
                            "context": rel.context,
                            "context_id": rel.context_id,
                            "metadata": rel.context_metadata
                        }
                        for rel in file_record.relationships
                    ]
                )
                result.append(file_metadata)
            
            return result
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Duplicate stats endpoint removed
        
        with get_db() as db:
            total_files = db.query(func.count(FileRecord.id)).scalar()
            total_size = db.query(func.sum(FileRecord.size)).scalar() or 0
            
            # Get file type distribution
            file_types = db.query(
                FileRecord.file_type,
                func.count(FileRecord.id).label('count')
            ).group_by(FileRecord.file_type).all()
            
            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "file_types": [{"type": ft.file_type, "count": ft.count} for ft in file_types]
            }
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/files/{file_id}")
async def delete_file(file_id: int):
    """Remove a file from the index"""
    try:
        from .database import get_db
        from .models import FileRecord
        
        with get_db() as db:
            file_record = db.query(FileRecord).filter(FileRecord.id == file_id).first()
            if not file_record:
                raise HTTPException(status_code=404, detail="File not found")
            
            # Delete from vector database
            if file_record.embedding_id:
                get_semantic_fs().embedding_engine.delete_embedding(file_record.embedding_id)
            
            # Delete from database
            db.delete(file_record)
            db.commit()
            
            return {"message": f"File {file_record.filename} removed from index"}
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "semantic_fs.api:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True
    )
