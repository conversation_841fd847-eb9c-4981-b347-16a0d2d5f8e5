"""
Command-line interface for the semantic filesystem
"""

import click
import json
import os
import subprocess
import platform
import time
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
import logging

from .core import SemanticFilesystem
from .models import SemanticQuery
from .config import settings

console = Console()

@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--log-level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'], case_sensitive=False), help='Set logging level')
@click.option('--no-file-logging', is_flag=True, help='Disable file logging')
@click.option('--json-logging', is_flag=True, help='Enable JSON structured logging')
def cli(verbose, log_level, no_file_logging, json_logging):
    """🧠 Semantic Filesystem - LLM-powered file organization"""
    from .logging_config import setup_logging

    # Determine log level
    if log_level:
        level = log_level.upper()
    elif verbose:
        level = "DEBUG"
    else:
        level = settings.log_level

    # Setup comprehensive logging
    setup_logging(
        log_level=level,
        enable_file_logging=not no_file_logging,
        enable_json_logging=json_logging or settings.enable_json_logging,
        enable_rotation=settings.enable_log_rotation
    )

@cli.command()
@click.argument('query')
@click.option('--limit', '-l', default=10, help='Maximum number of results')
@click.option('--json-output', '-j', is_flag=True, help='Output results as JSON')
def search(query, limit, json_output):
    """Search files using natural language"""
    semantic_fs = SemanticFilesystem()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Searching...", total=None)
        
        semantic_query = SemanticQuery(query=query, limit=limit)
        result = semantic_fs.query(semantic_query)
    
    if json_output:
        # Convert to JSON-serializable format
        output = {
            "query": result.query,
            "total_found": result.total_found,
            "execution_time": result.execution_time,
            "interpretation": result.interpretation,
            "results": []
        }
        
        for search_result in result.results:
            output["results"].append({
                "file": {
                    "id": search_result.file.id,
                    "path": search_result.file.path,
                    "filename": search_result.file.filename,
                    "file_type": search_result.file.file_type,
                    "size": search_result.file.size,
                    "content_summary": search_result.file.content_summary,
                    "auto_tags": search_result.file.auto_tags,
                    "user_tags": search_result.file.user_tags
                },
                "relevance_score": search_result.relevance_score,
                "match_reason": search_result.match_reason
            })
        
        console.print(json.dumps(output, indent=2, default=str))
    else:
        # Rich formatted output
        console.print(Panel(f"🔍 Query: [bold]{query}[/bold]", title="Search"))
        
        if result.interpretation:
            console.print(f"💭 Interpreted as: [italic]{result.interpretation}[/italic]")
        
        console.print(f"⏱️  Found {result.total_found} results in {result.execution_time:.2f}s\n")
        
        if not result.results:
            console.print("[red]No files found matching your query.[/red]")
            return
        
        for i, search_result in enumerate(result.results, 1):
            file_info = search_result.file
            
            # Create result table
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Field", style="bold cyan")
            table.add_column("Value")
            
            table.add_row("📁 File", file_info.filename)
            table.add_row("📍 Path", file_info.path)
            table.add_row("📊 Score", f"{search_result.relevance_score:.1%}")
            table.add_row("🎯 Match", search_result.match_reason)
            
            if file_info.content_summary:
                table.add_row("📝 Summary", file_info.content_summary)
            
            if file_info.auto_tags:
                tags = " ".join([f"[blue]#{tag}[/blue]" for tag in file_info.auto_tags])
                table.add_row("🏷️  Tags", tags)
            
            console.print(Panel(table, title=f"Result {i}"))
            console.print()

@cli.command()
@click.argument('path')
@click.option('--context', '-c', help='Context information as JSON')
def index(path, context):
    """Index a file or directory"""
    semantic_fs = SemanticFilesystem()
    path_obj = Path(path)

    # Check if path exists
    if not path_obj.exists():
        console.print(f"[red]Path does not exist: {path}[/red]")
        return

    # Parse context if provided
    context_data = None
    if context:
        try:
            context_data = json.loads(context)
        except json.JSONDecodeError:
            console.print("[red]Invalid JSON in context parameter[/red]")
            return
    
    if path_obj.is_file():
        # Index single file
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Indexing {path_obj.name}...", total=None)
            result = semantic_fs.index_file(str(path_obj), context_data)
        
        if result:
            console.print(f"✅ Successfully indexed: [green]{path_obj}[/green]")
        else:
            console.print(f"❌ Failed to index: [red]{path_obj}[/red]")
    
    elif path_obj.is_dir():
        # Index directory
        all_files = list(path_obj.rglob('*'))
        files_to_index = [f for f in all_files if f.is_file()]
        file_count = len(files_to_index)

        if file_count == 0:
            console.print(f"[yellow]No files found in directory: {path_obj}[/yellow]")
            return

        with Progress(console=console) as progress:
            task = progress.add_task(f"Indexing {file_count} files...", total=file_count)

            indexed_count = 0
            for file_path in files_to_index:
                try:
                    result = semantic_fs.index_file(str(file_path), context_data)
                    if result:
                        indexed_count += 1
                        console.print(f"✅ Indexed: [green]{file_path.name}[/green]")
                    else:
                        console.print(f"⚠️  Skipped: [yellow]{file_path.name}[/yellow]")
                    progress.update(task, advance=1)
                except Exception as e:
                    console.print(f"❌ Error indexing {file_path.name}: [red]{e}[/red]")
                    progress.update(task, advance=1)

        console.print(f"\n✅ Successfully indexed {indexed_count}/{file_count} files")

@cli.command()
@click.option('--limit', '-l', default=20, help='Maximum number of files to show')
@click.option('--json-output', '-j', is_flag=True, help='Output results as JSON')
def list(limit, json_output):
    """List indexed files with ID and metadata"""
    from .database import get_db
    from .models import FileRecord

    try:
        with get_db() as db:
            files = db.query(FileRecord).order_by(FileRecord.modified_at.desc()).limit(limit).all()

            if json_output:
                # JSON output
                output = []
                for file_record in files:
                    output.append({
                        "id": file_record.id,
                        "filename": file_record.filename,
                        "path": file_record.path,
                        "file_type": file_record.file_type,
                        "size": file_record.size,
                        "created_at": file_record.created_at.isoformat(),
                        "modified_at": file_record.modified_at.isoformat(),
                        "content_summary": file_record.content_summary,
                        "auto_tags": file_record.auto_tags or [],
                        "user_tags": file_record.user_tags or []
                    })
                console.print(json.dumps(output, indent=2))
            else:
                # Rich formatted output
                if not files:
                    console.print("[yellow]No files indexed yet. Use 'index' command to add files.[/yellow]")
                    return

                table = Table(title=f"📋 Indexed Files (showing {len(files)} most recent)")
                table.add_column("ID", style="bold cyan", width=6)
                table.add_column("Filename", style="bold")
                table.add_column("Type", style="green", width=12)
                table.add_column("Size", style="yellow", width=10)
                table.add_column("Modified", style="blue", width=12)
                table.add_column("Tags", style="magenta")

                for file_record in files:
                    size_mb = file_record.size / (1024 * 1024)
                    size_str = f"{size_mb:.1f} MB" if size_mb >= 1 else f"{file_record.size / 1024:.1f} KB"

                    tags = ", ".join(file_record.auto_tags[:3]) if file_record.auto_tags else ""
                    if len(file_record.auto_tags or []) > 3:
                        tags += "..."

                    table.add_row(
                        str(file_record.id),
                        file_record.filename,
                        file_record.file_type.split('/')[-1] if file_record.file_type else "unknown",
                        size_str,
                        file_record.modified_at.strftime('%Y-%m-%d'),
                        tags
                    )

                console.print(table)
                console.print(f"\n💡 Use 'open <id>' to open a file or 'search \"<query>\"' to find specific files")

    except Exception as e:
        console.print(f"[red]Error listing files: {e}[/red]")

@cli.command()
def cache():
    """Show cache statistics and management"""
    from .cache import get_memory_cache, get_query_cache

    cache = get_memory_cache()
    query_cache = get_query_cache()

    # Get cache stats
    stats = cache.get_stats()

    console.print("🗄️  [bold]Cache Statistics[/bold]")
    console.print(f"📊 Total entries: {stats['total_entries']}")
    console.print(f"✅ Active entries: {stats['active_entries']}")
    console.print(f"⏰ Expired entries: {stats['expired_entries']}")
    console.print(f"💾 Memory usage: {stats['memory_usage_mb']:.2f} MB")

    # Cleanup expired entries
    cleaned = cache.cleanup_expired()
    if cleaned > 0:
        console.print(f"🧹 Cleaned up {cleaned} expired entries")

    console.print("\n💡 Cache commands:")
    console.print("  • Use --clear to clear all cache")
    console.print("  • Use --clear-queries to clear query cache only")

@cli.command()
def stats():
    """Show filesystem statistics"""
    from .database import get_db
    from .models import FileRecord
    from sqlalchemy import func

    try:
        with get_db() as db:
            total_files = db.query(func.count(FileRecord.id)).scalar()
            total_size = db.query(func.sum(FileRecord.size)).scalar() or 0

            # Get file type distribution
            file_types = db.query(
                FileRecord.file_type,
                func.count(FileRecord.id).label('count')
            ).group_by(FileRecord.file_type).all()

            # Create stats table
            table = Table(title="📊 Semantic Filesystem Statistics")
            table.add_column("Metric", style="bold cyan")
            table.add_column("Value", style="green")

            table.add_row("Total Files", str(total_files))
            table.add_row("Total Size", f"{total_size / (1024*1024):.1f} MB")

            console.print(table)
            console.print()

            # File types table
            if file_types:
                types_table = Table(title="📁 File Types")
                types_table.add_column("Type", style="bold")
                types_table.add_column("Count", style="green")

                for file_type in file_types:
                    types_table.add_row(file_type.file_type or "Unknown", str(file_type.count))

                console.print(types_table)

    except Exception as e:
        console.print(f"[red]Error getting statistics: {e}[/red]")

@cli.command()
def serve():
    """Start the web API server"""
    import uvicorn

    console.print(f"🚀 Starting Semantic Filesystem API on http://{settings.api_host}:{settings.api_port}")
    console.print("📖 API documentation available at http://localhost:8000/docs")
    console.print("🌐 Web interface available at http://localhost:8000")
    console.print("⏳ Initializing server...")

    try:
        uvicorn.run(
            "semantic_fs.api:app",
            host=settings.api_host,
            port=settings.api_port,
            reload=False,  # Disable reload to avoid issues
            log_level="info"
        )
    except Exception as e:
        console.print(f"[red]❌ Failed to start server: {e}[/red]")

@cli.command()
@click.argument('path')
@click.option('--recursive/--no-recursive', default=True, help='Monitor subdirectories recursively')
def watch(path, recursive):
    """Start monitoring a directory for file changes with improved Windows support"""
    from .core import SemanticFilesystem
    from .file_monitor import FileMonitor
    from .signal_handler import setup_signal_handling, is_shutdown_initiated
    import sys
    import platform

    console.print(f"🔍 Starting file monitor for: {path}")
    console.print(f"📁 Recursive monitoring: {'enabled' if recursive else 'disabled'}")
    console.print(f"💻 Platform: {platform.system()}")

    monitor = None
    try:
        # Setup signal handling
        console.print("🛡️  Setting up signal handling...")
        signal_manager = setup_signal_handling()

        # Initialize semantic filesystem
        console.print("🚀 Initializing semantic filesystem...")
        semantic_fs = SemanticFilesystem()

        # Create file monitor
        console.print("📡 Creating file monitor...")
        monitor = FileMonitor(semantic_fs)

        # Add watch path
        console.print(f"📂 Adding watch path: {path}")
        if not monitor.add_watch_path(path, recursive=recursive):
            console.print("[red]❌ Failed to add watch path[/red]")
            return

        # Start monitoring
        console.print("▶️  Starting file monitoring...")
        if not monitor.start():
            console.print("[red]❌ Failed to start file monitor[/red]")
            return

        console.print("✅ File monitor started successfully!")
        console.print("📝 Supported file types: TXT, MD, JSON, CSV, PY, JS, HTML, CSS, PDF, DOCX, XLSX, XML, YAML, LOG")
        console.print("⏱️  Files are processed with 2-second debouncing")
        console.print("🛑 Press Ctrl+C to stop monitoring")

        # Keep the main thread alive until shutdown is initiated
        try:
            while monitor.is_running and not is_shutdown_initiated():
                time.sleep(1)
        except KeyboardInterrupt:
            console.print("\n🛑 Keyboard interrupt received...")

        if not is_shutdown_initiated():
            console.print("🛑 Stopping file monitor...")
            if monitor:
                monitor.stop()

        console.print("✅ File monitor stopped")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")
        if monitor:
            try:
                monitor.stop()
            except:
                pass

@cli.command()
@click.argument('file_identifier')
def open(file_identifier):
    """Open a file in the default viewer (by ID or path)"""
    from .database import get_db
    from .models import FileRecord

    try:
        with get_db() as db:
            file_record = None

            # Try to parse as ID first
            try:
                file_id = int(file_identifier)
                file_record = db.query(FileRecord).filter(FileRecord.id == file_id).first()
            except ValueError:
                # If not a number, treat as path
                file_record = db.query(FileRecord).filter(FileRecord.path == file_identifier).first()

            if not file_record:
                console.print(f"[red]File not found: {file_identifier}[/red]")
                console.print("💡 Use 'list' command to see available files and their IDs")
                return

            file_path = Path(file_record.path)
            if not file_path.exists():
                console.print(f"[red]File no longer exists at: {file_record.path}[/red]")
                console.print("💡 The file may have been moved or deleted. Consider removing it from the index.")
                return

            # Open file with default application
            try:
                system = platform.system()
                if system == "Windows":
                    os.startfile(str(file_path))
                elif system == "Darwin":  # macOS
                    subprocess.run(["open", str(file_path)])
                else:  # Linux and others
                    subprocess.run(["xdg-open", str(file_path)])

                console.print(f"✅ Opened [green]{file_record.filename}[/green] in default application")

            except Exception as e:
                console.print(f"[red]Error opening file: {e}[/red]")
                console.print(f"💡 File path: {file_record.path}")

    except Exception as e:
        console.print(f"[red]Error finding file: {e}[/red]")

@cli.command()
@click.argument('file_id', type=int)
def delete(file_id):
    """Remove a file from the index"""
    from .database import get_db
    from .models import FileRecord

    try:
        semantic_fs = SemanticFilesystem()

        with get_db() as db:
            file_record = db.query(FileRecord).filter(FileRecord.id == file_id).first()
            if not file_record:
                console.print(f"[red]File with ID {file_id} not found[/red]")
                return

            filename = file_record.filename

            # Delete from vector database
            if file_record.embedding_id:
                semantic_fs.embedding_engine.delete_embedding(file_record.embedding_id)

            # Delete from database
            db.delete(file_record)
            db.commit()

            console.print(f"✅ Removed [green]{filename}[/green] from index")

    except Exception as e:
        console.print(f"[red]Error deleting file: {e}[/red]")

@cli.command()
def config():
    """Show current configuration"""
    config_table = Table(title="⚙️ Configuration")
    config_table.add_column("Setting", style="bold cyan")
    config_table.add_column("Value", style="green")
    
    config_table.add_row("Database URL", settings.database_url)
    config_table.add_row("Vector DB Path", settings.vector_db_path)
    config_table.add_row("Embedding Model", settings.embedding_model)
    config_table.add_row("LLM Model", settings.llm_model)
    config_table.add_row("API Host", settings.api_host)
    config_table.add_row("API Port", str(settings.api_port))
    config_table.add_row("Max File Size", f"{settings.max_file_size / (1024*1024):.1f} MB")
    config_table.add_row("Auto-tagging", "✅" if settings.auto_tag_enabled else "❌")
    config_table.add_row("Content Summary", "✅" if settings.content_summary_enabled else "❌")
    
    console.print(config_table)

# Database migration commands
@cli.group()
def db():
    """Database management commands"""
    pass

@db.command()
def init():
    """Initialize database with migration support"""
    from .migrations import get_migration_manager

    console.print("🗄️  Initializing database...")

    try:
        manager = get_migration_manager()
        if manager.initialize_database():
            console.print("✅ Database initialized successfully")

            # Show status
            status = manager.get_status()
            console.print(f"📊 Current revision: {status['current_revision'] or 'None'}")
            console.print(f"📊 Head revision: {status['head_revision'] or 'None'}")
        else:
            console.print("[red]❌ Failed to initialize database[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@db.command()
def status():
    """Show database migration status"""
    from .migrations import get_migration_manager

    try:
        manager = get_migration_manager()
        status = manager.get_status()

        console.print("📊 Database Migration Status")
        console.print(f"Database URL: {status['database_url']}")
        console.print(f"Current revision: {status['current_revision'] or 'None'}")
        console.print(f"Head revision: {status['head_revision'] or 'None'}")
        console.print(f"Up to date: {'✅ Yes' if status['is_up_to_date'] else '❌ No'}")

        pending = status['pending_migrations']
        if pending:
            console.print(f"Pending migrations: {len(pending)}")
            for rev in pending:
                console.print(f"  - {rev}")
        else:
            console.print("No pending migrations")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@db.command()
@click.argument('message')
def migrate(message):
    """Create a new migration"""
    from .migrations import get_migration_manager

    console.print(f"📝 Creating migration: {message}")

    try:
        manager = get_migration_manager()
        if manager.create_migration(message):
            console.print("✅ Migration created successfully")
        else:
            console.print("[red]❌ Failed to create migration[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@db.command()
@click.option('--revision', default='head', help='Target revision (default: head)')
def upgrade(revision):
    """Upgrade database to specified revision"""
    from .migrations import get_migration_manager

    console.print(f"⬆️  Upgrading database to: {revision}")

    try:
        manager = get_migration_manager()
        if manager.upgrade_database(revision):
            console.print("✅ Database upgrade completed")
        else:
            console.print("[red]❌ Database upgrade failed[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

# Logging management commands
@cli.group()
def logs():
    """Logging management commands"""
    pass

@logs.command()
def status():
    """Show logging status and statistics"""
    from .logging_config import get_log_stats

    try:
        stats = get_log_stats()

        console.print("📊 Logging Status")
        console.print(f"Log directory: {stats['log_directory']}")
        console.print()

        if stats['files']:
            log_table = Table(title="Log Files")
            log_table.add_column("File", style="bold cyan")
            log_table.add_column("Size", style="green")
            log_table.add_column("Last Modified", style="yellow")
            log_table.add_column("Path", style="dim")

            for name, info in stats['files'].items():
                if info.get('exists', True):
                    size_str = f"{info['size_mb']} MB"
                    modified = info['modified'][:19].replace('T', ' ')
                    log_table.add_row(name, size_str, modified, info['path'])
                else:
                    log_table.add_row(name, "N/A", "N/A", info['path'])

            console.print(log_table)
        else:
            console.print("No log files found")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@logs.command()
@click.argument('file_type', type=click.Choice(['main', 'errors', 'all']), default='main')
@click.option('--lines', '-n', type=int, default=50, help='Number of lines to show')
def tail(file_type, lines):
    """Show recent log entries"""
    from .logging_config import get_logging_manager

    try:
        manager = get_logging_manager()
        log_files = manager.get_log_files()

        files_to_show = []
        if file_type == 'all':
            files_to_show = list(log_files.items())
        else:
            files_to_show = [(file_type, log_files[file_type])]

        for name, path in files_to_show:
            if not path.exists():
                console.print(f"[yellow]Log file {name} does not exist: {path}[/yellow]")
                continue

            console.print(f"\n📄 {name.title()} Log (last {lines} lines):")
            console.print("─" * 80)

            try:
                with open(path, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

                    for line in recent_lines:
                        console.print(line.rstrip())

            except Exception as e:
                console.print(f"[red]Error reading {name} log: {e}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@logs.command()
def clear():
    """Clear all log files"""
    from .logging_config import get_logging_manager

    if not click.confirm("Are you sure you want to clear all log files?"):
        console.print("Operation cancelled")
        return

    try:
        manager = get_logging_manager()
        log_files = manager.get_log_files()

        cleared_count = 0
        for name, path in log_files.items():
            if path.exists():
                path.unlink()
                console.print(f"✅ Cleared {name} log")
                cleared_count += 1

        if cleared_count > 0:
            console.print(f"🗑️  Cleared {cleared_count} log files")
        else:
            console.print("No log files to clear")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

# Testing commands
@cli.group()
def test():
    """Testing and quality assurance commands"""
    pass

@test.command()
@click.option('--type', 'test_type', type=click.Choice(['unit', 'integration', 'e2e', 'all']), default='all', help='Type of tests to run')
@click.option('--coverage/--no-coverage', default=True, help='Generate coverage report')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.option('--parallel', '-p', is_flag=True, help='Run tests in parallel')
def run(test_type, coverage, verbose, parallel):
    """Run the test suite"""
    import subprocess
    import sys

    console.print(f"🧪 Running {test_type} tests...")

    # Build pytest command
    cmd = ["python", "-m", "pytest"]

    # Add test type filter
    if test_type != 'all':
        cmd.extend(["-m", test_type])

    # Add coverage
    if coverage:
        cmd.extend(["--cov=semantic_fs", "--cov-report=term-missing", "--cov-report=html"])

    # Add verbosity
    if verbose:
        cmd.append("-v")

    # Add parallel execution
    if parallel:
        cmd.extend(["-n", "auto"])

    # Add test directory
    cmd.append("tests/")

    try:
        console.print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=".", check=False)

        if result.returncode == 0:
            console.print("✅ All tests passed!")
            if coverage:
                console.print("📊 Coverage report generated in htmlcov/")
        else:
            console.print(f"❌ Tests failed with exit code {result.returncode}")
            sys.exit(result.returncode)

    except FileNotFoundError:
        console.print("[red]❌ pytest not found. Install test dependencies:[/red]")
        console.print("pip install pytest pytest-cov pytest-asyncio")
    except Exception as e:
        console.print(f"[red]❌ Error running tests: {e}[/red]")

@test.command()
def install():
    """Install test dependencies"""
    import subprocess

    console.print("📦 Installing test dependencies...")

    test_deps = [
        "pytest>=7.4.3",
        "pytest-asyncio>=0.21.1",
        "pytest-cov>=4.1.0",
        "pytest-timeout>=2.2.0",
        "pytest-mock>=3.12.0",
        "pytest-xdist>=3.5.0",
        "coverage>=7.3.2"
    ]

    try:
        cmd = ["pip", "install"] + test_deps
        result = subprocess.run(cmd, check=True)

        console.print("✅ Test dependencies installed successfully!")

    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ Failed to install dependencies: {e}[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@test.command()
def coverage():
    """Generate and display coverage report"""
    import subprocess
    from pathlib import Path

    console.print("📊 Generating coverage report...")

    try:
        # Run tests with coverage
        cmd = ["python", "-m", "pytest", "--cov=semantic_fs", "--cov-report=html", "--cov-report=term", "tests/"]
        result = subprocess.run(cmd, check=True)

        # Check if HTML report was generated
        html_report = Path("htmlcov/index.html")
        if html_report.exists():
            console.print(f"✅ HTML coverage report generated: {html_report.absolute()}")

        console.print("✅ Coverage report generated!")

    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ Failed to generate coverage report: {e}[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@test.command()
@click.option('--fix', is_flag=True, help='Automatically fix issues')
def lint(fix):
    """Run code quality checks"""
    import subprocess

    console.print("🔍 Running code quality checks...")

    # Check if tools are available
    tools = []

    try:
        subprocess.run(["black", "--version"], capture_output=True, check=True)
        tools.append("black")
    except (subprocess.CalledProcessError, FileNotFoundError):
        console.print("[yellow]⚠️  black not installed[/yellow]")

    try:
        subprocess.run(["flake8", "--version"], capture_output=True, check=True)
        tools.append("flake8")
    except (subprocess.CalledProcessError, FileNotFoundError):
        console.print("[yellow]⚠️  flake8 not installed[/yellow]")

    if not tools:
        console.print("[red]❌ No linting tools available. Install with:[/red]")
        console.print("pip install black flake8")
        return

    # Run black
    if "black" in tools:
        console.print("🎨 Running black...")
        cmd = ["black"]
        if not fix:
            cmd.append("--check")
        cmd.extend(["semantic_fs/", "tests/"])

        try:
            result = subprocess.run(cmd, check=False)
            if result.returncode == 0:
                console.print("✅ Black formatting check passed")
            else:
                console.print("❌ Black formatting issues found")
        except Exception as e:
            console.print(f"[red]❌ Error running black: {e}[/red]")

    # Run flake8
    if "flake8" in tools:
        console.print("🔍 Running flake8...")
        cmd = ["flake8", "semantic_fs/", "tests/"]

        try:
            result = subprocess.run(cmd, check=False)
            if result.returncode == 0:
                console.print("✅ Flake8 checks passed")
            else:
                console.print("❌ Flake8 issues found")
        except Exception as e:
            console.print(f"[red]❌ Error running flake8: {e}[/red]")

# Resource management commands
@cli.group()
def resources():
    """Resource management and monitoring commands"""
    pass

@resources.command()
def status():
    """Show current resource usage"""
    try:
        from .resource_manager import get_resource_stats

        stats = get_resource_stats()

        console.print("📊 Resource Usage Status")

        if "current" in stats:
            current = stats["current"]
            console.print(f"Memory: {current.get('memory_mb', 0):.1f} MB")
            console.print(f"CPU: {current.get('cpu_percent', 0):.1f}%")
            console.print(f"Open Files: {current.get('open_files', 0)}")
            console.print(f"Threads: {current.get('threads', 0)}")
            console.print(f"Connections: {current.get('connections', 0)}")
            console.print(f"Cache Size: {current.get('cache_size', 0)} items")

        if "limits" in stats:
            limits = stats["limits"]
            console.print("\n📏 Resource Limits")
            console.print(f"Memory Limit: {limits.get('memory_mb', 0)} MB")
            console.print(f"File Limit: {limits.get('open_files', 0)}")
            console.print(f"Thread Limit: {limits.get('threads', 0)}")
            console.print(f"Connection Limit: {limits.get('connections', 0)}")

        console.print(f"\nManaged Objects: {stats.get('managed_objects', 0)}")
        console.print(f"Cleanup Handlers: {stats.get('cleanup_handlers', 0)}")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@resources.command()
def cleanup():
    """Manually trigger resource cleanup"""
    try:
        from .resource_manager import cleanup_resources

        console.print("🧹 Running resource cleanup...")
        cleanup_resources()
        console.print("✅ Resource cleanup completed")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@resources.command()
def cache():
    """Show cache statistics and management"""
    try:
        from .cache import get_memory_cache

        cache = get_memory_cache()
        stats = cache.get_stats()

        console.print("💾 Cache Statistics")
        console.print(f"Total Entries: {stats['total_entries']}")
        console.print(f"Expired Entries: {stats['expired_entries']}")
        console.print(f"Max Size: {stats['max_size']}")
        console.print(f"Utilization: {stats['utilization']:.1%}")

        # Cleanup expired entries
        cleaned = cache.cleanup_expired()
        if cleaned > 0:
            console.print(f"🗑️  Cleaned up {cleaned} expired entries")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@resources.command()
def database():
    """Show database connection statistics"""
    try:
        from .database import get_connection_stats

        stats = get_connection_stats()

        console.print("🗄️  Database Connection Pool")
        console.print(f"Pool Size: {stats.get('pool_size', 'N/A')}")
        console.print(f"Checked In: {stats.get('checked_in', 'N/A')}")
        console.print(f"Checked Out: {stats.get('checked_out', 'N/A')}")
        console.print(f"Overflow: {stats.get('overflow', 'N/A')}")
        console.print(f"Invalid: {stats.get('invalid', 'N/A')}")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

# Configuration management commands
@cli.group()
def config():
    """Configuration management and validation commands"""
    pass

@config.command()
def validate():
    """Validate current configuration"""
    try:
        from .config_manager import get_config_manager, register_builtin_validators

        # Register validators
        register_builtin_validators()

        manager = get_config_manager()
        result = manager.reload_config()

        console.print("🔍 Configuration Validation Results")

        if result.is_valid:
            console.print("✅ Configuration is valid")
        else:
            console.print("❌ Configuration has errors")

        if result.errors:
            console.print("\n🚨 Errors:")
            for error in result.errors:
                console.print(f"  • {error}")

        if result.warnings:
            console.print("\n⚠️  Warnings:")
            for warning in result.warnings:
                console.print(f"  • {warning}")

        if result.changes:
            console.print(f"\n📝 Changes detected: {len(result.changes)}")
            for change in result.changes:
                console.print(f"  • {change.key}: {change.old_value} → {change.new_value}")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@config.command()
def show():
    """Show current configuration"""
    try:
        from .config_manager import get_config_manager

        manager = get_config_manager()
        summary = manager.get_config_summary()

        console.print("⚙️  Configuration Summary")
        console.print(f"Environment: {summary.get('environment', 'unknown')}")
        console.print(f"Config Files: {len(summary.get('config_files', []))}")

        for config_file in summary.get('config_files', []):
            console.print(f"  • {config_file}")

        console.print(f"Hot Reload: {'enabled' if summary.get('hot_reload_enabled') else 'disabled'}")
        console.print(f"File Watching: {'active' if summary.get('watching') else 'inactive'}")
        console.print(f"Settings Count: {summary.get('settings_count', 0)}")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@config.command()
@click.option('--format', 'export_format', type=click.Choice(['json', 'yaml']), default='json', help='Export format')
@click.option('--output', '-o', help='Output file (default: stdout)')
def export(export_format, output):
    """Export current configuration"""
    try:
        from .config_manager import get_config_manager

        manager = get_config_manager()
        config_str = manager.export_config(export_format)

        if output:
            Path(output).write_text(config_str, encoding='utf-8')
            console.print(f"✅ Configuration exported to {output}")
        else:
            console.print(config_str)

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@config.command()
@click.argument('file_path')
def validate_file(file_path):
    """Validate a configuration file"""
    try:
        from .config_manager import get_config_manager

        manager = get_config_manager()
        result = manager.validate_config_file(file_path)

        console.print(f"🔍 Validating {file_path}")

        if result.is_valid:
            console.print("✅ Configuration file is valid")
        else:
            console.print("❌ Configuration file has errors")

        if result.errors:
            console.print("\n🚨 Errors:")
            for error in result.errors:
                console.print(f"  • {error}")

        if result.warnings:
            console.print("\n⚠️  Warnings:")
            for warning in result.warnings:
                console.print(f"  • {warning}")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@config.command()
def reload():
    """Reload configuration from files"""
    try:
        from .config_manager import reload_config, register_builtin_validators

        # Register validators
        register_builtin_validators()

        console.print("🔄 Reloading configuration...")
        result = reload_config()

        if result.is_valid:
            console.print("✅ Configuration reloaded successfully")
            if result.changes:
                console.print(f"📝 Applied {len(result.changes)} changes")
        else:
            console.print("❌ Configuration reload failed")
            for error in result.errors:
                console.print(f"  • {error}")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

@config.command()
def watch():
    """Start configuration file watching"""
    try:
        from .config_manager import start_config_watching

        console.print("👀 Starting configuration file watching...")
        start_config_watching()
        console.print("✅ Configuration watching started")
        console.print("Press Ctrl+C to stop")

        # Keep running until interrupted
        import time
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            console.print("\n🛑 Stopping configuration watching...")
            from .config_manager import stop_config_watching
            stop_config_watching()
            console.print("✅ Configuration watching stopped")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

if __name__ == '__main__':
    cli()
