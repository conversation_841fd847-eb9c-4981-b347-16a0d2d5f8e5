"""
Pytest configuration and shared fixtures
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Generator, Dict, Any
import pytest
import asyncio
from unittest.mock import Mock, patch

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from semantic_fs.core import SemanticFilesystem
from semantic_fs.config import settings
from semantic_fs.database import engine, SessionLocal
from semantic_fs.models import Base


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def temp_dir():
    """Create a temporary directory for tests"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture(scope="function")
def test_database():
    """Create a test database"""
    # Use in-memory SQLite for tests
    test_db_url = "sqlite:///:memory:"
    
    # Patch the settings to use test database
    with patch.object(settings, 'database_url', test_db_url):
        # Create test engine and tables
        from sqlalchemy import create_engine
        test_engine = create_engine(test_db_url)
        Base.metadata.create_all(bind=test_engine)
        
        yield test_engine
        
        # Cleanup
        Base.metadata.drop_all(bind=test_engine)
        test_engine.dispose()


@pytest.fixture(scope="function")
def mock_embedding_engine():
    """Mock embedding engine for tests"""
    mock_engine = Mock()
    mock_engine.generate_embedding.return_value = [0.1] * 384  # Mock embedding vector
    mock_engine.generate_embedding_id.return_value = "test_embedding_id"
    mock_engine.store_embedding.return_value = True
    mock_engine.search_similar.return_value = [
        {"id": "test_id", "distance": 0.1, "metadata": {"file_id": 1}}
    ]
    mock_engine.delete_embedding.return_value = True
    return mock_engine


@pytest.fixture(scope="function")
def mock_content_extractor():
    """Mock content extractor for tests"""
    mock_extractor = Mock()
    mock_extractor.extract_content.return_value = "Test content extracted from file"
    mock_extractor.get_file_type.return_value = "text/plain"
    return mock_extractor


@pytest.fixture(scope="function")
def mock_llm_processor():
    """Mock LLM processor for tests"""
    mock_processor = Mock()
    mock_processor.generate_tags.return_value = ["test", "document", "sample"]
    mock_processor.generate_summary.return_value = "This is a test document summary"
    mock_processor.interpret_query.return_value = "User is looking for test documents"
    return mock_processor


@pytest.fixture(scope="function")
def semantic_fs_with_mocks(
    test_database,
    mock_embedding_engine,
    mock_content_extractor,
    mock_llm_processor
):
    """Create a semantic filesystem instance with mocked dependencies"""
    with patch('semantic_fs.core.EmbeddingEngine', return_value=mock_embedding_engine), \
         patch('semantic_fs.core.ContentExtractor', return_value=mock_content_extractor), \
         patch('semantic_fs.core.LLMProcessor', return_value=mock_llm_processor), \
         patch('semantic_fs.core.initialize_database', return_value=True):
        
        # Reset singleton
        SemanticFilesystem._instance = None
        SemanticFilesystem._initialized = False
        
        fs = SemanticFilesystem()
        yield fs
        
        # Reset singleton after test
        SemanticFilesystem._instance = None
        SemanticFilesystem._initialized = False


@pytest.fixture(scope="function")
def test_files(temp_dir):
    """Create test files for testing"""
    files = {
        "test_document.txt": "This is a test document about artificial intelligence and machine learning.",
        "sample_report.txt": "Quarterly report showing 15% growth in revenue and improved customer satisfaction.",
        "python_script.py": "#!/usr/bin/env python3\n\ndef hello_world():\n    print('Hello, World!')\n\nif __name__ == '__main__':\n    hello_world()",
        "meeting_notes.md": "# Team Meeting\n\n## Agenda\n1. Project updates\n2. Budget review\n3. Next steps\n\n## Action Items\n- Complete testing\n- Deploy to production",
        "data_analysis.json": '{"results": {"accuracy": 0.95, "precision": 0.92, "recall": 0.89}, "model": "transformer", "dataset": "test_data"}'
    }
    
    created_files = {}
    for filename, content in files.items():
        file_path = temp_dir / filename
        file_path.write_text(content, encoding='utf-8')
        created_files[filename] = file_path
    
    return created_files


@pytest.fixture(scope="function")
def api_client():
    """Create a test client for the FastAPI app"""
    from fastapi.testclient import TestClient
    from semantic_fs.api import app
    
    # Mock the security settings for testing
    with patch('semantic_fs.security.security_settings') as mock_settings:
        mock_settings.auth_enabled = False
        mock_settings.rate_limit_enabled = False
        mock_settings.security_headers_enabled = True
        
        client = TestClient(app)
        yield client


@pytest.fixture(scope="function")
def sample_query_data():
    """Sample query data for testing"""
    return {
        "simple_query": {
            "query": "test document",
            "limit": 5
        },
        "complex_query": {
            "query": "artificial intelligence machine learning report",
            "limit": 10,
            "filters": {"file_type": "txt"}
        },
        "empty_query": {
            "query": "",
            "limit": 5
        },
        "long_query": {
            "query": "a" * 1001,  # Exceeds max length
            "limit": 5
        }
    }


@pytest.fixture(scope="function")
def mock_file_monitor():
    """Mock file monitor for testing"""
    from semantic_fs.file_monitor import FileMonitor
    
    mock_monitor = Mock(spec=FileMonitor)
    mock_monitor.add_watch_path.return_value = True
    mock_monitor.remove_watch_path.return_value = True
    mock_monitor.start.return_value = True
    mock_monitor.stop.return_value = None
    mock_monitor.is_running = False
    mock_monitor.get_status.return_value = {
        'is_running': False,
        'watched_paths': [],
        'path_count': 0
    }
    
    return mock_monitor


@pytest.fixture(scope="function")
def security_test_data():
    """Test data for security testing"""
    return {
        "valid_api_key": "test_api_key_123",
        "invalid_api_key": "invalid_key",
        "jwt_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test.token",
        "user_data": {
            "username": "test_user",
            "permissions": ["read", "write"]
        }
    }


# Test markers
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as API test"
    )
    config.addinivalue_line(
        "markers", "security: mark test as security test"
    )


# Test environment setup
def pytest_sessionstart(session):
    """Called after the Session object has been created"""
    # Set test environment variables
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["AUTH_ENABLED"] = "false"
    os.environ["RATE_LIMIT_ENABLED"] = "false"


def pytest_sessionfinish(session, exitstatus):
    """Called after whole test run finished"""
    # Cleanup test environment
    os.environ.pop("TESTING", None)
    os.environ.pop("LOG_LEVEL", None)
    os.environ.pop("AUTH_ENABLED", None)
    os.environ.pop("RATE_LIMIT_ENABLED", None)
