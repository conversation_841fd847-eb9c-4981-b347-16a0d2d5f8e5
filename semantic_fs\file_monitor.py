"""
File system monitoring for automatic indexing with Windows support
"""

import os
import sys
import time
import signal
import logging
import platform
import atexit
from pathlib import Path
from typing import Set, Optional, Dict, Any
from threading import Thread, Event, Lock
from contextlib import contextmanager
from watchdog.observers import Observer
from watchdog.events import FileSystemEvent<PERSON>and<PERSON>, FileModifiedEvent, FileCreatedEvent, FileDeletedEvent

from .core import SemanticFilesystem
from .database import get_db
from .models import FileRecord
from .signal_handler import register_shutdown_handler

logger = logging.getLogger(__name__)

# Windows-specific imports
if platform.system() == "Windows":
    try:
        import win32api
        import win32con
        WINDOWS_SIGNALS_AVAILABLE = True
    except ImportError:
        WINDOWS_SIGNALS_AVAILABLE = False
        logger.warning("pywin32 not available - Windows signal handling will be limited")
else:
    WINDOWS_SIGNALS_AVAILABLE = False

class SemanticFileHandler(FileSystemEventHandler):
    """File system event handler for semantic filesystem with improved Windows support"""

    def __init__(self, semantic_fs: SemanticFilesystem, debounce_seconds: float = 2.0):
        """Initialize file handler

        Args:
            semantic_fs: SemanticFilesystem instance
            debounce_seconds: Seconds to wait before processing file changes
        """
        super().__init__()
        self.semantic_fs = semantic_fs
        self.debounce_seconds = debounce_seconds
        self.pending_files: Dict[str, float] = {}
        self.pending_files_lock = Lock()  # Thread-safe access to pending files
        self.supported_extensions = {
            '.txt', '.md', '.json', '.csv', '.py', '.js', '.html', '.css',
            '.pdf', '.docx', '.xlsx', '.xml', '.yaml', '.yml', '.log'
        }

        # Thread management
        self.stop_event = Event()
        self.processor_thread = Thread(target=self._process_pending_files, daemon=False)
        self.processor_thread.name = "FileProcessor"
        self.processor_thread.start()

        # Register cleanup
        atexit.register(self.stop)

    def stop(self):
        """Stop the file handler gracefully"""
        if hasattr(self, 'stop_event') and not self.stop_event.is_set():
            logger.info("Stopping file handler...")
            self.stop_event.set()

            if hasattr(self, 'processor_thread') and self.processor_thread.is_alive():
                self.processor_thread.join(timeout=10)  # Increased timeout
                if self.processor_thread.is_alive():
                    logger.warning("File processor thread did not stop gracefully")

            logger.info("File handler stopped")
    
    def _is_supported_file(self, file_path: str) -> bool:
        """Check if file type is supported"""
        path = Path(file_path)
        
        # Skip hidden files and directories
        if any(part.startswith('.') for part in path.parts):
            return False
        
        # Skip temporary files
        if path.name.startswith('~') or path.name.endswith('.tmp'):
            return False
        
        # Check extension
        return path.suffix.lower() in self.supported_extensions
    
    def _schedule_file_processing(self, file_path: str):
        """Schedule file for processing with debouncing (thread-safe)"""
        if self._is_supported_file(file_path):
            with self.pending_files_lock:
                self.pending_files[file_path] = time.time()
            logger.debug(f"Scheduled file for processing: {file_path}")

    def _process_pending_files(self):
        """Process pending files with debouncing and improved error handling"""
        logger.info("File processor thread started")

        while not self.stop_event.is_set():
            try:
                current_time = time.time()
                files_to_process = []

                # Thread-safe access to pending files
                with self.pending_files_lock:
                    for file_path, scheduled_time in list(self.pending_files.items()):
                        if current_time - scheduled_time >= self.debounce_seconds:
                            files_to_process.append(file_path)
                            del self.pending_files[file_path]

                # Process ready files
                for file_path in files_to_process:
                    if self.stop_event.is_set():
                        break
                    self._process_file(file_path)

                # Sleep with interrupt check
                for _ in range(5):  # 0.5 seconds total, check every 0.1s
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error in file processor: {e}", exc_info=True)
                # Longer sleep on error to prevent spam
                for _ in range(10):  # 1 second total
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.1)

        logger.info("File processor thread stopped")
    
    def _process_file(self, file_path: str):
        """Process a single file with improved error handling"""
        try:
            path = Path(file_path)

            # Check if we should stop processing
            if self.stop_event.is_set():
                return

            if path.exists() and path.is_file():
                logger.info(f"Auto-indexing file: {file_path}")

                # Add timeout protection for file processing
                try:
                    result = self.semantic_fs.index_file(str(path))
                    if result:
                        logger.info(f"Successfully auto-indexed: {path.name}")
                    else:
                        logger.warning(f"Failed to auto-index: {path.name}")
                except Exception as index_error:
                    logger.error(f"Error indexing file {path.name}: {index_error}")
            else:
                # File was deleted, remove from index
                self._remove_file_from_index(file_path)

        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}", exc_info=True)

    def _remove_file_from_index(self, file_path: str):
        """Remove file from index when deleted with improved error handling"""
        try:
            # Check if we should stop processing
            if self.stop_event.is_set():
                return

            with get_db() as db:
                file_record = db.query(FileRecord).filter(
                    FileRecord.path == str(Path(file_path).resolve())  # Fixed: use 'path' not 'file_path'
                ).first()

                if file_record:
                    # Remove from vector database
                    if file_record.embedding_id:
                        try:
                            self.semantic_fs.embedding_engine.delete_embedding(file_record.embedding_id)
                        except Exception as embed_error:
                            logger.warning(f"Error removing embedding for {file_path}: {embed_error}")

                    # Remove from database
                    db.delete(file_record)
                    logger.info(f"Removed deleted file from index: {Path(file_path).name}")

        except Exception as e:
            logger.error(f"Error removing file from index {file_path}: {e}", exc_info=True)
    
    def on_created(self, event):
        """Handle file creation events"""
        if not event.is_directory:
            self._schedule_file_processing(event.src_path)
    
    def on_modified(self, event):
        """Handle file modification events"""
        if not event.is_directory:
            self._schedule_file_processing(event.src_path)
    
    def on_deleted(self, event):
        """Handle file deletion events"""
        if not event.is_directory:
            self._remove_file_from_index(event.src_path)

class FileMonitor:
    """File system monitor for automatic indexing with Windows support"""

    def __init__(self, semantic_fs: SemanticFilesystem):
        """Initialize file monitor

        Args:
            semantic_fs: SemanticFilesystem instance
        """
        self.semantic_fs = semantic_fs
        self.observer = Observer()
        self.watched_paths: Set[str] = set()
        self.handlers: Dict[str, SemanticFileHandler] = {}
        self.is_running = False
        self.shutdown_lock = Lock()

        # Register cleanup handlers
        atexit.register(self.stop)
        register_shutdown_handler(self.stop)

    @contextmanager
    def _safe_operation(self, operation_name: str):
        """Context manager for safe operations with error handling"""
        try:
            logger.debug(f"Starting {operation_name}")
            yield
            logger.debug(f"Completed {operation_name}")
        except Exception as e:
            logger.error(f"Error during {operation_name}: {e}", exc_info=True)
            raise
    
    def add_watch_path(self, path: str, recursive: bool = True) -> bool:
        """Add a path to monitor
        
        Args:
            path: Path to monitor
            recursive: Whether to monitor subdirectories
            
        Returns:
            True if path was added successfully
        """
        try:
            path = str(Path(path).resolve())
            
            if not Path(path).exists():
                logger.warning(f"Path does not exist: {path}")
                return False
            
            if path in self.watched_paths:
                logger.info(f"Path already being monitored: {path}")
                return True
            
            # Create handler for this path
            handler = SemanticFileHandler(self.semantic_fs)
            
            # Add watch
            self.observer.schedule(handler, path, recursive=recursive)
            
            # Track the watch
            self.watched_paths.add(path)
            self.handlers[path] = handler
            
            logger.info(f"Added watch path: {path} (recursive: {recursive})")
            return True
            
        except Exception as e:
            logger.error(f"Error adding watch path {path}: {e}")
            return False
    
    def remove_watch_path(self, path: str) -> bool:
        """Remove a path from monitoring
        
        Args:
            path: Path to stop monitoring
            
        Returns:
            True if path was removed successfully
        """
        try:
            path = str(Path(path).resolve())
            
            if path not in self.watched_paths:
                logger.warning(f"Path not being monitored: {path}")
                return False
            
            # Stop handler
            if path in self.handlers:
                self.handlers[path].stop()
                del self.handlers[path]
            
            # Remove from tracking
            self.watched_paths.remove(path)
            
            logger.info(f"Removed watch path: {path}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing watch path {path}: {e}")
            return False
    
    def start(self) -> bool:
        """Start file monitoring with improved error handling

        Returns:
            True if monitoring started successfully
        """
        with self.shutdown_lock:
            try:
                if self.is_running:
                    logger.warning("File monitor is already running")
                    return True

                if not self.watched_paths:
                    logger.warning("No paths to monitor")
                    return False

                with self._safe_operation("starting observer"):
                    self.observer.start()
                    self.is_running = True
                    logger.info(f"File monitor started, watching {len(self.watched_paths)} paths")
                    return True

            except Exception as e:
                logger.error(f"Error starting file monitor: {e}", exc_info=True)
                self.is_running = False
                return False

    def stop(self):
        """Stop file monitoring gracefully"""
        with self.shutdown_lock:
            try:
                if not self.is_running:
                    return

                logger.info("Stopping file monitor...")

                # Stop all handlers first
                with self._safe_operation("stopping handlers"):
                    for path, handler in list(self.handlers.items()):
                        try:
                            handler.stop()
                        except Exception as e:
                            logger.warning(f"Error stopping handler for {path}: {e}")

                # Stop observer
                with self._safe_operation("stopping observer"):
                    if self.observer.is_alive():
                        self.observer.stop()
                        self.observer.join(timeout=10)  # Increased timeout

                        if self.observer.is_alive():
                            logger.warning("Observer did not stop gracefully")

                self.is_running = False
                logger.info("File monitor stopped successfully")

            except Exception as e:
                logger.error(f"Error stopping file monitor: {e}", exc_info=True)
                self.is_running = False
    
    def get_status(self) -> Dict[str, Any]:
        """Get monitoring status
        
        Returns:
            Dictionary with monitoring status
        """
        return {
            'is_running': self.is_running,
            'watched_paths': list(self.watched_paths),
            'path_count': len(self.watched_paths)
        }
