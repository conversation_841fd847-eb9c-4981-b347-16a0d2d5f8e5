"""
USB DRIVE INDEXING TEST - Real-world test with your Samsung USB drive
This will index your actual Vaultarica project and other USB files
"""

import os
import sys
import time
import psutil
import threading
from pathlib import Path
from datetime import datetime

# Add paths for imports
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from core.desktop_engine import DesktopSemanticEngine, IndexingProgress


def find_usb_drives():
    """Find available USB drives"""
    drives = []
    
    # Check common drive letters
    for letter in 'DEFGHIJKLMNOPQRSTUVWXYZ':
        drive_path = Path(f"{letter}:\\")
        if drive_path.exists():
            try:
                # Check if it's a removable drive
                import win32file
                drive_type = win32file.GetDriveType(f"{letter}:\\")
                if drive_type == win32file.DRIVE_REMOVABLE:
                    drives.append(drive_path)
            except:
                # Fallback - just add existing drives
                drives.append(drive_path)
    
    return drives


def analyze_usb_drive(drive_path: Path):
    """Analyze USB drive contents"""
    print(f"🔍 ANALYZING USB DRIVE: {drive_path}")
    print("=" * 50)
    
    if not drive_path.exists():
        print(f"❌ Drive {drive_path} not found!")
        return None
    
    # Quick scan
    total_files = 0
    total_size = 0
    folders = []
    
    try:
        for item in drive_path.iterdir():
            if item.is_dir():
                folders.append(item.name)
            elif item.is_file():
                total_files += 1
                try:
                    total_size += item.stat().st_size
                except:
                    pass
        
        # Count files in subdirectories
        for root, dirs, files in os.walk(drive_path):
            total_files += len(files)
            for file in files:
                try:
                    file_path = Path(root) / file
                    total_size += file_path.stat().st_size
                except:
                    pass
        
        print(f"📊 USB DRIVE CONTENTS:")
        print(f"   Total files: {total_files:,}")
        print(f"   Total size: {total_size / (1024**2):.1f} MB")
        print(f"   Root folders: {', '.join(folders)}")
        
        return {
            'total_files': total_files,
            'total_size': total_size,
            'folders': folders
        }
        
    except Exception as e:
        print(f"❌ Error analyzing drive: {e}")
        return None


def test_usb_indexing():
    """Test indexing USB drive"""
    print("💾 USB DRIVE INDEXING TEST")
    print("=" * 60)
    
    # Find USB drives
    usb_drives = find_usb_drives()
    
    if not usb_drives:
        print("❌ No USB drives found!")
        print("💡 Trying manual path...")
        
        # Try common USB drive letters
        for letter in 'EFGH':
            test_path = Path(f"{letter}:\\")
            if test_path.exists():
                usb_drives.append(test_path)
                break
    
    if not usb_drives:
        print("❌ No USB drives detected. Please specify manually:")
        drive_letter = input("Enter USB drive letter (e.g., E): ").strip().upper()
        if drive_letter:
            usb_drives = [Path(f"{drive_letter}:\\")]
    
    if not usb_drives:
        print("❌ No USB drive specified")
        return False
    
    # Show available drives
    print(f"📱 Found USB drives:")
    for i, drive in enumerate(usb_drives):
        print(f"   {i+1}. {drive}")
    
    # Let user choose
    if len(usb_drives) > 1:
        choice = input(f"Choose drive (1-{len(usb_drives)}): ").strip()
        try:
            drive_index = int(choice) - 1
            selected_drive = usb_drives[drive_index]
        except:
            selected_drive = usb_drives[0]
    else:
        selected_drive = usb_drives[0]
    
    print(f"🎯 Selected drive: {selected_drive}")
    
    # Analyze drive
    analysis = analyze_usb_drive(selected_drive)
    if not analysis:
        return False
    
    # Ask for confirmation
    print(f"\n🤔 Ready to index {analysis['total_files']:,} files from {selected_drive}?")
    confirm = input("Proceed with indexing? (y/N): ").lower().strip()
    
    if confirm != 'y':
        print("👋 Indexing cancelled")
        return True
    
    # Initialize engine
    config_dir = Path.home() / ".semantic_fs_usb_test"
    config_dir.mkdir(exist_ok=True)
    
    print(f"📁 Config directory: {config_dir}")
    
    engine = DesktopSemanticEngine(config_dir)
    
    # Progress tracking
    progress_updates = []
    last_update_time = time.time()
    
    def progress_callback(progress: IndexingProgress):
        nonlocal last_update_time, progress_updates
        
        current_time = time.time()
        memory_gb = psutil.Process().memory_info().rss / (1024**3)
        
        progress_updates.append({
            'time': current_time,
            'processed': progress.processed_files,
            'total': progress.total_files,
            'percentage': progress.progress_percentage,
            'current_file': progress.current_file,
            'memory_gb': memory_gb
        })
        
        # Live counter update
        print(f"\r📊 Progress: {progress.processed_files:,}/{progress.total_files:,} "
              f"({progress.progress_percentage:.1f}%) - "
              f"Memory: {memory_gb:.2f}GB - "
              f"Current: {Path(progress.current_file).name if progress.current_file else 'N/A'}", 
              end='', flush=True)
        
        # Detailed update every 50 files
        if current_time - last_update_time >= 5 or progress.processed_files % 50 == 0:
            if progress.processed_files > 0:
                elapsed = current_time - progress.start_time
                files_per_sec = progress.processed_files / elapsed if elapsed > 0 else 0
                print(f"\n   ⚡ Speed: {files_per_sec:.1f} files/sec | "
                      f"Elapsed: {elapsed:.0f}s | "
                      f"ETA: {(progress.total_files - progress.processed_files) / files_per_sec:.0f}s")
            last_update_time = current_time
    
    # Start indexing
    print(f"\n🚀 STARTING USB DRIVE INDEXING...")
    print(f"📂 Indexing: {selected_drive}")
    print(f"📊 Files to process: {analysis['total_files']:,}")
    print("🔄 Live progress updates below:")
    print()
    
    indexing_start_time = time.time()
    indexing_started = engine.start_full_indexing([selected_drive], progress_callback)
    
    if not indexing_started:
        print("❌ Failed to start indexing")
        return False
    
    print("✅ Indexing started!")
    
    # Monitor progress
    try:
        while engine.is_indexing:
            time.sleep(0.5)
            
            # Check for high memory usage
            current_memory = psutil.Process().memory_info().rss / (1024**3)
            if current_memory > 4:  # 4GB warning
                print(f"\n⚠️  Memory usage: {current_memory:.2f}GB")
    
    except KeyboardInterrupt:
        print(f"\n🛑 Indexing interrupted by user")
        engine.stop_indexing()
        time.sleep(2)
    
    # Final results
    indexing_time = time.time() - indexing_start_time
    stats = engine.get_indexing_stats()
    
    print(f"\n\n🎉 USB DRIVE INDEXING COMPLETED!")
    print("=" * 60)
    print(f"📊 FINAL RESULTS:")
    print(f"   Files indexed: {stats['total_indexed_files']:,}")
    print(f"   Time taken: {indexing_time/60:.1f} minutes")
    print(f"   Average speed: {stats['total_indexed_files']/(indexing_time/60):.1f} files/minute")
    print(f"   Final memory usage: {stats['memory_usage_gb']:.2f} GB")
    print(f"   Success rate: {(stats['total_indexed_files']/analysis['total_files']*100):.1f}%")
    
    # Test search functionality
    if stats['total_indexed_files'] > 0:
        print(f"\n🔍 TESTING SEARCH ON REAL DATA...")
        
        test_queries = [
            "vaultarica",
            "python",
            "semantic",
            "desktop",
            "config",
            "test",
            "file",
            "code"
        ]
        
        search_results = {}
        for query in test_queries:
            try:
                results = engine.search_files(query, limit=5)
                search_results[query] = results
                print(f"   '{query}': {len(results)} results")
                if results:
                    best_match = results[0]
                    filename = Path(best_match['path']).name
                    score = best_match['relevance_score']
                    print(f"      🎯 Best: {filename} (score: {score:.3f})")
            except Exception as e:
                print(f"   '{query}': ❌ Error - {e}")
        
        # Show most relevant results
        print(f"\n🏆 TOP SEARCH RESULTS:")
        all_results = []
        for query, results in search_results.items():
            for result in results:
                all_results.append((query, result))
        
        # Sort by relevance score
        all_results.sort(key=lambda x: x[1]['relevance_score'], reverse=True)
        
        for i, (query, result) in enumerate(all_results[:10]):
            filename = Path(result['path']).name
            score = result['relevance_score']
            print(f"   {i+1}. '{query}' → {filename} (score: {score:.3f})")
    
    print(f"\n✅ USB DRIVE TEST COMPLETED SUCCESSFULLY!")
    return True


def main():
    """Main USB drive test"""
    try:
        success = test_usb_indexing()
        if success:
            print("\n🎉 TEST SUCCESSFUL!")
            print("💡 The desktop engine can handle real-world file indexing!")
        else:
            print("\n❌ TEST FAILED!")
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
