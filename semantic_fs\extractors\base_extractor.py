"""
Base Content Extractor
Abstract base class for all content extractors
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional, Dict, Set
import logging

logger = logging.getLogger(__name__)


class BaseExtractor(ABC):
    """Abstract base class for content extractors"""
    
    def __init__(self):
        """Initialize the base extractor"""
        self.supported_extensions: Set[str] = set()
        self.mime_mappings: Dict[str, str] = {}
        self.extractor_mappings: Dict[str, str] = {}
    
    @abstractmethod
    def get_supported_extensions(self) -> Set[str]:
        """Get set of supported file extensions"""
        pass
    
    @abstractmethod
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get mapping of extensions to MIME types"""
        pass
    
    @abstractmethod
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract content from file"""
        pass
    
    def supports_extension(self, extension: str) -> bool:
        """Check if extension is supported"""
        return extension.lower() in self.get_supported_extensions()
    
    def get_mime_type(self, extension: str) -> Optional[str]:
        """Get MIME type for extension"""
        return self.get_mime_mappings().get(extension.lower())
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize extracted content"""
        if not content:
            return ""
        
        # Remove excessive whitespace
        import re
        content = re.sub(r'\s+', ' ', content)
        content = content.strip()
        
        # Remove very long lines (likely binary data)
        lines = content.split('\n')
        cleaned_lines = []
        for line in lines:
            if len(line) <= 10000:  # Max line length
                cleaned_lines.append(line)
            else:
                cleaned_lines.append(line[:1000] + "... [truncated]")
        
        return '\n'.join(cleaned_lines)
    
    def _get_file_info(self, file_path: Path) -> Dict[str, str]:
        """Get basic file information"""
        try:
            stat = file_path.stat()
            return {
                'name': file_path.name,
                'extension': file_path.suffix.lower(),
                'size_mb': f"{stat.st_size / (1024*1024):.2f}",
                'size_bytes': str(stat.st_size)
            }
        except Exception as e:
            logger.debug(f"Error getting file info for {file_path}: {e}")
            return {
                'name': file_path.name,
                'extension': file_path.suffix.lower(),
                'size_mb': "0.00",
                'size_bytes': "0"
            }
    
    def _analyze_filename_context(self, file_path: Path) -> Optional[str]:
        """Analyze filename for contextual information"""
        try:
            filename = file_path.stem.lower()
            parent_dir = file_path.parent.name.lower()
            
            context_parts = []
            
            # Common naming patterns
            patterns = {
                'backup': ['backup', 'bak', 'archive', 'old', 'orig'],
                'config': ['config', 'conf', 'settings', 'prefs'],
                'temp': ['temp', 'tmp', 'cache', 'temporary'],
                'test': ['test', 'spec', 'example', 'sample', 'demo'],
                'log': ['log', 'debug', 'trace', 'error'],
                'data': ['data', 'dataset', 'export', 'dump'],
                'doc': ['readme', 'doc', 'help', 'manual', 'guide']
            }
            
            for category, keywords in patterns.items():
                if any(keyword in filename for keyword in keywords):
                    context_parts.append(f"likely {category}")
                    break
            
            # Check parent directory for context
            if parent_dir in ['docs', 'documentation', 'help']:
                context_parts.append("from documentation folder")
            elif parent_dir in ['config', 'configs', 'settings']:
                context_parts.append("from configuration folder")
            elif parent_dir in ['logs', 'log']:
                context_parts.append("from logs folder")
            elif parent_dir in ['backup', 'backups']:
                context_parts.append("from backup folder")
            
            return ", ".join(context_parts) if context_parts else None
            
        except Exception as e:
            logger.debug(f"Error analyzing filename context: {e}")
            return None
