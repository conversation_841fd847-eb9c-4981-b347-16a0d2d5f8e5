{"tests/unit/test_security.py": true, "tests/unit/test_core.py::TestSemanticFilesystem::test_index_file_success": true, "tests/unit/test_core.py::TestSemanticFilesystem::test_index_file_nonexistent": true, "tests/unit/test_core.py::TestSemanticFilesystem::test_index_file_too_large": true, "tests/unit/test_core.py::TestSemanticFilesystem::test_file_permissions": true, "tests/unit/test_core.py::TestSemanticFilesystem::test_update_existing_file": true, "tests/unit/test_core.py::TestErrorHandling::test_database_error_handling": true}