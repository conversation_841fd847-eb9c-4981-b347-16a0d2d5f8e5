"""
Archive Content Extractor
Specialized extractor for archive files with metadata and content listing
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Set
from .base_extractor import BaseExtractor

logger = logging.getLogger(__name__)


class ArchiveExtractor(BaseExtractor):
    """Specialized extractor for archive files"""
    
    def get_supported_extensions(self) -> Set[str]:
        """Get set of supported archive extensions"""
        return {
            # Common archives
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.lz4', '.zst',
            
            # Combined formats
            '.tar.gz', '.tar.bz2', '.tar.xz', '.tar.lz4', '.tar.zst',
            
            # Platform specific
            '.dmg', '.pkg', '.deb', '.rpm', '.msi', '.cab',
            
            # Development archives
            '.jar', '.war', '.ear', '.aar', '.apk', '.ipa',
            
            # Backup formats
            '.backup', '.bak', '.archive'
        }
    
    def get_mime_mappings(self) -> Dict[str, str]:
        """Get mapping of archive extensions to MIME types"""
        return {
            '.zip': 'application/zip',
            '.rar': 'application/vnd.rar',
            '.7z': 'application/x-7z-compressed',
            '.tar': 'application/x-tar',
            '.gz': 'application/gzip',
            '.bz2': 'application/x-bzip2',
            '.xz': 'application/x-xz',
            '.lz4': 'application/x-lz4',
            '.zst': 'application/zstd',
            '.tar.gz': 'application/gzip',
            '.tar.bz2': 'application/x-bzip2',
            '.tar.xz': 'application/x-xz',
            '.dmg': 'application/x-apple-diskimage',
            '.pkg': 'application/x-newton-compatible-pkg',
            '.deb': 'application/vnd.debian.binary-package',
            '.rpm': 'application/x-rpm',
            '.msi': 'application/x-msi',
            '.cab': 'application/vnd.ms-cab-compressed',
            '.jar': 'application/java-archive',
            '.war': 'application/java-archive',
            '.ear': 'application/java-archive',
            '.aar': 'application/java-archive',
            '.apk': 'application/vnd.android.package-archive',
            '.ipa': 'application/octet-stream',
            '.backup': 'application/octet-stream',
            '.bak': 'application/octet-stream',
            '.archive': 'application/octet-stream'
        }
    
    def extract_content(self, file_path: Path) -> Optional[str]:
        """Extract metadata and content listing from archive files"""
        try:
            extension = file_path.suffix.lower()
            if extension not in self.get_supported_extensions():
                return None
            
            content_parts = []
            
            # 1. Extract basic file info
            file_info = self._get_file_info(file_path)
            content_parts.append(f"Archive File: {file_info['name']} ({file_info['size_mb']} MB)")
            content_parts.append(f"Archive Type: {extension.upper()}")
            
            # 2. Extract archive contents
            archive_contents = self._extract_archive_contents(file_path, extension)
            if archive_contents:
                content_parts.append(archive_contents)
            
            # 3. Analyze archive purpose
            purpose_analysis = self._analyze_archive_purpose(file_path)
            if purpose_analysis:
                content_parts.append(f"Purpose Analysis: {purpose_analysis}")
            
            # 4. Extract filename context
            filename_context = self._analyze_filename_context(file_path)
            if filename_context:
                content_parts.append(f"Context: {filename_context}")
            
            return "\n\n".join(content_parts) if content_parts else None
            
        except Exception as e:
            logger.error(f"Error extracting archive content from {file_path}: {e}")
            return f"Archive file: {file_path.name} (extraction error)"
    
    def _extract_archive_contents(self, file_path: Path, extension: str) -> Optional[str]:
        """Extract file listing from archive"""
        try:
            content_parts = []
            
            if extension == '.zip':
                contents = self._extract_zip_contents(file_path)
            elif extension in ['.tar', '.gz', '.bz2', '.xz'] or '.tar.' in extension:
                contents = self._extract_tar_contents(file_path)
            elif extension == '.7z':
                contents = self._extract_7z_contents(file_path)
            elif extension == '.rar':
                contents = self._extract_rar_contents(file_path)
            else:
                contents = None
            
            if contents:
                file_count, sample_files, total_size = contents
                content_parts.append(f"Contains: {file_count} files")
                if total_size:
                    content_parts.append(f"Uncompressed size: {total_size / (1024*1024):.2f} MB")
                if sample_files:
                    content_parts.append(f"Sample files: {', '.join(sample_files[:10])}")
                    if len(sample_files) > 10:
                        content_parts.append(f"... and {len(sample_files) - 10} more files")
            
            return "\n".join(content_parts) if content_parts else None
            
        except Exception as e:
            logger.debug(f"Error extracting archive contents from {file_path}: {e}")
            return None
    
    def _extract_zip_contents(self, file_path: Path) -> Optional[tuple]:
        """Extract contents from ZIP file"""
        try:
            import zipfile
            with zipfile.ZipFile(file_path, 'r') as zf:
                file_list = zf.namelist()
                total_size = sum(zf.getinfo(name).file_size for name in file_list)
                return len(file_list), file_list, total_size
        except Exception as e:
            logger.debug(f"Error reading ZIP file {file_path}: {e}")
            return None
    
    def _extract_tar_contents(self, file_path: Path) -> Optional[tuple]:
        """Extract contents from TAR file"""
        try:
            import tarfile
            with tarfile.open(file_path, 'r:*') as tf:
                members = tf.getnames()
                total_size = sum(member.size for member in tf.getmembers() if member.isfile())
                return len(members), members, total_size
        except Exception as e:
            logger.debug(f"Error reading TAR file {file_path}: {e}")
            return None
    
    def _extract_7z_contents(self, file_path: Path) -> Optional[tuple]:
        """Extract contents from 7Z file"""
        try:
            # Try py7zr if available
            import py7zr
            with py7zr.SevenZipFile(file_path, mode='r') as archive:
                file_list = archive.getnames()
                return len(file_list), file_list, None
        except ImportError:
            logger.debug("py7zr not available for 7z extraction")
            return None
        except Exception as e:
            logger.debug(f"Error reading 7Z file {file_path}: {e}")
            return None
    
    def _extract_rar_contents(self, file_path: Path) -> Optional[tuple]:
        """Extract contents from RAR file"""
        try:
            # Try rarfile if available
            import rarfile
            with rarfile.RarFile(file_path) as rf:
                file_list = rf.namelist()
                total_size = sum(info.file_size for info in rf.infolist())
                return len(file_list), file_list, total_size
        except ImportError:
            logger.debug("rarfile not available for RAR extraction")
            return None
        except Exception as e:
            logger.debug(f"Error reading RAR file {file_path}: {e}")
            return None
    
    def _analyze_archive_purpose(self, file_path: Path) -> Optional[str]:
        """Analyze the likely purpose of the archive"""
        try:
            filename = file_path.stem.lower()
            extension = file_path.suffix.lower()
            
            purpose_parts = []
            
            # Analyze by extension
            if extension in ['.jar', '.war', '.ear', '.aar']:
                purpose_parts.append("Java application archive")
            elif extension == '.apk':
                purpose_parts.append("Android application package")
            elif extension == '.ipa':
                purpose_parts.append("iOS application package")
            elif extension in ['.deb', '.rpm']:
                purpose_parts.append("Linux package")
            elif extension == '.msi':
                purpose_parts.append("Windows installer package")
            elif extension == '.dmg':
                purpose_parts.append("macOS disk image")
            
            # Analyze by filename
            if any(keyword in filename for keyword in ['backup', 'bak', 'archive']):
                purpose_parts.append("backup archive")
            elif any(keyword in filename for keyword in ['source', 'src', 'code']):
                purpose_parts.append("source code archive")
            elif any(keyword in filename for keyword in ['data', 'dataset', 'export']):
                purpose_parts.append("data archive")
            elif any(keyword in filename for keyword in ['install', 'setup', 'package']):
                purpose_parts.append("installation package")
            elif any(keyword in filename for keyword in ['release', 'dist', 'build']):
                purpose_parts.append("distribution archive")
            
            return ", ".join(purpose_parts) if purpose_parts else None
            
        except Exception as e:
            logger.debug(f"Error analyzing archive purpose: {e}")
            return None
